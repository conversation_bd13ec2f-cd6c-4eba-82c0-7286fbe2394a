<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'module',
        'action',
        'is_system_permission',
    ];

    protected $casts = [
        'is_system_permission' => 'boolean',
    ];

    /**
     * الصلاحيات الافتراضية للنظام
     */
    const SYSTEM_PERMISSIONS = [
        // لوحة التحكم
        'dashboard' => [
            'view' => 'عرض لوحة التحكم',
        ],

        // العملاء
        'customers' => [
            'view' => 'عرض العملاء',
            'create' => 'إنشاء عميل جديد',
            'edit' => 'تعديل العملاء',
            'delete' => 'حذف العملاء',
            'export' => 'تصدير بيانات العملاء',
        ],

        // الموردين
        'suppliers' => [
            'view' => 'عرض الموردين',
            'create' => 'إنشاء مورد جديد',
            'edit' => 'تعديل الموردين',
            'delete' => 'حذف الموردين',
            'export' => 'تصدير بيانات الموردين',
        ],

        // المنتجات
        'products' => [
            'view' => 'عرض المنتجات',
            'create' => 'إنشاء منتج جديد',
            'edit' => 'تعديل المنتجات',
            'delete' => 'حذف المنتجات',
            'export' => 'تصدير بيانات المنتجات',
        ],

        // المخازن
        'warehouses' => [
            'view' => 'عرض المخازن',
            'create' => 'إنشاء مخزن جديد',
            'edit' => 'تعديل المخازن',
            'delete' => 'حذف المخازن',
        ],

        // حركات المخزون
        'stock_movements' => [
            'view' => 'عرض حركات المخزون',
            'create' => 'إنشاء حركة مخزون',
            'edit' => 'تعديل حركات المخزون',
            'delete' => 'حذف حركات المخزون',
        ],

        // الفواتير
        'invoices' => [
            'view' => 'عرض الفواتير',
            'create' => 'إنشاء فاتورة جديدة',
            'edit' => 'تعديل الفواتير',
            'delete' => 'حذف الفواتير',
            'confirm' => 'تأكيد الفواتير',
            'cancel' => 'إلغاء الفواتير',
            'print' => 'طباعة الفواتير',
        ],

        // فواتير البيع
        'invoices.sales' => [
            'view' => 'عرض فواتير البيع',
            'create' => 'إنشاء فاتورة بيع',
            'edit' => 'تعديل فواتير البيع',
            'delete' => 'حذف فواتير البيع',
        ],

        // فواتير الشراء
        'invoices.purchases' => [
            'view' => 'عرض فواتير الشراء',
            'create' => 'إنشاء فاتورة شراء',
            'edit' => 'تعديل فواتير الشراء',
            'delete' => 'حذف فواتير الشراء',
        ],

        // المدفوعات
        'payments' => [
            'view' => 'عرض المدفوعات',
            'create' => 'إنشاء مدفوعة جديدة',
            'edit' => 'تعديل المدفوعات',
            'delete' => 'حذف المدفوعات',
            'clear' => 'مقاصة المدفوعات',
            'cancel' => 'إلغاء المدفوعات',
            'print' => 'طباعة المدفوعات',
        ],

        // المقبوضات
        'payments.received' => [
            'view' => 'عرض المقبوضات',
            'create' => 'إنشاء مقبوضات',
            'edit' => 'تعديل المقبوضات',
            'delete' => 'حذف المقبوضات',
        ],

        // المدفوعات للموردين
        'payments.paid' => [
            'view' => 'عرض المدفوعات للموردين',
            'create' => 'إنشاء مدفوعات للموردين',
            'edit' => 'تعديل المدفوعات للموردين',
            'delete' => 'حذف المدفوعات للموردين',
        ],

        // التقارير
        'reports' => [
            'view' => 'عرض التقارير',
            'export' => 'تصدير التقارير',
            'print' => 'طباعة التقارير',
        ],

        // التقارير المالية
        'reports.financial' => [
            'view' => 'عرض التقارير المالية',
            'profit_loss' => 'قائمة الأرباح والخسائر',
            'balance_sheet' => 'الميزانية العمومية',
            'cash_flow' => 'قائمة التدفق النقدي',
        ],

        // تقارير المبيعات
        'reports.sales' => [
            'view' => 'عرض تقارير المبيعات',
            'summary' => 'ملخص المبيعات',
            'detailed' => 'تفاصيل المبيعات',
        ],

        // تقارير المخزون
        'reports.inventory' => [
            'view' => 'عرض تقارير المخزون',
            'stock_summary' => 'ملخص المخزون',
            'movements' => 'حركات المخزون',
        ],

        // تقارير المدفوعات
        'reports.payments' => [
            'view' => 'عرض تقارير المدفوعات',
            'summary' => 'ملخص المدفوعات',
            'receivables' => 'الذمم المدينة',
            'payables' => 'الذمم الدائنة',
        ],

        // دليل الحسابات
        'chart_of_accounts' => [
            'view' => 'عرض دليل الحسابات',
            'create' => 'إنشاء حساب جديد',
            'edit' => 'تعديل الحسابات',
            'delete' => 'حذف الحسابات',
        ],

        // المستخدمين
        'users' => [
            'view' => 'عرض المستخدمين',
            'create' => 'إنشاء مستخدم جديد',
            'edit' => 'تعديل المستخدمين',
            'delete' => 'حذف المستخدمين',
            'assign_roles' => 'تعيين الأدوار',
        ],

        // الأدوار والصلاحيات
        'roles' => [
            'view' => 'عرض الأدوار',
            'create' => 'إنشاء دور جديد',
            'edit' => 'تعديل الأدوار',
            'delete' => 'حذف الأدوار',
            'assign_permissions' => 'تعيين الصلاحيات',
        ],

        // الإعدادات
        'settings' => [
            'view' => 'عرض الإعدادات',
            'edit' => 'تعديل الإعدادات',
            'system' => 'إعدادات النظام',
            'company' => 'إعدادات الشركة',
        ],
    ];

    /**
     * العلاقة مع الأدوار
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'permission_role')
                    ->withTimestamps();
    }

    /**
     * Scope للصلاحيات النظام
     */
    public function scopeSystemPermissions($query)
    {
        return $query->where('is_system_permission', true);
    }

    /**
     * Scope للصلاحيات المخصصة
     */
    public function scopeCustomPermissions($query)
    {
        return $query->where('is_system_permission', false);
    }

    /**
     * Scope حسب الوحدة
     */
    public function scopeByModule($query, $module)
    {
        return $query->where('module', $module);
    }

    /**
     * إنشاء الصلاحيات الافتراضية
     */
    public static function createSystemPermissions(): void
    {
        foreach (self::SYSTEM_PERMISSIONS as $module => $actions) {
            foreach ($actions as $action => $displayName) {
                $permissionName = "{$module}.{$action}";

                self::updateOrCreate(
                    ['name' => $permissionName],
                    [
                        'display_name' => $displayName,
                        'description' => "صلاحية {$displayName} في وحدة {$module}",
                        'module' => $module,
                        'action' => $action,
                        'is_system_permission' => true,
                    ]
                );
            }
        }
    }

    /**
     * الحصول على الصلاحيات مجمعة حسب الوحدة
     */
    public static function getGroupedPermissions(): array
    {
        return self::all()
                  ->groupBy('module')
                  ->map(function($permissions) {
                      return $permissions->keyBy('action');
                  })
                  ->toArray();
    }
}
