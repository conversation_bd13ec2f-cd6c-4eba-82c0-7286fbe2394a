<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    /**
     * عرض قائمة المستخدمين
     */
    public function index(Request $request)
    {
        $query = User::with(['roles']);

        // البحث
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // التصفية حسب الدور
        if ($request->filled('role')) {
            $query->whereHas('roles', function($roleQuery) use ($request) {
                $roleQuery->where('name', $request->role);
            });
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $active = $request->status === 'active';
            $query->where('is_active', $active);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);
        $roles = Role::active()->get(['id', 'name', 'display_name']);

        return view('tenant.users.index', compact('users', 'roles'));
    }

    /**
     * عرض نموذج إنشاء مستخدم جديد
     */
    public function create()
    {
        $roles = Role::active()->get(['id', 'name', 'display_name']);

        return view('tenant.users.create', compact('roles'));
    }

    /**
     * حفظ مستخدم جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'roles' => ['required', 'array', 'min:1'],
            'roles.*' => ['exists:roles,name'],
            'is_active' => ['boolean'],
        ], [
            'name.required' => 'الاسم مطلوب',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.unique' => 'البريد الإلكتروني مستخدم من قبل',
            'password.required' => 'كلمة المرور مطلوبة',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق',
            'roles.required' => 'يجب تحديد دور واحد على الأقل',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_active' => $request->boolean('is_active', true),
        ]);

        // تعيين الأدوار
        $user->syncRoles($request->roles);

        return redirect()->route('tenant.users.index')
            ->with('success', 'تم إنشاء المستخدم بنجاح');
    }

    /**
     * عرض تفاصيل المستخدم
     */
    public function show(User $user)
    {
        $user->load(['roles']);

        return view('tenant.users.show', compact('user'));
    }

    /**
     * عرض نموذج تعديل المستخدم
     */
    public function edit(User $user)
    {
        $user->load(['roles']);
        $roles = Role::active()->get(['id', 'name', 'display_name']);
        $userRoles = $user->roles->pluck('name')->toArray();

        return view('tenant.users.edit', compact('user', 'roles', 'userRoles'));
    }

    /**
     * تحديث المستخدم
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'roles' => ['required', 'array', 'min:1'],
            'roles.*' => ['exists:roles,name'],
            'is_active' => ['boolean'],
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'is_active' => $request->boolean('is_active', true),
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        // تحديث الأدوار
        $user->syncRoles($request->roles);

        return redirect()->route('tenant.users.index')
            ->with('success', 'تم تحديث المستخدم بنجاح');
    }

    /**
     * حذف المستخدم
     */
    public function destroy(User $user)
    {
        // منع حذف المستخدم الحالي
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'لا يمكنك حذف حسابك الخاص');
        }

        // منع حذف مدير النظام الوحيد
        if ($user->isSuperAdmin() && User::whereHas('roles', function($query) {
            $query->where('name', 'super_admin');
        })->count() === 1) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف مدير النظام الوحيد');
        }

        $user->roles()->detach();
        $user->delete();

        return redirect()->route('tenant.users.index')
            ->with('success', 'تم حذف المستخدم بنجاح');
    }

    /**
     * تعيين أدوار للمستخدم
     */
    public function assignRoles(Request $request, User $user)
    {
        $request->validate([
            'roles' => ['required', 'array', 'min:1'],
            'roles.*' => ['exists:roles,name'],
        ]);

        $user->syncRoles($request->roles);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث أدوار المستخدم بنجاح',
            'roles' => $user->fresh()->roleDisplayNames,
        ]);
    }

    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleStatus(User $user)
    {
        // منع إلغاء تفعيل المستخدم الحالي
        if ($user->id === auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكنك إلغاء تفعيل حسابك الخاص',
            ], 400);
        }

        $user->update(['is_active' => !$user->is_active]);

        return response()->json([
            'success' => true,
            'message' => $user->is_active ? 'تم تفعيل المستخدم' : 'تم إلغاء تفعيل المستخدم',
            'is_active' => $user->is_active,
        ]);
    }

    /**
     * إعادة تعيين كلمة المرور
     */
    public function resetPassword(Request $request, User $user)
    {
        $request->validate([
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إعادة تعيين كلمة المرور بنجاح',
        ]);
    }
}
