<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TaxSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // ملاحظة: هذه الضرائب ستُضاف لكل tenant عند إنشائه
        // هنا نضع الضرائب الافتراضية للمملكة العربية السعودية
        $defaultTaxes = [
            [
                'tenant_id' => 1, // سيتم تحديثه لكل tenant
                'tax_code' => 'VAT15',
                'name' => 'ضريبة القيمة المضافة',
                'name_en' => 'Value Added Tax',
                'type' => 'percentage',
                'rate' => 15.00,
                'amount' => null,
                'is_inclusive' => false,
                'is_default' => true,
                'is_active' => true,
                'description' => 'ضريبة القيمة المضافة 15% حسب نظام المملكة العربية السعودية',
            ],
            [
                'tenant_id' => 1,
                'tax_code' => 'EXEMPT',
                'name' => 'معفى من الضريبة',
                'name_en' => 'Tax Exempt',
                'type' => 'percentage',
                'rate' => 0.00,
                'amount' => null,
                'is_inclusive' => false,
                'is_default' => false,
                'is_active' => true,
                'description' => 'معفى من ضريبة القيمة المضافة',
            ],
        ];

        // سيتم استخدام هذا في seeder منفصل لكل tenant
        // أو في factory عند إنشاء tenant جديد
    }
}
