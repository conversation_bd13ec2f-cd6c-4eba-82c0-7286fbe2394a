<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Tenant;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Resolvers\DomainTenantResolver;
use Stancl\Tenancy\Tenancy;

class InitializeTenancyBySubdomain
{
    protected $tenancy;
    protected $resolver;

    public function __construct(Tenancy $tenancy, DomainTenantResolver $resolver)
    {
        $this->tenancy = $tenancy;
        $this->resolver = $resolver;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $host = $request->getHost();

        // التحقق من النطاق الفرعي
        if ($this->isSubdomain($host)) {
            $subdomain = $this->extractSubdomain($host);

            // البحث عن الـ tenant بالنطاق الفرعي
            $tenant = Tenant::where('subdomain', $subdomain)
                           ->where('status', 'active')
                           ->first();

            if ($tenant) {
                // تهيئة الـ tenancy
                $this->tenancy->initialize($tenant);
            } else {
                // إذا لم يتم العثور على الـ tenant
                abort(404, 'الشركة غير موجودة أو غير نشطة');
            }
        }

        return $next($request);
    }

    /**
     * التحقق من وجود نطاق فرعي
     */
    protected function isSubdomain(string $host): bool
    {
        $centralDomains = config('tenancy.central_domains', []);

        foreach ($centralDomains as $centralDomain) {
            if ($host !== $centralDomain && str_ends_with($host, '.' . $centralDomain)) {
                return true;
            }
        }

        return false;
    }

    /**
     * استخراج النطاق الفرعي
     */
    protected function extractSubdomain(string $host): string
    {
        $centralDomains = config('tenancy.central_domains', []);

        foreach ($centralDomains as $centralDomain) {
            if (str_ends_with($host, '.' . $centralDomain)) {
                return str_replace('.' . $centralDomain, '', $host);
            }
        }

        return '';
    }
}
