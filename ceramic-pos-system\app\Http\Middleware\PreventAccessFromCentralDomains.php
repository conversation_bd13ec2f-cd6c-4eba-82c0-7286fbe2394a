<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PreventAccessFromCentralDomains
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $host = $request->getHost();
        $centralDomains = config('tenancy.central_domains', []);

        // منع الوصول للصفحات الخاصة بالـ tenants من النطاقات المركزية
        if (in_array($host, $centralDomains)) {
            abort(404, 'هذه الصفحة غير متاحة من النطاق المركزي');
        }

        return $next($request);
    }
}
