<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SimpleTenant;
use App\Models\User;
use App\Models\PlanFeature;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class SuperAdminController extends Controller
{
    /**
     * لوحة تحكم المدير العام
     */
    public function dashboard()
    {
        $stats = [
            'total_tenants' => SimpleTenant::count(),
            'active_tenants' => SimpleTenant::where('status', 'active')->count(),
            'trial_tenants' => SimpleTenant::where('status', 'trial')->count(),
            'expired_tenants' => SimpleTenant::where('status', 'expired')->count(),
            'total_revenue' => SimpleTenant::where('status', 'active')->sum('monthly_price'),
            'new_tenants_this_month' => SimpleTenant::whereMonth('created_at', now()->month)->count(),
        ];

        $recent_tenants = SimpleTenant::latest()->take(10)->get();
        $expiring_trials = SimpleTenant::where('status', 'trial')
            ->where('trial_ends_at', '<=', now()->addDays(7))
            ->get();

        return view('super-admin.dashboard', compact('stats', 'recent_tenants', 'expiring_trials'));
    }

    /**
     * إدارة المشتركين
     */
    public function tenants(Request $request)
    {
        $query = SimpleTenant::query();

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('company_name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('subdomain', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('plan')) {
            $query->where('plan', $request->plan);
        }

        $tenants = $query->latest()->paginate(20);

        return view('super-admin.tenants.index', compact('tenants'));
    }

    /**
     * عرض تفاصيل مشترك
     */
    public function showTenant($id)
    {
        $tenant = SimpleTenant::findOrFail($id);
        $users = User::where('tenant_id', $id)->get();
        
        return view('super-admin.tenants.show', compact('tenant', 'users'));
    }

    /**
     * تحديث حالة المشترك
     */
    public function updateTenantStatus(Request $request, $id)
    {
        $tenant = SimpleTenant::findOrFail($id);
        
        $request->validate([
            'status' => 'required|in:trial,active,suspended,expired',
            'subscription_ends_at' => 'nullable|date',
        ]);

        $tenant->update([
            'status' => $request->status,
            'subscription_ends_at' => $request->subscription_ends_at,
        ]);

        return back()->with('success', 'تم تحديث حالة المشترك بنجاح');
    }

    /**
     * حذف مشترك
     */
    public function deleteTenant($id)
    {
        $tenant = SimpleTenant::findOrFail($id);
        
        // حذف جميع المستخدمين المرتبطين
        User::where('tenant_id', $id)->delete();
        
        // حذف المشترك
        $tenant->delete();

        return redirect()->route('super-admin.tenants')->with('success', 'تم حذف المشترك بنجاح');
    }

    /**
     * إدارة خطط الأسعار
     */
    public function plans()
    {
        $plans = PlanFeature::orderBy('sort_order')->get();
        $availableModules = PlanFeature::getAvailableModules();
        $availableFeatures = PlanFeature::getAvailableFeatures();

        return view('super-admin.plans', compact('plans', 'availableModules', 'availableFeatures'));
    }

    /**
     * تحديث خطة
     */
    public function updatePlan(Request $request, $id)
    {
        $plan = PlanFeature::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'max_users' => 'nullable|integer|min:1',
            'max_products' => 'nullable|integer|min:1',
            'max_customers' => 'nullable|integer|min:1',
            'max_invoices_per_month' => 'nullable|integer|min:1',
            'modules' => 'array',
            'features' => 'array',
        ]);

        $plan->update([
            'name' => $request->name,
            'description' => $request->description,
            'price' => $request->price,
            'max_users' => $request->max_users,
            'max_products' => $request->max_products,
            'max_customers' => $request->max_customers,
            'max_invoices_per_month' => $request->max_invoices_per_month,
            'modules' => $request->modules ?? [],
            'features' => $request->features ?? [],
            'is_active' => $request->has('is_active'),
            'is_featured' => $request->has('is_featured'),
        ]);

        return back()->with('success', 'تم تحديث الخطة بنجاح');
    }

    /**
     * إعدادات النظام العامة
     */
    public function settings()
    {
        return view('super-admin.settings');
    }

    /**
     * تحديث إعدادات النظام
     */
    public function updateSettings(Request $request)
    {
        // هنا يمكن حفظ الإعدادات في جدول settings أو ملف config
        return back()->with('success', 'تم تحديث الإعدادات بنجاح');
    }

    /**
     * إحصائيات مالية
     */
    public function financialReports()
    {
        $monthly_revenue = SimpleTenant::selectRaw('MONTH(created_at) as month, SUM(monthly_price) as revenue')
            ->where('status', 'active')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->get();

        $plan_distribution = SimpleTenant::selectRaw('plan, COUNT(*) as count')
            ->groupBy('plan')
            ->get();

        return view('super-admin.financial-reports', compact('monthly_revenue', 'plan_distribution'));
    }

    /**
     * إنشاء مشترك جديد (من المدير)
     */
    public function createTenant()
    {
        return view('super-admin.tenants.create');
    }

    /**
     * حفظ مشترك جديد
     */
    public function storeTenant(Request $request)
    {
        $request->validate([
            'company_name' => 'required|string|max:255',
            'subdomain' => 'required|string|max:50|unique:simple_tenants,subdomain|alpha_dash',
            'owner_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'required|string|max:20',
            'city' => 'required|string|max:100',
            'plan' => 'required|in:basic,premium,enterprise',
            'status' => 'required|in:trial,active,suspended',
            'password' => 'required|string|min:8',
        ]);

        DB::beginTransaction();
        try {
            // إنشاء المشترك
            $tenant = SimpleTenant::create([
                'company_name' => $request->company_name,
                'subdomain' => $request->subdomain,
                'owner_name' => $request->owner_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'city' => $request->city,
                'plan' => $request->plan,
                'status' => $request->status,
                'trial_ends_at' => $request->status === 'trial' ? now()->addDays(30) : null,
                'subscription_ends_at' => $request->status === 'active' ? now()->addMonth() : null,
                'monthly_price' => $this->getPlanPrice($request->plan),
            ]);

            // إنشاء المستخدم المالك
            User::create([
                'name' => $request->owner_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'tenant_id' => $tenant->id,
                'is_owner' => true,
                'is_active' => true,
            ]);

            DB::commit();
            return redirect()->route('super-admin.tenants')->with('success', 'تم إنشاء المشترك بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'حدث خطأ أثناء إنشاء المشترك'])->withInput();
        }
    }

    /**
     * الحصول على سعر الخطة
     */
    private function getPlanPrice($plan)
    {
        $prices = [
            'basic' => 299,
            'premium' => 599,
            'enterprise' => 999,
        ];

        return $prices[$plan] ?? 599;
    }
}
