<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Warehouse;
use App\Models\Product;
use App\Models\ProductStock;
use Illuminate\Support\Facades\DB;

class WarehouseController extends Controller
{
    /**
     * عرض قائمة المخازن
     */
    public function index(Request $request)
    {
        $query = Warehouse::withCount(['productStock']);

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // التصفية حسب الحالة
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'warehouse_code');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $warehouses = $query->paginate(20);

        // إحصائيات
        $stats = [
            'total_warehouses' => Warehouse::count(),
            'active_warehouses' => Warehouse::active()->count(),
            'main_warehouse' => Warehouse::main()->first(),
            'total_stock_value' => ProductStock::get()->sum('stock_value'),
            'low_stock_products' => ProductStock::lowStock()->count(),
            'out_of_stock_products' => ProductStock::outOfStock()->count(),
        ];

        return view('tenant.warehouses.index', compact('warehouses', 'stats'));
    }

    /**
     * عرض نموذج إنشاء مخزن جديد
     */
    public function create()
    {
        return view('tenant.warehouses.create');
    }

    /**
     * حفظ مخزن جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'warehouse_code' => 'required|string|max:20|unique:warehouses,warehouse_code',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:20',
            'is_main' => 'boolean',
        ], [
            'warehouse_code.required' => 'رقم المخزن مطلوب',
            'warehouse_code.unique' => 'رقم المخزن مستخدم من قبل',
            'name.required' => 'اسم المخزن مطلوب',
        ]);

        DB::beginTransaction();

        try {
            // إذا كان هذا المخزن الرئيسي، قم بإلغاء تعيين المخازن الأخرى كرئيسية
            if ($request->boolean('is_main')) {
                Warehouse::where('is_main', true)->update(['is_main' => false]);
            }

            $warehouse = Warehouse::create([
                'warehouse_code' => $request->warehouse_code,
                'name' => $request->name,
                'description' => $request->description,
                'address' => $request->address,
                'city' => $request->city,
                'phone' => $request->phone,
                'manager_name' => $request->manager_name,
                'manager_phone' => $request->manager_phone,
                'is_main' => $request->boolean('is_main'),
                'is_active' => true,
            ]);

            // إنشاء مخزون أولي لجميع المنتجات النشطة
            $products = Product::active()->get();
            foreach ($products as $product) {
                ProductStock::create([
                    'product_id' => $product->id,
                    'warehouse_id' => $warehouse->id,
                    'quantity' => 0,
                    'reserved_quantity' => 0,
                    'available_quantity' => 0,
                    'average_cost' => $product->purchase_price,
                ]);
            }

            DB::commit();

            return redirect()->route('tenant.warehouses.index')
                ->with('success', 'تم إنشاء المخزن بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المخزن: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل المخزن
     */
    public function show(Warehouse $warehouse)
    {
        $warehouse->load(['productStock.product']);

        // إحصائيات المخزن
        $stats = [
            'total_products' => $warehouse->products_count,
            'total_stock_value' => $warehouse->total_stock_value,
            'low_stock_products' => $warehouse->low_stock_products_count,
            'out_of_stock_products' => $warehouse->out_of_stock_products_count,
        ];

        // المنتجات في المخزن
        $products = $warehouse->getProducts()->paginate(20);

        return view('tenant.warehouses.show', compact('warehouse', 'stats', 'products'));
    }

    /**
     * عرض نموذج تعديل المخزن
     */
    public function edit(Warehouse $warehouse)
    {
        return view('tenant.warehouses.edit', compact('warehouse'));
    }

    /**
     * تحديث المخزن
     */
    public function update(Request $request, Warehouse $warehouse)
    {
        $request->validate([
            'warehouse_code' => 'required|string|max:20|unique:warehouses,warehouse_code,' . $warehouse->id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:20',
            'is_main' => 'boolean',
            'is_active' => 'boolean',
        ]);

        DB::beginTransaction();

        try {
            // إذا كان هذا المخزن الرئيسي، قم بإلغاء تعيين المخازن الأخرى كرئيسية
            if ($request->boolean('is_main') && !$warehouse->is_main) {
                Warehouse::where('is_main', true)->update(['is_main' => false]);
            }

            $warehouse->update([
                'warehouse_code' => $request->warehouse_code,
                'name' => $request->name,
                'description' => $request->description,
                'address' => $request->address,
                'city' => $request->city,
                'phone' => $request->phone,
                'manager_name' => $request->manager_name,
                'manager_phone' => $request->manager_phone,
                'is_main' => $request->boolean('is_main'),
                'is_active' => $request->boolean('is_active', true),
            ]);

            DB::commit();

            return redirect()->route('tenant.warehouses.index')
                ->with('success', 'تم تحديث المخزن بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث المخزن: ' . $e->getMessage());
        }
    }

    /**
     * حذف المخزن
     */
    public function destroy(Warehouse $warehouse)
    {
        // التحقق من عدم وجود مخزون
        if ($warehouse->productStock()->where('quantity', '>', 0)->count() > 0) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف مخزن يحتوي على مخزون');
        }

        // التحقق من عدم كونه المخزن الرئيسي
        if ($warehouse->is_main) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف المخزن الرئيسي');
        }

        try {
            // حذف مخزون المنتجات
            $warehouse->productStock()->delete();

            // حذف المخزن
            $warehouse->delete();

            return redirect()->route('tenant.warehouses.index')
                ->with('success', 'تم حذف المخزن بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف المخزن: ' . $e->getMessage());
        }
    }

    /**
     * تفعيل/إلغاء تفعيل المخزن
     */
    public function toggleStatus(Warehouse $warehouse)
    {
        $warehouse->update(['is_active' => !$warehouse->is_active]);

        return response()->json([
            'success' => true,
            'message' => $warehouse->is_active ? 'تم تفعيل المخزن' : 'تم إلغاء تفعيل المخزن',
            'is_active' => $warehouse->is_active
        ]);
    }

    /**
     * تقرير المخزن
     */
    public function report(Warehouse $warehouse, Request $request)
    {
        $query = $warehouse->productStock()->with('product');

        // التصفية حسب حالة المخزون
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low_stock':
                    $query->lowStock();
                    break;
                case 'out_of_stock':
                    $query->outOfStock();
                    break;
                case 'available':
                    $query->available();
                    break;
            }
        }

        $stock = $query->orderBy('quantity', 'desc')->get();

        return view('tenant.warehouses.report', compact('warehouse', 'stock'));
    }

    /**
     * نقل المخزون بين المخازن
     */
    public function transfer(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'from_warehouse_id' => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ], [
            'product_id.required' => 'المنتج مطلوب',
            'from_warehouse_id.required' => 'المخزن المصدر مطلوب',
            'to_warehouse_id.required' => 'المخزن المستهدف مطلوب',
            'to_warehouse_id.different' => 'المخزن المستهدف يجب أن يكون مختلف عن المصدر',
            'quantity.required' => 'الكمية مطلوبة',
            'quantity.min' => 'الكمية يجب أن تكون أكبر من صفر',
        ]);

        DB::beginTransaction();

        try {
            $fromWarehouse = Warehouse::findOrFail($request->from_warehouse_id);
            $toWarehouse = Warehouse::findOrFail($request->to_warehouse_id);
            $product = Product::findOrFail($request->product_id);

            // التحقق من توفر الكمية
            if (!$fromWarehouse->hasProductQuantity($request->product_id, $request->quantity)) {
                return response()->json([
                    'success' => false,
                    'message' => 'الكمية المطلوبة غير متوفرة في المخزن المصدر'
                ], 400);
            }

            // تنفيذ التحويل
            $success = $fromWarehouse->transferStockTo(
                $toWarehouse,
                $request->product_id,
                $request->quantity
            );

            if (!$success) {
                throw new \Exception('فشل في تحويل المخزون');
            }

            // تسجيل حركة التحويل
            \App\Models\StockMovement::createTransferMovement(
                $request->product_id,
                $request->from_warehouse_id,
                $request->to_warehouse_id,
                $request->quantity,
                $product->average_cost,
                'TRANSFER-' . time(),
                $request->notes
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحويل المخزون بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحويل المخزون: ' . $e->getMessage()
            ], 500);
        }
    }
}
