<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_stock', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('warehouse_id')->constrained('warehouses')->onDelete('cascade');
            $table->integer('quantity')->default(0); // الكمية المتاحة
            $table->integer('reserved_quantity')->default(0); // الكمية المحجوزة
            $table->integer('available_quantity')->default(0); // الكمية المتاحة للبيع
            $table->decimal('average_cost', 10, 2)->default(0); // متوسط التكلفة
            $table->date('last_purchase_date')->nullable(); // تاريخ آخر شراء
            $table->date('last_sale_date')->nullable(); // تاريخ آخر بيع
            $table->timestamps();

            $table->unique(['product_id', 'warehouse_id']);
            $table->index(['tenant_id', 'product_id']);
            $table->index(['tenant_id', 'warehouse_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_stock');
    }
};
