<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ChartOfAccount>
 */
class ChartOfAccountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $accountTypes = array_keys(\App\Models\ChartOfAccount::ACCOUNT_TYPES);
        $accountCategories = array_keys(\App\Models\ChartOfAccount::ACCOUNT_CATEGORIES);
        $natures = ['debit', 'credit'];

        return [
            'tenant_id' => 1, // سيتم تحديثه عند الاستخدام
            'account_code' => $this->faker->unique()->numerify('####'),
            'account_name' => $this->faker->words(3, true),
            'account_name_en' => $this->faker->words(3, true),
            'account_type' => $this->faker->randomElement($accountTypes),
            'account_category' => $this->faker->randomElement($accountCategories),
            'parent_id' => null,
            'level' => 1,
            'path' => null,
            'nature' => $this->faker->randomElement($natures),
            'opening_balance' => $this->faker->randomFloat(2, 0, 10000),
            'current_balance' => $this->faker->randomFloat(2, 0, 10000),
            'is_active' => true,
            'is_system' => false,
            'description' => $this->faker->sentence(),
        ];
    }

    /**
     * حساب نشط
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    /**
     * حساب غير نشط
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * حساب نظام
     */
    public function system()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_system' => true,
            ];
        });
    }

    /**
     * حساب أصول
     */
    public function assets()
    {
        return $this->state(function (array $attributes) {
            return [
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'nature' => 'debit',
            ];
        });
    }

    /**
     * حساب خصوم
     */
    public function liabilities()
    {
        return $this->state(function (array $attributes) {
            return [
                'account_type' => 'liabilities',
                'account_category' => 'current_liabilities',
                'nature' => 'credit',
            ];
        });
    }

    /**
     * حساب إيرادات
     */
    public function revenue()
    {
        return $this->state(function (array $attributes) {
            return [
                'account_type' => 'revenue',
                'account_category' => 'sales_revenue',
                'nature' => 'credit',
            ];
        });
    }

    /**
     * حساب مصروفات
     */
    public function expenses()
    {
        return $this->state(function (array $attributes) {
            return [
                'account_type' => 'expenses',
                'account_category' => 'operating_expenses',
                'nature' => 'debit',
            ];
        });
    }
}
