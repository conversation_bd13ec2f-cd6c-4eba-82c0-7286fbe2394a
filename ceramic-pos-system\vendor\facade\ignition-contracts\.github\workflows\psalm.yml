name: Psalm

on:
    push:
        paths:
            - '**.php'
            - 'psalm.xml'

jobs:
    psalm:
        name: psalm
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: '7.4'
                  extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick
                  coverage: none

            - name: Cache composer dependencies
              uses: actions/cache@v1
              with:
                  path: vendor
                  key: composer-${{ hashFiles('composer.lock') }}

            - name: Run composer require
              run: composer require -n --prefer-dist

            - name: Run psalm
              run: ./vendor/bin/psalm -c psalm.xml
