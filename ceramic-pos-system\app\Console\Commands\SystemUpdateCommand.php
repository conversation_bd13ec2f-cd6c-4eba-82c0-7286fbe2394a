<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Permission;
use App\Models\Role;
use App\Models\Setting;
use App\Models\Tenant;
use Illuminate\Support\Facades\Artisan;

class SystemUpdateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:update {--force : Force update without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تحديث النظام وإنشاء البيانات الافتراضية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 بدء تحديث النظام...');

        if (!$this->option('force') && !$this->confirm('هل تريد المتابعة مع تحديث النظام؟')) {
            $this->info('تم إلغاء التحديث.');
            return 0;
        }

        try {
            // 1. تشغيل Migrations
            $this->updateMigrations();

            // 2. إنشاء الصلاحيات والأدوار
            $this->updatePermissionsAndRoles();

            // 3. إنشاء الإعدادات الافتراضية
            $this->updateSettings();

            // 4. تحديث إحصائيات لوحة التحكم
            $this->updateDashboardStats();

            // 5. تنظيف الكاش
            $this->clearCache();

            // 6. تحسين النظام
            $this->optimizeSystem();

            $this->info('✅ تم تحديث النظام بنجاح!');

        } catch (\Exception $e) {
            $this->error('❌ فشل في تحديث النظام: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * تشغيل Migrations
     */
    private function updateMigrations()
    {
        $this->info('📦 تشغيل Migrations...');

        Artisan::call('migrate', ['--force' => true]);
        $this->line(Artisan::output());
    }

    /**
     * تحديث الصلاحيات والأدوار
     */
    private function updatePermissionsAndRoles()
    {
        $this->info('🔐 تحديث الصلاحيات والأدوار...');

        // إنشاء الصلاحيات الافتراضية
        Permission::createSystemPermissions();
        $this->line('✓ تم إنشاء الصلاحيات الافتراضية');

        // إنشاء الأدوار الافتراضية لجميع الشركات
        $tenants = Tenant::all();
        $progressBar = $this->output->createProgressBar($tenants->count());
        $progressBar->start();

        foreach ($tenants as $tenant) {
            Role::createSystemRoles($tenant->id);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->line('');
        $this->line('✓ تم إنشاء الأدوار الافتراضية لجميع الشركات');
    }

    /**
     * تحديث الإعدادات الافتراضية
     */
    private function updateSettings()
    {
        $this->info('⚙️ تحديث الإعدادات الافتراضية...');

        $tenants = Tenant::all();
        $progressBar = $this->output->createProgressBar($tenants->count());
        $progressBar->start();

        foreach ($tenants as $tenant) {
            Setting::createDefaultSettings($tenant->id);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->line('');
        $this->line('✓ تم تحديث الإعدادات الافتراضية لجميع الشركات');
    }

    /**
     * تحديث إحصائيات لوحة التحكم
     */
    private function updateDashboardStats()
    {
        $this->info('📊 تحديث إحصائيات لوحة التحكم...');

        Artisan::call('dashboard:update-stats');
        $this->line(Artisan::output());
    }

    /**
     * تنظيف الكاش
     */
    private function clearCache()
    {
        $this->info('🧹 تنظيف الكاش...');

        $commands = [
            'cache:clear',
            'config:clear',
            'route:clear',
            'view:clear',
        ];

        foreach ($commands as $command) {
            Artisan::call($command);
            $this->line("✓ تم تشغيل {$command}");
        }
    }

    /**
     * تحسين النظام
     */
    private function optimizeSystem()
    {
        $this->info('⚡ تحسين النظام...');

        $commands = [
            'config:cache',
            'route:cache',
            'view:cache',
        ];

        foreach ($commands as $command) {
            Artisan::call($command);
            $this->line("✓ تم تشغيل {$command}");
        }
    }
}
