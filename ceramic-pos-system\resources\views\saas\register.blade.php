<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إنشاء حساب جديد - Ceramic POS</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        
        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
        }
        
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .register-body {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-weight: 600;
            font-size: 16px;
            width: 100%;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .plan-badge {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .trial-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1>
                <i class="fas fa-rocket me-2"></i>
                ابدأ تجربتك المجانية
            </h1>
            <p>30 يوماً مجاناً - بدون بطاقة ائتمان</p>
            
            @if(request('plan'))
                <div class="plan-badge">
                    الخطة المختارة: 
                    @switch(request('plan'))
                        @case('basic')
                            الأساسية (299 ج.م/شهر)
                            @break
                        @case('premium')
                            المتقدمة (599 ج.م/شهر)
                            @break
                        @case('enterprise')
                            المؤسسية (999 ج.م/شهر)
                            @break
                        @default
                            المتقدمة (599 ج.م/شهر)
                    @endswitch
                </div>
            @endif
        </div>
        
        <div class="register-body">
            <div class="trial-info">
                <h6 class="fw-bold text-primary">
                    <i class="fas fa-gift me-2"></i>
                    ما تحصل عليه في التجربة المجانية:
                </h6>
                <ul class="mb-0">
                    <li>وصول كامل لجميع المميزات</li>
                    <li>دعم فني مجاني</li>
                    <li>إعداد مجاني للنظام</li>
                    <li>تدريب أساسي على النظام</li>
                </ul>
            </div>
            
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <form method="POST" action="{{ route('saas.register.store') }}">
                @csrf
                
                <input type="hidden" name="plan" value="{{ request('plan', 'premium') }}">
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="company_name" class="form-label">
                                <i class="fas fa-building me-1"></i>
                                اسم الشركة *
                            </label>
                            <input type="text" 
                                   class="form-control @error('company_name') is-invalid @enderror" 
                                   id="company_name" 
                                   name="company_name" 
                                   value="{{ old('company_name') }}" 
                                   required 
                                   placeholder="مثال: شركة السيراميك الحديث">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="subdomain" class="form-label">
                                <i class="fas fa-link me-1"></i>
                                النطاق الفرعي *
                            </label>
                            <div class="input-group">
                                <input type="text" 
                                       class="form-control @error('subdomain') is-invalid @enderror" 
                                       id="subdomain" 
                                       name="subdomain" 
                                       value="{{ old('subdomain') }}" 
                                       required 
                                       placeholder="mycompany">
                                <span class="input-group-text">.ceramicpos.com</span>
                            </div>
                            <small class="text-muted">سيكون رابط شركتك: https://mycompany.ceramicpos.com</small>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="owner_name" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                اسم المدير *
                            </label>
                            <input type="text" 
                                   class="form-control @error('owner_name') is-invalid @enderror" 
                                   id="owner_name" 
                                   name="owner_name" 
                                   value="{{ old('owner_name') }}" 
                                   required 
                                   placeholder="أحمد محمد">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>
                                البريد الإلكتروني *
                            </label>
                            <input type="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}" 
                                   required 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                رقم الهاتف *
                            </label>
                            <input type="tel" 
                                   class="form-control @error('phone') is-invalid @enderror" 
                                   id="phone" 
                                   name="phone" 
                                   value="{{ old('phone') }}" 
                                   required 
                                   placeholder="01234567890">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="city" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                المدينة *
                            </label>
                            <select class="form-control @error('city') is-invalid @enderror" 
                                    id="city" 
                                    name="city" 
                                    required>
                                <option value="">اختر المدينة</option>
                                <option value="القاهرة" {{ old('city') == 'القاهرة' ? 'selected' : '' }}>القاهرة</option>
                                <option value="الإسكندرية" {{ old('city') == 'الإسكندرية' ? 'selected' : '' }}>الإسكندرية</option>
                                <option value="الجيزة" {{ old('city') == 'الجيزة' ? 'selected' : '' }}>الجيزة</option>
                                <option value="أخرى" {{ old('city') == 'أخرى' ? 'selected' : '' }}>أخرى</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                كلمة المرور *
                            </label>
                            <input type="password" 
                                   class="form-control @error('password') is-invalid @enderror" 
                                   id="password" 
                                   name="password" 
                                   required
                                   placeholder="كلمة مرور قوية">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password_confirmation" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                تأكيد كلمة المرور *
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="password_confirmation" 
                                   name="password_confirmation" 
                                   required
                                   placeholder="أعد كتابة كلمة المرور">
                        </div>
                    </div>
                </div>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">
                        أوافق على <a href="#" class="text-primary">شروط الاستخدام</a> و <a href="#" class="text-primary">سياسة الخصوصية</a>
                    </label>
                </div>
                
                <button type="submit" class="btn btn-register">
                    <i class="fas fa-rocket me-2"></i>
                    ابدأ تجربتي المجانية الآن
                </button>
            </form>
            
            <div class="text-center mt-3">
                <p class="text-muted">
                    لديك حساب بالفعل؟ 
                    <a href="{{ route('login') }}" class="text-primary fw-bold">سجل دخولك</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحويل اسم الشركة إلى subdomain تلقائياً
        document.getElementById('company_name').addEventListener('input', function() {
            const companyName = this.value;
            const subdomain = companyName
                .toLowerCase()
                .replace(/[^a-z0-9\u0600-\u06FF]/g, '') // إزالة الرموز
                .replace(/[\u0600-\u06FF]/g, '') // إزالة العربية
                .substring(0, 20); // تحديد الطول
            
            if (subdomain) {
                document.getElementById('subdomain').value = subdomain;
            }
        });
    </script>
</body>
</html>
