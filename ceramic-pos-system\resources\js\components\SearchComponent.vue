<template>
  <div class="search-component">
    <div class="input-group">
      <span class="input-group-text">
        <i class="fas fa-search"></i>
      </span>
      <input
        ref="searchInput"
        type="text"
        class="form-control"
        :placeholder="placeholder"
        v-model="query"
        @input="onInput"
        @keydown.enter="onEnter"
        @keydown.escape="clear"
        @focus="showResults = true"
        @blur="hideResults"
      >
      <button
        v-if="query"
        class="btn btn-outline-secondary"
        type="button"
        @click="clear"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Search Results -->
    <div
      v-if="showResults && (results.length > 0 || loading)"
      class="search-results"
    >
      <div v-if="loading" class="search-result-item text-center">
        <div class="spinner-border spinner-border-sm me-2"></div>
        جاري البحث...
      </div>
      
      <div
        v-else
        v-for="(result, index) in results"
        :key="getResultKey(result, index)"
        class="search-result-item"
        :class="{ active: selectedIndex === index }"
        @mousedown="selectResult(result)"
        @mouseenter="selectedIndex = index"
      >
        <slot name="result" :result="result" :index="index">
          <div class="d-flex align-items-center">
            <div class="result-icon me-2" v-if="result.icon">
              <i :class="result.icon"></i>
            </div>
            <div class="flex-grow-1">
              <div class="result-title">{{ result.title || result.name }}</div>
              <div class="result-subtitle text-muted" v-if="result.subtitle">
                {{ result.subtitle }}
              </div>
            </div>
            <div class="result-meta text-muted" v-if="result.meta">
              {{ result.meta }}
            </div>
          </div>
        </slot>
      </div>

      <div v-if="!loading && results.length === 0 && query" class="search-result-item text-muted">
        لا توجد نتائج للبحث "{{ query }}"
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchComponent',
  props: {
    placeholder: {
      type: String,
      default: 'البحث...'
    },
    searchUrl: {
      type: String,
      default: ''
    },
    searchFunction: {
      type: Function,
      default: null
    },
    debounceTime: {
      type: Number,
      default: 300
    },
    minLength: {
      type: Number,
      default: 2
    },
    maxResults: {
      type: Number,
      default: 10
    },
    searchParams: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['search', 'select', 'clear'],
  data() {
    return {
      query: '',
      results: [],
      loading: false,
      showResults: false,
      selectedIndex: -1,
      debounceTimer: null
    }
  },
  methods: {
    onInput() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }

      this.debounceTimer = setTimeout(() => {
        this.performSearch()
      }, this.debounceTime)
    },
    
    async performSearch() {
      if (this.query.length < this.minLength) {
        this.results = []
        this.showResults = false
        return
      }

      this.loading = true
      this.selectedIndex = -1

      try {
        let results = []

        if (this.searchFunction) {
          results = await this.searchFunction(this.query, this.searchParams)
        } else if (this.searchUrl) {
          const response = await this.$http.get(this.searchUrl, {
            params: {
              q: this.query,
              limit: this.maxResults,
              ...this.searchParams
            }
          })
          results = response.data.data || response.data
        }

        this.results = Array.isArray(results) ? results.slice(0, this.maxResults) : []
        this.showResults = true
        this.$emit('search', { query: this.query, results: this.results })
      } catch (error) {
        console.error('Search error:', error)
        this.results = []
      } finally {
        this.loading = false
      }
    },

    selectResult(result) {
      this.query = result.title || result.name || ''
      this.showResults = false
      this.selectedIndex = -1
      this.$emit('select', result)
    },

    clear() {
      this.query = ''
      this.results = []
      this.showResults = false
      this.selectedIndex = -1
      this.$emit('clear')
      this.$refs.searchInput.focus()
    },

    onEnter() {
      if (this.selectedIndex >= 0 && this.results[this.selectedIndex]) {
        this.selectResult(this.results[this.selectedIndex])
      } else {
        this.$emit('search', { query: this.query, results: this.results })
      }
    },

    hideResults() {
      setTimeout(() => {
        this.showResults = false
      }, 200)
    },

    getResultKey(result, index) {
      return result.id || result.key || index
    },

    navigateResults(direction) {
      if (direction === 'up') {
        this.selectedIndex = Math.max(-1, this.selectedIndex - 1)
      } else if (direction === 'down') {
        this.selectedIndex = Math.min(this.results.length - 1, this.selectedIndex + 1)
      }
    }
  },

  mounted() {
    // Add keyboard navigation
    this.$refs.searchInput.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowUp') {
        e.preventDefault()
        this.navigateResults('up')
      } else if (e.key === 'ArrowDown') {
        e.preventDefault()
        this.navigateResults('down')
      }
    })
  },

  beforeUnmount() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }
  }
}
</script>

<style scoped>
.search-component {
  position: relative;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dee2e6;
  border-top: none;
  border-radius: 0 0 0.375rem 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.search-result-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.15s ease-in-out;
}

.search-result-item:hover,
.search-result-item.active {
  background-color: #f8f9fa;
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-icon {
  width: 24px;
  text-align: center;
  color: #6c757d;
}

.result-title {
  font-weight: 500;
  color: #212529;
}

.result-subtitle {
  font-size: 0.875rem;
  margin-top: 0.125rem;
}

.result-meta {
  font-size: 0.75rem;
}

.input-group .btn {
  border-left: none;
}
</style>
