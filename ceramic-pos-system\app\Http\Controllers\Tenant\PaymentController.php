<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Payment;
use App\Models\PaymentInvoice;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Supplier;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    /**
     * عرض قائمة المدفوعات
     */
    public function index(Request $request)
    {
        $query = Payment::with(['customer', 'supplier', 'createdBy']);

        // التصفية حسب نوع المدفوعة
        if ($request->filled('payment_type')) {
            $query->where('payment_type', $request->payment_type);
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية حسب طريقة الدفع
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // التصفية حسب العميل
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // التصفية حسب المورد
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // التصفية حسب التاريخ
        if ($request->filled('date_from')) {
            $query->where('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('payment_date', '<=', $request->date_to);
        }

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // الترتيب
        $query->orderBy('payment_date', 'desc')->orderBy('id', 'desc');

        $payments = $query->paginate(20);

        // البيانات المساعدة
        $customers = Customer::active()->orderBy('name')->get(['id', 'name', 'customer_code']);
        $suppliers = Supplier::active()->orderBy('name')->get(['id', 'name', 'supplier_code']);

        // إحصائيات
        $stats = [
            'total_payments' => Payment::count(),
            'received_payments' => Payment::received()->count(),
            'paid_payments' => Payment::paid()->count(),
            'cleared_payments' => Payment::cleared()->count(),
            'pending_payments' => Payment::pending()->count(),
            'bounced_payments' => Payment::bounced()->count(),
            'total_received_amount' => Payment::received()->cleared()->sum('amount'),
            'total_paid_amount' => Payment::paid()->cleared()->sum('amount'),
        ];

        return view('tenant.payments.index', compact('payments', 'customers', 'suppliers', 'stats'));
    }

    /**
     * عرض نموذج إنشاء مدفوعة جديدة
     */
    public function create(Request $request)
    {
        $type = $request->get('type', 'received'); // received or paid

        $customers = Customer::active()->orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();

        // الفواتير غير المدفوعة
        if ($type === 'received') {
            $invoices = Invoice::sales()
                             ->confirmed()
                             ->where('remaining_amount', '>', 0)
                             ->with('customer')
                             ->orderBy('invoice_date', 'desc')
                             ->get();
        } else {
            $invoices = Invoice::purchases()
                             ->confirmed()
                             ->where('remaining_amount', '>', 0)
                             ->with('supplier')
                             ->orderBy('invoice_date', 'desc')
                             ->get();
        }

        // إنشاء رقم مدفوعة تلقائي
        $paymentNumber = Payment::generatePaymentNumber($type);

        return view('tenant.payments.create', compact(
            'type', 'customers', 'suppliers', 'invoices', 'paymentNumber'
        ));
    }

    /**
     * حفظ مدفوعة جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'payment_type' => 'required|in:received,paid',
            'payment_number' => 'required|string|max:50|unique:payments,payment_number',
            'payment_date' => 'required|date',
            'customer_id' => 'required_if:payment_type,received|nullable|exists:customers,id',
            'supplier_id' => 'required_if:payment_type,paid|nullable|exists:suppliers,id',
            'payment_method' => 'required|in:' . implode(',', array_keys(Payment::PAYMENT_METHODS)),
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|in:' . implode(',', array_keys(Payment::CURRENCIES)),
            'exchange_rate' => 'nullable|numeric|min:0.0001',
            'bank_name' => 'nullable|string|max:255',
            'account_number' => 'nullable|string|max:50',
            'check_number' => 'nullable|string|max:50',
            'check_date' => 'nullable|date',
            'reference_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
            'invoices' => 'nullable|array',
            'invoices.*.invoice_id' => 'required|exists:invoices,id',
            'invoices.*.amount' => 'required|numeric|min:0.01',
        ], [
            'payment_type.required' => 'نوع المدفوعة مطلوب',
            'payment_number.required' => 'رقم المدفوعة مطلوب',
            'payment_number.unique' => 'رقم المدفوعة مستخدم من قبل',
            'payment_date.required' => 'تاريخ المدفوعة مطلوب',
            'customer_id.required_if' => 'العميل مطلوب للمقبوضات',
            'supplier_id.required_if' => 'المورد مطلوب للمدفوعات',
            'payment_method.required' => 'طريقة الدفع مطلوبة',
            'amount.required' => 'المبلغ مطلوب',
            'currency.required' => 'العملة مطلوبة',
        ]);

        DB::beginTransaction();

        try {
            // حساب المبلغ بالعملة الأساسية
            $exchangeRate = $request->exchange_rate ?? 1;
            $amountInBaseCurrency = $request->amount * $exchangeRate;

            // إنشاء المدفوعة
            $payment = Payment::create([
                'payment_number' => $request->payment_number,
                'payment_type' => $request->payment_type,
                'payment_date' => $request->payment_date,
                'customer_id' => $request->customer_id,
                'supplier_id' => $request->supplier_id,
                'payer_name' => $request->payment_type === 'received'
                    ? ($request->customer_id ? Customer::find($request->customer_id)->full_name : null)
                    : ($request->supplier_id ? Supplier::find($request->supplier_id)->full_name : null),
                'payment_method' => $request->payment_method,
                'amount' => $request->amount,
                'currency' => $request->currency,
                'exchange_rate' => $exchangeRate,
                'amount_in_base_currency' => $amountInBaseCurrency,
                'bank_name' => $request->bank_name,
                'account_number' => $request->account_number,
                'check_number' => $request->check_number,
                'check_date' => $request->check_date,
                'reference_number' => $request->reference_number,
                'notes' => $request->notes,
                'status' => 'pending',
                'created_by' => auth()->id(),
            ]);

            // ربط الفواتير إذا تم تحديدها
            if ($request->filled('invoices')) {
                $totalAllocated = 0;

                foreach ($request->invoices as $invoiceData) {
                    $invoice = Invoice::find($invoiceData['invoice_id']);
                    $amount = $invoiceData['amount'];

                    // التحقق من صحة المبلغ
                    if ($amount > $invoice->remaining_amount) {
                        throw new \Exception("المبلغ المخصص للفاتورة {$invoice->invoice_number} يتجاوز المبلغ المتبقي");
                    }

                    PaymentInvoice::create([
                        'payment_id' => $payment->id,
                        'invoice_id' => $invoice->id,
                        'amount' => $amount,
                    ]);

                    $totalAllocated += $amount;
                }

                // التحقق من أن إجمالي المبالغ المخصصة لا يتجاوز مبلغ المدفوعة
                if ($totalAllocated > $payment->amount) {
                    throw new \Exception('إجمالي المبالغ المخصصة للفواتير يتجاوز مبلغ المدفوعة');
                }
            }

            DB::commit();

            return redirect()->route('tenant.payments.show', $payment)
                ->with('success', 'تم إنشاء المدفوعة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المدفوعة: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل المدفوعة
     */
    public function show(Payment $payment)
    {
        $payment->load(['customer', 'supplier', 'paymentInvoices.invoice', 'createdBy', 'clearedBy']);

        return view('tenant.payments.show', compact('payment'));
    }

    /**
     * عرض نموذج تعديل المدفوعة
     */
    public function edit(Payment $payment)
    {
        if (!$payment->canEdit()) {
            return redirect()->route('tenant.payments.show', $payment)
                ->with('error', 'لا يمكن تعديل هذه المدفوعة');
        }

        $payment->load(['paymentInvoices.invoice']);

        $customers = Customer::active()->orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();

        // الفواتير غير المدفوعة
        if ($payment->payment_type === 'received') {
            $invoices = Invoice::sales()
                             ->confirmed()
                             ->where('remaining_amount', '>', 0)
                             ->with('customer')
                             ->orderBy('invoice_date', 'desc')
                             ->get();
        } else {
            $invoices = Invoice::purchases()
                             ->confirmed()
                             ->where('remaining_amount', '>', 0)
                             ->with('supplier')
                             ->orderBy('invoice_date', 'desc')
                             ->get();
        }

        return view('tenant.payments.edit', compact('payment', 'customers', 'suppliers', 'invoices'));
    }

    /**
     * تحديث المدفوعة
     */
    public function update(Request $request, Payment $payment)
    {
        if (!$payment->canEdit()) {
            return redirect()->route('tenant.payments.show', $payment)
                ->with('error', 'لا يمكن تعديل هذه المدفوعة');
        }

        $request->validate([
            'payment_date' => 'required|date',
            'customer_id' => 'required_if:payment_type,received|nullable|exists:customers,id',
            'supplier_id' => 'required_if:payment_type,paid|nullable|exists:suppliers,id',
            'payment_method' => 'required|in:' . implode(',', array_keys(Payment::PAYMENT_METHODS)),
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|in:' . implode(',', array_keys(Payment::CURRENCIES)),
            'exchange_rate' => 'nullable|numeric|min:0.0001',
            'bank_name' => 'nullable|string|max:255',
            'account_number' => 'nullable|string|max:50',
            'check_number' => 'nullable|string|max:50',
            'check_date' => 'nullable|date',
            'reference_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
            'invoices' => 'nullable|array',
            'invoices.*.invoice_id' => 'required|exists:invoices,id',
            'invoices.*.amount' => 'required|numeric|min:0.01',
        ]);

        DB::beginTransaction();

        try {
            // حساب المبلغ بالعملة الأساسية
            $exchangeRate = $request->exchange_rate ?? 1;
            $amountInBaseCurrency = $request->amount * $exchangeRate;

            // تحديث المدفوعة
            $payment->update([
                'payment_date' => $request->payment_date,
                'customer_id' => $request->customer_id,
                'supplier_id' => $request->supplier_id,
                'payer_name' => $request->payment_type === 'received'
                    ? ($request->customer_id ? Customer::find($request->customer_id)->full_name : null)
                    : ($request->supplier_id ? Supplier::find($request->supplier_id)->full_name : null),
                'payment_method' => $request->payment_method,
                'amount' => $request->amount,
                'currency' => $request->currency,
                'exchange_rate' => $exchangeRate,
                'amount_in_base_currency' => $amountInBaseCurrency,
                'bank_name' => $request->bank_name,
                'account_number' => $request->account_number,
                'check_number' => $request->check_number,
                'check_date' => $request->check_date,
                'reference_number' => $request->reference_number,
                'notes' => $request->notes,
                'updated_by' => auth()->id(),
            ]);

            // حذف ربط الفواتير القديم
            $payment->paymentInvoices()->delete();

            // إنشاء ربط الفواتير الجديد
            if ($request->filled('invoices')) {
                $totalAllocated = 0;

                foreach ($request->invoices as $invoiceData) {
                    $invoice = Invoice::find($invoiceData['invoice_id']);
                    $amount = $invoiceData['amount'];

                    // التحقق من صحة المبلغ
                    if ($amount > $invoice->remaining_amount) {
                        throw new \Exception("المبلغ المخصص للفاتورة {$invoice->invoice_number} يتجاوز المبلغ المتبقي");
                    }

                    PaymentInvoice::create([
                        'payment_id' => $payment->id,
                        'invoice_id' => $invoice->id,
                        'amount' => $amount,
                    ]);

                    $totalAllocated += $amount;
                }

                // التحقق من أن إجمالي المبالغ المخصصة لا يتجاوز مبلغ المدفوعة
                if ($totalAllocated > $payment->amount) {
                    throw new \Exception('إجمالي المبالغ المخصصة للفواتير يتجاوز مبلغ المدفوعة');
                }
            }

            DB::commit();

            return redirect()->route('tenant.payments.show', $payment)
                ->with('success', 'تم تحديث المدفوعة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث المدفوعة: ' . $e->getMessage());
        }
    }

    /**
     * حذف المدفوعة
     */
    public function destroy(Payment $payment)
    {
        if (!$payment->canDelete()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذه المدفوعة');
        }

        try {
            $payment->paymentInvoices()->delete();
            $payment->delete();

            return redirect()->route('tenant.payments.index')
                ->with('success', 'تم حذف المدفوعة بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف المدفوعة: ' . $e->getMessage());
        }
    }

    /**
     * مقاصة المدفوعة
     */
    public function clear(Payment $payment)
    {
        if ($payment->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن مقاصة هذه المدفوعة'
            ], 400);
        }

        try {
            $payment->clear();

            return response()->json([
                'success' => true,
                'message' => 'تم مقاصة المدفوعة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء مقاصة المدفوعة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إلغاء المدفوعة
     */
    public function cancel(Payment $payment)
    {
        if (!in_array($payment->status, ['pending', 'cleared'])) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن إلغاء هذه المدفوعة'
            ], 400);
        }

        try {
            $payment->cancel();

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء المدفوعة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء المدفوعة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * طباعة المدفوعة
     */
    public function print(Payment $payment)
    {
        $payment->load(['customer', 'supplier', 'paymentInvoices.invoice']);

        return view('tenant.payments.print', compact('payment'));
    }

    /**
     * تقرير المدفوعات
     */
    public function report(Request $request)
    {
        $query = Payment::with(['customer', 'supplier', 'createdBy']);

        // التصفية حسب نوع المدفوعة
        if ($request->filled('payment_type')) {
            $query->where('payment_type', $request->payment_type);
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية حسب الفترة الزمنية
        if ($request->filled('date_from')) {
            $query->where('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('payment_date', '<=', $request->date_to);
        }

        $payments = $query->orderBy('payment_date', 'desc')->get();

        return view('tenant.payments.report', compact('payments'));
    }

    /**
     * الحصول على الفواتير غير المدفوعة للعميل/المورد
     */
    public function getUnpaidInvoices(Request $request)
    {
        $type = $request->get('type');
        $customerId = $request->get('customer_id');
        $supplierId = $request->get('supplier_id');

        if ($type === 'received' && $customerId) {
            $invoices = Invoice::sales()
                             ->confirmed()
                             ->where('customer_id', $customerId)
                             ->where('remaining_amount', '>', 0)
                             ->orderBy('invoice_date', 'desc')
                             ->get(['id', 'invoice_number', 'invoice_date', 'total_amount', 'remaining_amount']);
        } elseif ($type === 'paid' && $supplierId) {
            $invoices = Invoice::purchases()
                             ->confirmed()
                             ->where('supplier_id', $supplierId)
                             ->where('remaining_amount', '>', 0)
                             ->orderBy('invoice_date', 'desc')
                             ->get(['id', 'invoice_number', 'invoice_date', 'total_amount', 'remaining_amount']);
        } else {
            $invoices = collect();
        }

        return response()->json($invoices);
    }
}
