<?php $__env->startSection('title', 'المحاسبة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-calculator me-2 text-primary"></i>
                المحاسبة
            </h2>
            <p class="text-muted mb-0"><?php echo e($tenant->company_name); ?></p>
        </div>
        <div>
            <div class="btn-group">
                <a href="<?php echo e(route('accounting.chart-of-accounts', ['tenant' => $tenant->id])); ?>" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>
                    دليل الحسابات
                </a>
                <a href="<?php echo e(route('accounting.profit-loss', ['tenant' => $tenant->id])); ?>" class="btn btn-outline-success">
                    <i class="fas fa-chart-line me-2"></i>
                    الأرباح والخسائر
                </a>
                <a href="<?php echo e(route('accounting.balance-sheet', ['tenant' => $tenant->id])); ?>" class="btn btn-outline-info">
                    <i class="fas fa-balance-scale me-2"></i>
                    الميزانية العمومية
                </a>
            </div>
        </div>
    </div>

    <!-- Financial Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-arrow-up fa-2x"></i>
                    </div>
                    <h3 class="text-success"><?php echo e(number_format($stats['total_sales'], 2)); ?></h3>
                    <p class="text-muted mb-0">إجمالي المبيعات (ج.م)</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-danger mb-2">
                        <i class="fas fa-arrow-down fa-2x"></i>
                    </div>
                    <h3 class="text-danger"><?php echo e(number_format($stats['total_purchases'], 2)); ?></h3>
                    <p class="text-muted mb-0">إجمالي المشتريات (ج.م)</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h3 class="text-warning"><?php echo e(number_format($stats['pending_payments'], 2)); ?></h3>
                    <p class="text-muted mb-0">مدفوعات معلقة (ج.م)</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                    <h3 class="text-primary"><?php echo e(number_format($stats['cash_balance'], 2)); ?></h3>
                    <p class="text-muted mb-0">الرصيد النقدي (ج.م)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Profit Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-chart-pie me-2"></i>
                        ملخص الربحية
                    </h5>
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="border-end">
                                <h4 class="text-success"><?php echo e(number_format($stats['total_sales'], 2)); ?></h4>
                                <p class="text-muted">إجمالي الإيرادات</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border-end">
                                <h4 class="text-danger"><?php echo e(number_format($stats['total_purchases'], 2)); ?></h4>
                                <p class="text-muted">إجمالي المصروفات</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h4 class="text-<?php echo e(($stats['total_sales'] - $stats['total_purchases']) >= 0 ? 'success' : 'danger'); ?>">
                                <?php echo e(number_format($stats['total_sales'] - $stats['total_purchases'], 2)); ?>

                            </h4>
                            <p class="text-muted">صافي الربح</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Transactions -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        آخر المعاملات المالية
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($recentTransactions->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>النوع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($transaction->payment_date->format('Y/m/d')); ?></td>
                                        <td>
                                            <?php if($transaction->invoice): ?>
                                                <a href="<?php echo e(route('invoices.show', ['invoice' => $transaction->invoice->id, 'tenant' => $tenant->id])); ?>">
                                                    <?php echo e($transaction->invoice->invoice_number); ?>

                                                </a>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($transaction->invoice && $transaction->invoice->customer): ?>
                                                <?php echo e($transaction->invoice->customer->name); ?>

                                            <?php else: ?>
                                                عميل نقدي
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e(number_format($transaction->amount, 2)); ?> ج.م</td>
                                        <td>
                                            <?php switch($transaction->payment_method):
                                                case ('cash'): ?>
                                                    <span class="badge bg-success">نقدي</span>
                                                    <?php break; ?>
                                                <?php case ('card'): ?>
                                                    <span class="badge bg-primary">بطاقة</span>
                                                    <?php break; ?>
                                                <?php case ('bank_transfer'): ?>
                                                    <span class="badge bg-info">تحويل بنكي</span>
                                                    <?php break; ?>
                                                <?php default: ?>
                                                    <span class="badge bg-secondary"><?php echo e($transaction->payment_method); ?></span>
                                            <?php endswitch; ?>
                                        </td>
                                        <td>
                                            <?php if($transaction->invoice): ?>
                                                <?php if($transaction->invoice->type === 'sale'): ?>
                                                    <span class="text-success">
                                                        <i class="fas fa-arrow-up me-1"></i>
                                                        وارد
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-danger">
                                                        <i class="fas fa-arrow-down me-1"></i>
                                                        صادر
                                                    </span>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد معاملات مالية</h6>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('invoices.create', ['tenant' => $tenant->id])); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء فاتورة جديدة
                        </a>
                        <a href="<?php echo e(route('pos.index', ['tenant' => $tenant->id])); ?>" class="btn btn-success">
                            <i class="fas fa-cash-register me-2"></i>
                            نقطة البيع
                        </a>
                        <a href="<?php echo e(route('payments.index', ['tenant' => $tenant->id])); ?>" class="btn btn-info">
                            <i class="fas fa-credit-card me-2"></i>
                            إدارة المدفوعات
                        </a>
                        <a href="<?php echo e(route('accounting.profit-loss', ['tenant' => $tenant->id])); ?>" class="btn btn-warning">
                            <i class="fas fa-chart-line me-2"></i>
                            تقرير الأرباح والخسائر
                        </a>
                    </div>
                </div>
            </div>

            <!-- Account Summary -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        ملخص الحسابات الرئيسية
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($mainAccounts->count() > 0): ?>
                        <?php $__currentLoopData = $mainAccounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong><?php echo e($account->account_name); ?></strong>
                                <br>
                                <small class="text-muted"><?php echo e($account->account_code); ?></small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo e($account->account_type === 'asset' ? 'success' : ($account->account_type === 'liability' ? 'danger' : 'info')); ?>">
                                    <?php echo e($account->children->count()); ?> حساب فرعي
                                </span>
                            </div>
                        </div>
                        <?php if(!$loop->last): ?>
                            <hr>
                        <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <p class="text-muted text-center">لم يتم إعداد دليل الحسابات بعد</p>
                        <div class="d-grid">
                            <a href="<?php echo e(route('accounting.chart-of-accounts', ['tenant' => $tenant->id])); ?>" class="btn btn-outline-primary btn-sm">
                                إعداد دليل الحسابات
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Work\ceramic pos\ceramic-pos-system\resources\views/accounting/index.blade.php ENDPATH**/ ?>