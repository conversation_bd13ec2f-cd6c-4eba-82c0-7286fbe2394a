<template>
  <div class="data-table-container">
    <!-- Search and Filters -->
    <div class="row mb-3" v-if="showSearch || showFilters">
      <div class="col-md-6" v-if="showSearch">
        <div class="input-group">
          <span class="input-group-text">
            <i class="fas fa-search"></i>
          </span>
          <input
            type="text"
            class="form-control"
            :placeholder="searchPlaceholder"
            v-model="searchQuery"
            @input="onSearch"
          >
        </div>
      </div>
      <div class="col-md-6" v-if="showFilters">
        <div class="d-flex gap-2">
          <select
            v-for="filter in filters"
            :key="filter.key"
            class="form-select"
            v-model="filterValues[filter.key]"
            @change="onFilter"
          >
            <option value="">{{ filter.placeholder }}</option>
            <option
              v-for="option in filter.options"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="table-responsive">
      <table class="table table-hover">
        <thead class="table-light">
          <tr>
            <th v-if="selectable">
              <input
                type="checkbox"
                class="form-check-input"
                v-model="selectAll"
                @change="toggleSelectAll"
              >
            </th>
            <th
              v-for="column in columns"
              :key="column.key"
              :class="getSortClass(column.key)"
              @click="sort(column.key)"
              style="cursor: pointer;"
            >
              {{ column.label }}
              <i
                v-if="column.sortable !== false"
                class="fas fa-sort ms-1"
                :class="getSortIcon(column.key)"
              ></i>
            </th>
            <th v-if="actions.length > 0">الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td :colspan="totalColumns" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
              </div>
            </td>
          </tr>
          <tr v-else-if="paginatedData.length === 0">
            <td :colspan="totalColumns" class="text-center py-4 text-muted">
              {{ emptyMessage }}
            </td>
          </tr>
          <tr
            v-else
            v-for="(item, index) in paginatedData"
            :key="getItemKey(item, index)"
            :class="getRowClass(item)"
          >
            <td v-if="selectable">
              <input
                type="checkbox"
                class="form-check-input"
                :value="getItemKey(item, index)"
                v-model="selectedItems"
              >
            </td>
            <td v-for="column in columns" :key="column.key">
              <slot
                :name="`column-${column.key}`"
                :item="item"
                :value="getColumnValue(item, column.key)"
              >
                {{ formatColumnValue(item, column) }}
              </slot>
            </td>
            <td v-if="actions.length > 0">
              <div class="btn-group btn-group-sm">
                <button
                  v-for="action in getItemActions(item)"
                  :key="action.key"
                  :class="action.class || 'btn btn-outline-primary'"
                  :title="action.title"
                  @click="$emit('action', { action: action.key, item })"
                >
                  <i :class="action.icon"></i>
                  <span v-if="action.label" class="ms-1">{{ action.label }}</span>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="row align-items-center" v-if="showPagination && totalPages > 1">
      <div class="col-md-6">
        <p class="text-muted mb-0">
          عرض {{ startItem }} إلى {{ endItem }} من {{ filteredData.length }} عنصر
        </p>
      </div>
      <div class="col-md-6">
        <nav>
          <ul class="pagination pagination-sm justify-content-end mb-0">
            <li class="page-item" :class="{ disabled: currentPage === 1 }">
              <button class="page-link" @click="goToPage(currentPage - 1)">
                <i class="fas fa-chevron-right"></i>
              </button>
            </li>
            <li
              v-for="page in visiblePages"
              :key="page"
              class="page-item"
              :class="{ active: page === currentPage }"
            >
              <button class="page-link" @click="goToPage(page)">
                {{ page }}
              </button>
            </li>
            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
              <button class="page-link" @click="goToPage(currentPage + 1)">
                <i class="fas fa-chevron-left"></i>
              </button>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataTable',
  props: {
    data: {
      type: Array,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    actions: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showFilters: {
      type: Boolean,
      default: false
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    perPage: {
      type: Number,
      default: 20
    },
    searchPlaceholder: {
      type: String,
      default: 'البحث...'
    },
    emptyMessage: {
      type: String,
      default: 'لا توجد بيانات للعرض'
    },
    filters: {
      type: Array,
      default: () => []
    },
    keyField: {
      type: String,
      default: 'id'
    }
  },
  emits: ['action', 'selection-changed', 'search', 'filter'],
  data() {
    return {
      searchQuery: '',
      filterValues: {},
      sortKey: '',
      sortOrder: 'asc',
      currentPage: 1,
      selectedItems: [],
      selectAll: false
    }
  },
  computed: {
    filteredData() {
      let filtered = [...this.data]

      // Apply search
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(item => {
          return this.columns.some(column => {
            const value = this.getColumnValue(item, column.key)
            return String(value).toLowerCase().includes(query)
          })
        })
      }

      // Apply filters
      Object.keys(this.filterValues).forEach(key => {
        const value = this.filterValues[key]
        if (value) {
          filtered = filtered.filter(item => {
            return this.getColumnValue(item, key) === value
          })
        }
      })

      // Apply sorting
      if (this.sortKey) {
        filtered.sort((a, b) => {
          const aVal = this.getColumnValue(a, this.sortKey)
          const bVal = this.getColumnValue(b, this.sortKey)
          
          let result = 0
          if (aVal < bVal) result = -1
          else if (aVal > bVal) result = 1
          
          return this.sortOrder === 'desc' ? -result : result
        })
      }

      return filtered
    },
    paginatedData() {
      if (!this.showPagination) return this.filteredData
      
      const start = (this.currentPage - 1) * this.perPage
      const end = start + this.perPage
      return this.filteredData.slice(start, end)
    },
    totalPages() {
      return Math.ceil(this.filteredData.length / this.perPage)
    },
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    },
    startItem() {
      return (this.currentPage - 1) * this.perPage + 1
    },
    endItem() {
      return Math.min(this.currentPage * this.perPage, this.filteredData.length)
    },
    totalColumns() {
      let count = this.columns.length
      if (this.selectable) count++
      if (this.actions.length > 0) count++
      return count
    }
  },
  watch: {
    selectedItems() {
      this.$emit('selection-changed', this.selectedItems)
    },
    data() {
      this.currentPage = 1
    }
  },
  methods: {
    getColumnValue(item, key) {
      return key.split('.').reduce((obj, k) => obj?.[k], item)
    },
    formatColumnValue(item, column) {
      const value = this.getColumnValue(item, column.key)
      
      if (column.type === 'currency') {
        return this.$formatCurrency(value, column.currency)
      } else if (column.type === 'date') {
        return this.$formatDate(value, column.format)
      } else if (column.type === 'number') {
        return this.$formatNumber(value, column.decimals)
      }
      
      return value
    },
    getItemKey(item, index) {
      return this.getColumnValue(item, this.keyField) || index
    },
    getRowClass(item) {
      return item._rowClass || ''
    },
    getItemActions(item) {
      return this.actions.filter(action => {
        return !action.condition || action.condition(item)
      })
    },
    sort(key) {
      if (this.sortKey === key) {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc'
      } else {
        this.sortKey = key
        this.sortOrder = 'asc'
      }
    },
    getSortClass(key) {
      return this.sortKey === key ? 'sorted' : ''
    },
    getSortIcon(key) {
      if (this.sortKey !== key) return 'text-muted'
      return this.sortOrder === 'asc' ? 'fa-sort-up' : 'fa-sort-down'
    },
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      }
    },
    toggleSelectAll() {
      if (this.selectAll) {
        this.selectedItems = this.paginatedData.map((item, index) => 
          this.getItemKey(item, index)
        )
      } else {
        this.selectedItems = []
      }
    },
    onSearch() {
      this.currentPage = 1
      this.$emit('search', this.searchQuery)
    },
    onFilter() {
      this.currentPage = 1
      this.$emit('filter', this.filterValues)
    }
  }
}
</script>

<style scoped>
.data-table-container {
  background: white;
  border-radius: 8px;
  padding: 1rem;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.table th.sorted {
  background-color: #e9ecef;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.pagination .page-link {
  color: #007bff;
  border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}
</style>
