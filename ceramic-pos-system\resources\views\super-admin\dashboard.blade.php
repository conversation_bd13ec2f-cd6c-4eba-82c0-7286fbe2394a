@extends('layouts.super-admin')

@section('title', 'لوحة تحكم صاحب النظام')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="fw-bold mb-1">
                <i class="fas fa-crown me-2 text-warning"></i>
                لوحة تحكم صاحب النظام
            </h1>
            <p class="text-muted mb-0">إدارة شاملة لنظام Ceramic POS</p>
        </div>
        <div>
            <span class="badge bg-success fs-6">
                <i class="fas fa-shield-alt me-1"></i>
                Super Admin
            </span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ $stats['total_tenants'] }}</h3>
                            <p class="mb-0">إجمالي المشتركين</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ $stats['active_tenants'] }}</h3>
                            <p class="mb-0">مشتركين نشطين</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ $stats['trial_tenants'] }}</h3>
                            <p class="mb-0">فترة تجريبية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['total_revenue']) }}</h3>
                            <p class="mb-0">الإيرادات الشهرية (ج.م)</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tenants -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        أحدث المشتركين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الشركة</th>
                                    <th>المالك</th>
                                    <th>الخطة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recent_tenants as $tenant)
                                <tr>
                                    <td>
                                        <strong>{{ $tenant->company_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $tenant->subdomain }}.ceramicpos.com</small>
                                    </td>
                                    <td>
                                        {{ $tenant->owner_name }}
                                        <br>
                                        <small class="text-muted">{{ $tenant->email }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $tenant->getPlanName() }}</span>
                                    </td>
                                    <td>
                                        @switch($tenant->status)
                                            @case('active')
                                                <span class="badge bg-success">نشط</span>
                                                @break
                                            @case('trial')
                                                <span class="badge bg-warning">تجريبي</span>
                                                @break
                                            @case('suspended')
                                                <span class="badge bg-danger">معلق</span>
                                                @break
                                            @case('expired')
                                                <span class="badge bg-dark">منتهي</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>{{ $tenant->created_at->format('Y/m/d') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('super-admin.tenants.show', $tenant->id) }}" 
                                               class="btn btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('dashboard', ['tenant' => $tenant->id]) }}" 
                                               class="btn btn-outline-primary" title="دخول للنظام" target="_blank">
                                                <i class="fas fa-sign-in-alt"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ route('super-admin.tenants') }}" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع المشتركين
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expiring Trials & Quick Actions -->
        <div class="col-lg-4">
            <!-- Expiring Trials -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                        فترات تجريبية تنتهي قريباً
                    </h5>
                </div>
                <div class="card-body">
                    @if($expiring_trials->count() > 0)
                        @foreach($expiring_trials as $tenant)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ $tenant->company_name }}</strong>
                                <br>
                                <small class="text-muted">
                                    ينتهي في {{ $tenant->trial_ends_at->diffForHumans() }}
                                </small>
                            </div>
                            <a href="{{ route('super-admin.tenants.show', $tenant->id) }}" 
                               class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                        @if(!$loop->last)
                            <hr>
                        @endif
                        @endforeach
                    @else
                        <p class="text-muted text-center">لا توجد فترات تجريبية تنتهي قريباً</p>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('super-admin.tenants.create') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مشترك جديد
                        </a>
                        <a href="{{ route('super-admin.plans') }}" class="btn btn-info">
                            <i class="fas fa-tags me-2"></i>
                            إدارة الخطط والأسعار
                        </a>
                        <a href="{{ route('super-admin.financial-reports') }}" class="btn btn-warning">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير المالية
                        </a>
                        <a href="{{ route('super-admin.settings') }}" class="btn btn-secondary">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>
                        حالة النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">
                                    <i class="fas fa-check-circle"></i>
                                </h4>
                                <p class="text-muted mb-0">الخادم</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">
                                    <i class="fas fa-database"></i>
                                </h4>
                                <p class="text-muted mb-0">قاعدة البيانات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">
                                    <i class="fas fa-cloud"></i>
                                </h4>
                                <p class="text-muted mb-0">التخزين السحابي</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">
                                <i class="fas fa-shield-alt"></i>
                            </h4>
                            <p class="text-muted mb-0">الأمان</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
