<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إدارة SaaS - Ceramic POS</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .table-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-crown me-2"></i>
                Ceramic POS - إدارة SaaS
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مدير النظام | {{ auth()->user()->name }}
                </span>
                <a href="{{ route('home') }}" class="btn btn-outline-light btn-sm me-2">
                    <i class="fas fa-home me-1"></i>
                    الصفحة الرئيسية
                </a>
                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        تسجيل الخروج
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="fw-bold">
                    <i class="fas fa-chart-line me-2"></i>
                    لوحة تحكم SaaS
                </h2>
                <p class="text-muted">إدارة العملاء والاشتراكات والإيرادات</p>
            </div>
        </div>

        <!-- Stats Row -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-primary">{{ $stats['total_tenants'] }}</div>
                    <h6>إجمالي العملاء</h6>
                    <small class="text-muted">جميع الشركات المسجلة</small>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-success">{{ $stats['active_tenants'] }}</div>
                    <h6>العملاء النشطين</h6>
                    <small class="text-muted">اشتراكات نشطة</small>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-info">{{ $stats['trial_tenants'] }}</div>
                    <h6>فترات تجريبية</h6>
                    <small class="text-muted">عملاء في الفترة التجريبية</small>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number text-warning">{{ number_format($stats['monthly_revenue']) }}</div>
                    <h6>الإيرادات الشهرية</h6>
                    <small class="text-muted">جنيه مصري</small>
                </div>
            </div>
        </div>

        <!-- Tenants Table -->
        <div class="table-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    قائمة العملاء
                </h5>
                <div>
                    <button class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        إضافة عميل جديد
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الشركة</th>
                            <th>المالك</th>
                            <th>البريد الإلكتروني</th>
                            <th>الخطة</th>
                            <th>الحالة</th>
                            <th>السعر الشهري</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($tenants as $tenant)
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ $tenant->company_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $tenant->subdomain }}.ceramicpos.com</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    {{ $tenant->owner_name }}
                                    <br>
                                    <small class="text-muted">{{ $tenant->city }}</small>
                                </div>
                            </td>
                            <td>{{ $tenant->email }}</td>
                            <td>
                                <span class="badge bg-{{ $tenant->plan === 'enterprise' ? 'danger' : ($tenant->plan === 'premium' ? 'warning' : 'info') }}">
                                    {{ $tenant->getPlanName() }}
                                </span>
                            </td>
                            <td>
                                @if($tenant->status === 'trial')
                                    <span class="badge bg-info">فترة تجريبية</span>
                                @elseif($tenant->status === 'active')
                                    <span class="badge bg-success">نشط</span>
                                @elseif($tenant->status === 'suspended')
                                    <span class="badge bg-warning">معلق</span>
                                @else
                                    <span class="badge bg-danger">ملغي</span>
                                @endif
                            </td>
                            <td>{{ number_format($tenant->monthly_price) }} ج.م</td>
                            <td>
                                @if($tenant->trial_ends_at)
                                    {{ $tenant->trial_ends_at->format('Y/m/d') }}
                                @elseif($tenant->subscription_ends_at)
                                    {{ $tenant->subscription_ends_at->format('Y/m/d') }}
                                @else
                                    -
                                @endif
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" title="تعليق">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="table-card">
                    <h6 class="mb-3">
                        <i class="fas fa-clock me-2"></i>
                        النشاط الأخير
                    </h6>
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>تسجيل عميل جديد</strong>
                                <br>
                                <small class="text-muted">شركة السيراميك التجريبية</small>
                            </div>
                            <small class="text-muted">منذ ساعتين</small>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>ترقية اشتراك</strong>
                                <br>
                                <small class="text-muted">متجر السيراميك الحديث</small>
                            </div>
                            <small class="text-muted">أمس</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="table-card">
                    <h6 class="mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات مهمة
                    </h6>
                    <div class="list-group list-group-flush">
                        @foreach($expiring_trials as $tenant)
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ $tenant->company_name }}</strong>
                                    <br>
                                    <small class="text-muted">تنتهي الفترة التجريبية في {{ $tenant->trial_ends_at->diffForHumans() }}</small>
                                </div>
                                <span class="badge bg-warning">تنبيه</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
