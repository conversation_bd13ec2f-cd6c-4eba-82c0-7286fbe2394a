import { defineStore } from 'pinia'
import axios from 'axios'

export const useMainStore = defineStore('main', {
  state: () => ({
    // User & Auth
    user: null,
    permissions: [],
    
    // Dashboard
    dashboardStats: {},
    dashboardLoading: false,
    
    // Settings
    settings: {},
    
    // UI State
    sidebarCollapsed: false,
    theme: 'light',
    
    // Notifications
    notifications: [],
    
    // Loading states
    loading: {
      global: false,
      dashboard: false,
      settings: false
    },
    
    // Cache
    cache: {
      customers: [],
      suppliers: [],
      products: [],
      lastUpdated: {}
    }
  }),

  getters: {
    isAuthenticated: (state) => !!state.user,
    
    hasPermission: (state) => (permission) => {
      if (!state.permissions.length) return false
      return state.permissions.includes('*') || state.permissions.includes(permission)
    },
    
    getSetting: (state) => (key, defaultValue = null) => {
      return key.split('.').reduce((obj, k) => obj?.[k], state.settings) || defaultValue
    },
    
    getThemeClass: (state) => {
      return state.theme === 'dark' ? 'theme-dark' : 'theme-light'
    },
    
    getCachedData: (state) => (type) => {
      return state.cache[type] || []
    },
    
    isCacheExpired: (state) => (type, maxAge = 300000) => { // 5 minutes default
      const lastUpdated = state.cache.lastUpdated[type]
      if (!lastUpdated) return true
      return Date.now() - lastUpdated > maxAge
    }
  },

  actions: {
    // Auth actions
    setUser(user) {
      this.user = user
      this.permissions = user?.permissions || []
    },

    logout() {
      this.user = null
      this.permissions = []
      this.clearCache()
    },

    // Dashboard actions
    async fetchDashboardStats(period = 'month') {
      this.loading.dashboard = true
      try {
        const response = await axios.get('/api/v1/dashboard/stats', {
          params: { period }
        })
        this.dashboardStats = response.data.data
        return response.data.data
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error)
        throw error
      } finally {
        this.loading.dashboard = false
      }
    },

    // Settings actions
    async fetchSettings() {
      this.loading.settings = true
      try {
        const response = await axios.get('/api/v1/settings')
        this.settings = response.data.data
        return response.data.data
      } catch (error) {
        console.error('Failed to fetch settings:', error)
        throw error
      } finally {
        this.loading.settings = false
      }
    },

    async updateSetting(key, value, group = 'general') {
      try {
        const response = await axios.post('/api/v1/settings', {
          key,
          value,
          group
        })
        
        // Update local settings
        const keys = key.split('.')
        let current = this.settings
        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) current[keys[i]] = {}
          current = current[keys[i]]
        }
        current[keys[keys.length - 1]] = value
        
        return response.data
      } catch (error) {
        console.error('Failed to update setting:', error)
        throw error
      }
    },

    // UI actions
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
      localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed)
    },

    setSidebarCollapsed(collapsed) {
      this.sidebarCollapsed = collapsed
      localStorage.setItem('sidebarCollapsed', collapsed)
    },

    setTheme(theme) {
      this.theme = theme
      localStorage.setItem('theme', theme)
      document.documentElement.setAttribute('data-theme', theme)
    },

    // Notification actions
    addNotification(notification) {
      const id = Date.now() + Math.random()
      this.notifications.push({
        id,
        type: 'info',
        duration: 5000,
        ...notification
      })
      
      if (notification.duration > 0) {
        setTimeout(() => {
          this.removeNotification(id)
        }, notification.duration)
      }
      
      return id
    },

    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },

    clearNotifications() {
      this.notifications = []
    },

    // Cache actions
    setCacheData(type, data) {
      this.cache[type] = data
      this.cache.lastUpdated[type] = Date.now()
    },

    getCacheOrFetch(type, fetchFunction, maxAge = 300000) {
      if (!this.isCacheExpired(type, maxAge)) {
        return Promise.resolve(this.getCachedData(type))
      }
      
      return fetchFunction().then(data => {
        this.setCacheData(type, data)
        return data
      })
    },

    clearCache(type = null) {
      if (type) {
        this.cache[type] = []
        delete this.cache.lastUpdated[type]
      } else {
        this.cache = {
          customers: [],
          suppliers: [],
          products: [],
          lastUpdated: {}
        }
      }
    },

    // Search actions
    async search(query, type = 'all') {
      try {
        const response = await axios.get('/api/v1/search', {
          params: { q: query, type }
        })
        return response.data.data
      } catch (error) {
        console.error('Search failed:', error)
        throw error
      }
    },

    // Loading actions
    setLoading(key, value) {
      this.loading[key] = value
    },

    setGlobalLoading(value) {
      this.loading.global = value
    },

    // Initialize store
    async initialize() {
      // Load from localStorage
      const sidebarCollapsed = localStorage.getItem('sidebarCollapsed')
      if (sidebarCollapsed !== null) {
        this.sidebarCollapsed = JSON.parse(sidebarCollapsed)
      }

      const theme = localStorage.getItem('theme')
      if (theme) {
        this.setTheme(theme)
      }

      // Fetch initial data
      try {
        await Promise.all([
          this.fetchSettings(),
          this.fetchDashboardStats()
        ])
      } catch (error) {
        console.error('Failed to initialize store:', error)
      }
    },

    // Utility actions
    formatCurrency(amount, currency = 'EGP') {
      const symbols = {
        'EGP': 'ج.م',
        'USD': '$',
        'EUR': '€',
        'SAR': 'ر.س',
        'AED': 'د.إ'
      }
      
      const symbol = symbols[currency] || currency
      return new Intl.NumberFormat('ar-EG').format(amount) + ' ' + symbol
    },

    formatNumber(number, decimals = 2) {
      return new Intl.NumberFormat('ar-EG', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      }).format(number)
    },

    formatDate(date, format = 'short') {
      const options = {
        short: { year: 'numeric', month: '2-digit', day: '2-digit' },
        long: { year: 'numeric', month: 'long', day: 'numeric' },
        datetime: { 
          year: 'numeric', 
          month: '2-digit', 
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }
      }
      
      return new Intl.DateTimeFormat('ar-EG', options[format] || options.short).format(new Date(date))
    }
  }
})
