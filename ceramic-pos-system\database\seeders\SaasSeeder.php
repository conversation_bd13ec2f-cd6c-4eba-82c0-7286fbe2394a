<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SimpleTenant;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class SaasSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء شركات تجريبية
        $tenant1 = SimpleTenant::create([
            'company_name' => 'شركة السيراميك التجريبية',
            'subdomain' => 'demo-company',
            'owner_name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'phone' => '01234567890',
            'city' => 'القاهرة',
            'plan' => 'premium',
            'status' => 'trial',
            'trial_ends_at' => now()->addDays(30),
            'monthly_price' => 599,
        ]);

        $tenant2 = SimpleTenant::create([
            'company_name' => 'متجر السيراميك الحديث',
            'subdomain' => 'ceramic-store',
            'owner_name' => 'فاطمة أحمد',
            'email' => '<EMAIL>',
            'phone' => '01098765432',
            'city' => 'الإسكندرية',
            'plan' => 'basic',
            'status' => 'active',
            'trial_ends_at' => now()->subDays(10),
            'subscription_ends_at' => now()->addMonths(6),
            'monthly_price' => 299,
        ]);

        // إنشاء مستخدمين للشركة الأولى
        User::create([
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'tenant_id' => null, // سنستخدم نظام مختلف
            'is_owner' => true,
            'is_active' => true,
        ]);

        User::create([
            'name' => 'محاسب الشركة',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'tenant_id' => null,
            'is_owner' => false,
            'is_active' => true,
        ]);

        User::create([
            'name' => 'مدير المبيعات',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'tenant_id' => null,
            'is_owner' => false,
            'is_active' => true,
        ]);

        User::create([
            'name' => 'مدير المخزن',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'tenant_id' => null,
            'is_owner' => false,
            'is_active' => true,
        ]);

        $this->command->info('تم إنشاء البيانات التجريبية بنجاح!');
        $this->command->info('الشركات:');
        $this->command->info('- شركة السيراميك التجريبية (demo-company)');
        $this->command->info('- متجر السيراميك الحديث (ceramic-store)');
        $this->command->info('');
        $this->command->info('المستخدمين:');
        $this->command->info('- <EMAIL> / password');
        $this->command->info('- <EMAIL> / password');
        $this->command->info('- <EMAIL> / password');
        $this->command->info('- <EMAIL> / password');
        $this->command->info('- <EMAIL> / password');
        $this->command->info('- <EMAIL> / password');
    }
}
