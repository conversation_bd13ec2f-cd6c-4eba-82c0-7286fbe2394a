<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة حقول للمستخدمين إذا لم تكن موجودة
        if (!Schema::hasColumn('users', 'is_active')) {
            Schema::table('users', function (Blueprint $table) {
                $table->boolean('is_active')->default(true)->after('email_verified_at');
            });
        }

        if (!Schema::hasColumn('users', 'last_login_at')) {
            Schema::table('users', function (Blueprint $table) {
                $table->timestamp('last_login_at')->nullable();
            });
        }

        // إنشاء جدول الصلاحيات إذا لم يكن موجوداً
        if (!Schema::hasTable('permissions')) {
            Schema::create('permissions', function (Blueprint $table) {
                $table->id();
                $table->string('name')->unique();
                $table->string('display_name');
                $table->text('description')->nullable();
                $table->string('module');
                $table->string('action');
                $table->boolean('is_system_permission')->default(false);
                $table->timestamps();
            });
        }

        // تحديث جدول الأدوار الموجود
        if (!Schema::hasColumn('roles', 'tenant_id')) {
            Schema::table('roles', function (Blueprint $table) {
                $table->string('tenant_id')->nullable()->after('id');
                $table->string('display_name')->nullable()->after('name');
                $table->text('description')->nullable()->after('display_name');
                $table->json('permissions')->nullable()->after('description');
                $table->boolean('is_system_role')->default(false)->after('permissions');
                $table->boolean('is_active')->default(true)->after('is_system_role');
            });
        }

        // إنشاء جدول ربط المستخدمين بالأدوار
        if (!Schema::hasTable('role_user')) {
            Schema::create('role_user', function (Blueprint $table) {
                $table->id();
                $table->string('tenant_id')->nullable();
                $table->unsignedBigInteger('user_id');
                $table->unsignedBigInteger('role_id');
                $table->timestamp('assigned_at')->useCurrent();
                $table->unsignedBigInteger('assigned_by')->nullable();
                $table->timestamps();

                $table->unique(['tenant_id', 'user_id', 'role_id']);
            });
        }

        // إنشاء جدول ربط الأدوار بالصلاحيات
        if (!Schema::hasTable('permission_role')) {
            Schema::create('permission_role', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('permission_id');
                $table->unsignedBigInteger('role_id');
                $table->timestamps();

                $table->unique(['permission_id', 'role_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permission_role');
        Schema::dropIfExists('role_user');
        Schema::dropIfExists('permissions');

        Schema::table('roles', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
            $table->dropUnique(['tenant_id', 'name']);
            $table->dropColumn(['tenant_id', 'display_name', 'description', 'permissions', 'is_system_role', 'is_active']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['is_active', 'last_login_at']);
        });
    }
};
