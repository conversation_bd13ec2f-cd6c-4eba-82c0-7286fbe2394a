<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\StockMovement;
use App\Services\DashboardService;
use Carbon\Carbon;

class WidgetController extends Controller
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * عنصر إحصائيات المبيعات
     */
    public function salesWidget(Request $request)
    {
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDate($period);
        $endDate = now();

        $stats = [
            'total_sales' => Invoice::sales()
                                  ->confirmed()
                                  ->whereBetween('invoice_date', [$startDate, $endDate])
                                  ->sum('total_amount'),
            'total_invoices' => Invoice::sales()
                                      ->confirmed()
                                      ->whereBetween('invoice_date', [$startDate, $endDate])
                                      ->count(),
            'average_invoice' => Invoice::sales()
                                       ->confirmed()
                                       ->whereBetween('invoice_date', [$startDate, $endDate])
                                       ->avg('total_amount') ?? 0,
            'growth_rate' => $this->calculateSalesGrowthRate($period),
        ];

        return response()->json($stats);
    }

    /**
     * عنصر إحصائيات المدفوعات
     */
    public function paymentsWidget(Request $request)
    {
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDate($period);
        $endDate = now();

        $stats = [
            'total_received' => Payment::received()
                                      ->cleared()
                                      ->whereBetween('payment_date', [$startDate, $endDate])
                                      ->sum('amount'),
            'total_paid' => Payment::paid()
                                  ->cleared()
                                  ->whereBetween('payment_date', [$startDate, $endDate])
                                  ->sum('amount'),
            'pending_received' => Payment::received()->pending()->sum('amount'),
            'pending_paid' => Payment::paid()->pending()->sum('amount'),
            'net_cash_flow' => $this->calculateNetCashFlow($startDate, $endDate),
        ];

        return response()->json($stats);
    }

    /**
     * عنصر إحصائيات المخزون
     */
    public function inventoryWidget()
    {
        $stats = [
            'total_products' => Product::count(),
            'low_stock_count' => Product::lowStock()->count(),
            'out_of_stock_count' => Product::outOfStock()->count(),
            'total_stock_value' => ProductStock::get()->sum('stock_value'),
            'stock_health' => $this->calculateStockHealth(),
        ];

        return response()->json($stats);
    }

    /**
     * عنصر أفضل المنتجات
     */
    public function topProductsWidget(Request $request)
    {
        $limit = $request->get('limit', 5);
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDate($period);

        $topProducts = Product::withSum(['invoiceItems' => function($query) use ($startDate) {
                $query->whereHas('invoice', function($invoiceQuery) use ($startDate) {
                    $invoiceQuery->where('invoice_type', 'sale')
                                ->where('status', 'confirmed')
                                ->where('invoice_date', '>=', $startDate);
                });
            }], 'quantity')
            ->orderBy('invoice_items_sum_quantity', 'desc')
            ->limit($limit)
            ->get(['id', 'name', 'product_code', 'selling_price']);

        return response()->json($topProducts);
    }

    /**
     * عنصر أفضل العملاء
     */
    public function topCustomersWidget(Request $request)
    {
        $limit = $request->get('limit', 5);
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDate($period);

        $topCustomers = Customer::withSum(['salesInvoices' => function($query) use ($startDate) {
                $query->where('status', 'confirmed')
                      ->where('invoice_date', '>=', $startDate);
            }], 'total_amount')
            ->orderBy('sales_invoices_sum_total_amount', 'desc')
            ->limit($limit)
            ->get(['id', 'name', 'customer_code', 'phone']);

        return response()->json($topCustomers);
    }

    /**
     * عنصر الفواتير المستحقة
     */
    public function overdueInvoicesWidget()
    {
        $overdueInvoices = [
            'sales' => Invoice::sales()
                             ->overdue()
                             ->with('customer')
                             ->orderBy('due_date')
                             ->limit(5)
                             ->get(),
            'purchases' => Invoice::purchases()
                                 ->overdue()
                                 ->with('supplier')
                                 ->orderBy('due_date')
                                 ->limit(5)
                                 ->get(),
        ];

        return response()->json($overdueInvoices);
    }

    /**
     * عنصر التنبيهات
     */
    public function alertsWidget()
    {
        $alerts = [
            'low_stock' => Product::lowStock()->count(),
            'out_of_stock' => Product::outOfStock()->count(),
            'overdue_sales' => Invoice::sales()->overdue()->count(),
            'overdue_purchases' => Invoice::purchases()->overdue()->count(),
            'pending_payments' => Payment::pending()->count(),
            'bounced_checks' => Payment::bounced()->count(),
        ];

        return response()->json($alerts);
    }

    /**
     * عنصر الأنشطة الأخيرة
     */
    public function recentActivitiesWidget(Request $request)
    {
        $limit = $request->get('limit', 10);

        $activities = collect();

        // الفواتير الأخيرة
        $recentInvoices = Invoice::with(['customer', 'supplier'])
                                ->orderBy('created_at', 'desc')
                                ->limit($limit)
                                ->get()
                                ->map(function($invoice) {
                                    return [
                                        'type' => 'invoice',
                                        'title' => $invoice->invoice_type_name . ' رقم ' . $invoice->invoice_number,
                                        'description' => $invoice->customer ?
                                            'للعميل: ' . $invoice->customer->name :
                                            'للمورد: ' . $invoice->supplier->name,
                                        'amount' => $invoice->total_amount,
                                        'date' => $invoice->created_at,
                                        'status' => $invoice->status,
                                    ];
                                });

        // المدفوعات الأخيرة
        $recentPayments = Payment::with(['customer', 'supplier'])
                                ->orderBy('created_at', 'desc')
                                ->limit($limit)
                                ->get()
                                ->map(function($payment) {
                                    return [
                                        'type' => 'payment',
                                        'title' => $payment->payment_type_name . ' رقم ' . $payment->payment_number,
                                        'description' => $payment->customer ?
                                            'من العميل: ' . $payment->customer->name :
                                            'للمورد: ' . $payment->supplier->name,
                                        'amount' => $payment->amount,
                                        'date' => $payment->created_at,
                                        'status' => $payment->status,
                                    ];
                                });

        $activities = $activities->merge($recentInvoices)
                                ->merge($recentPayments)
                                ->sortByDesc('date')
                                ->take($limit)
                                ->values();

        return response()->json($activities);
    }

    /**
     * عنصر الرسم البياني للمبيعات
     */
    public function salesChartWidget(Request $request)
    {
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDate($period);
        $groupBy = $this->getGroupByFormat($period);

        $salesData = Invoice::sales()
                           ->confirmed()
                           ->where('invoice_date', '>=', $startDate)
                           ->selectRaw("DATE_FORMAT(invoice_date, '{$groupBy}') as period, SUM(total_amount) as total")
                           ->groupBy('period')
                           ->orderBy('period')
                           ->get();

        return response()->json([
            'labels' => $salesData->pluck('period')->toArray(),
            'data' => $salesData->pluck('total')->toArray(),
        ]);
    }

    /**
     * عنصر الرسم البياني للتدفق النقدي
     */
    public function cashFlowChartWidget(Request $request)
    {
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDate($period);
        $groupBy = $this->getGroupByFormat($period);

        $receivedData = Payment::received()
                              ->cleared()
                              ->where('payment_date', '>=', $startDate)
                              ->selectRaw("DATE_FORMAT(payment_date, '{$groupBy}') as period, SUM(amount) as total")
                              ->groupBy('period')
                              ->orderBy('period')
                              ->get();

        $paidData = Payment::paid()
                          ->cleared()
                          ->where('payment_date', '>=', $startDate)
                          ->selectRaw("DATE_FORMAT(payment_date, '{$groupBy}') as period, SUM(amount) as total")
                          ->groupBy('period')
                          ->orderBy('period')
                          ->get();

        return response()->json([
            'labels' => $receivedData->pluck('period')->toArray(),
            'received' => $receivedData->pluck('total')->toArray(),
            'paid' => $paidData->pluck('total')->toArray(),
        ]);
    }

    /**
     * حساب معدل نمو المبيعات
     */
    private function calculateSalesGrowthRate($period): float
    {
        $currentPeriodStart = $this->getStartDate($period);
        $currentPeriodSales = Invoice::sales()
                                    ->confirmed()
                                    ->where('invoice_date', '>=', $currentPeriodStart)
                                    ->sum('total_amount');

        $previousPeriodStart = $this->getPreviousPeriodStart($period);
        $previousPeriodEnd = $currentPeriodStart->copy()->subDay();
        $previousPeriodSales = Invoice::sales()
                                     ->confirmed()
                                     ->whereBetween('invoice_date', [$previousPeriodStart, $previousPeriodEnd])
                                     ->sum('total_amount');

        if ($previousPeriodSales > 0) {
            return (($currentPeriodSales - $previousPeriodSales) / $previousPeriodSales) * 100;
        }

        return 0;
    }

    /**
     * حساب صافي التدفق النقدي
     */
    private function calculateNetCashFlow($startDate, $endDate): float
    {
        $received = Payment::received()
                          ->cleared()
                          ->whereBetween('payment_date', [$startDate, $endDate])
                          ->sum('amount');

        $paid = Payment::paid()
                      ->cleared()
                      ->whereBetween('payment_date', [$startDate, $endDate])
                      ->sum('amount');

        return $received - $paid;
    }

    /**
     * حساب صحة المخزون
     */
    private function calculateStockHealth(): float
    {
        $totalProducts = Product::count();
        $lowStockProducts = Product::lowStock()->count();
        $outOfStockProducts = Product::outOfStock()->count();

        if ($totalProducts > 0) {
            return (($totalProducts - $lowStockProducts - $outOfStockProducts) / $totalProducts) * 100;
        }

        return 0;
    }

    /**
     * الحصول على تاريخ البداية حسب الفترة
     */
    private function getStartDate($period): Carbon
    {
        return match($period) {
            'today' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    /**
     * الحصول على تاريخ بداية الفترة السابقة
     */
    private function getPreviousPeriodStart($period): Carbon
    {
        return match($period) {
            'today' => now()->subDay()->startOfDay(),
            'week' => now()->subWeek()->startOfWeek(),
            'month' => now()->subMonth()->startOfMonth(),
            'quarter' => now()->subQuarter()->startOfQuarter(),
            'year' => now()->subYear()->startOfYear(),
            default => now()->subMonth()->startOfMonth(),
        };
    }

    /**
     * الحصول على تنسيق التجميع حسب الفترة
     */
    private function getGroupByFormat($period): string
    {
        return match($period) {
            'today' => '%H:00',
            'week' => '%Y-%m-%d',
            'month' => '%Y-%m-%d',
            'quarter' => '%Y-%m',
            'year' => '%Y-%m',
            default => '%Y-%m-%d',
        };
    }
}
