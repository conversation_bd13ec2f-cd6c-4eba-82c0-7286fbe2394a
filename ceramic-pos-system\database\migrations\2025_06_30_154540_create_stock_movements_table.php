<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('warehouse_id')->constrained('warehouses')->onDelete('cascade');
            $table->string('movement_type'); // in, out, transfer, adjustment
            $table->integer('quantity'); // الكمية (موجبة للداخل، سالبة للخارج)
            $table->integer('quantity_before'); // الكمية قبل الحركة
            $table->integer('quantity_after'); // الكمية بعد الحركة
            $table->decimal('unit_cost', 10, 2)->default(0); // تكلفة الوحدة
            $table->decimal('total_cost', 15, 2)->default(0); // إجمالي التكلفة
            $table->string('reference_type')->nullable(); // نوع المرجع (invoice, purchase_order, etc.)
            $table->unsignedBigInteger('reference_id')->nullable(); // معرف المرجع
            $table->string('reference_number')->nullable(); // رقم المرجع
            $table->foreignId('from_warehouse_id')->nullable()->constrained('warehouses')->onDelete('set null'); // للتحويلات
            $table->foreignId('to_warehouse_id')->nullable()->constrained('warehouses')->onDelete('set null'); // للتحويلات
            $table->text('notes')->nullable(); // ملاحظات
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade'); // المستخدم الذي أنشأ الحركة
            $table->timestamps();

            $table->index(['tenant_id', 'product_id']);
            $table->index(['tenant_id', 'warehouse_id']);
            $table->index(['tenant_id', 'movement_type']);
            $table->index(['tenant_id', 'created_at']);
            $table->index(['reference_type', 'reference_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_movements');
    }
};
