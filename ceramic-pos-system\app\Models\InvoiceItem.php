<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Traits\BelongsToTenant;

class InvoiceItem extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'invoice_id',
        'product_id',
        'product_code',
        'product_name',
        'product_description',
        'unit',
        'quantity',
        'unit_price',
        'discount_amount',
        'discount_percentage',
        'tax_amount',
        'tax_percentage',
        'total_amount',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'quantity' => 'decimal:3',
        'unit_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'tax_percentage' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * العلاقة مع الفاتورة
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * العلاقة مع المنتج
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * حساب إجمالي البند
     */
    public function calculateTotal(): void
    {
        // المبلغ الأساسي
        $baseAmount = $this->quantity * $this->unit_price;

        // خصم البند
        if ($this->discount_percentage > 0) {
            $this->discount_amount = $baseAmount * ($this->discount_percentage / 100);
        }

        // المبلغ بعد الخصم
        $amountAfterDiscount = $baseAmount - $this->discount_amount;

        // ضريبة البند
        if ($this->tax_percentage > 0) {
            $this->tax_amount = $amountAfterDiscount * ($this->tax_percentage / 100);
        }

        // الإجمالي
        $this->total_amount = $amountAfterDiscount + $this->tax_amount;

        $this->save();
    }

    /**
     * الحصول على المبلغ الأساسي
     */
    public function getBaseAmountAttribute(): float
    {
        return $this->quantity * $this->unit_price;
    }

    /**
     * الحصول على المبلغ بعد الخصم
     */
    public function getAmountAfterDiscountAttribute(): float
    {
        return $this->base_amount - $this->discount_amount;
    }

    /**
     * تحديث بيانات المنتج من المنتج الأصلي
     */
    public function updateFromProduct(): void
    {
        if ($this->product) {
            $this->product_code = $this->product->product_code;
            $this->product_name = $this->product->name;
            $this->product_description = $this->product->description;
            $this->unit = $this->product->unit;

            // تحديد السعر حسب نوع الفاتورة
            if ($this->invoice->isSale()) {
                $this->unit_price = $this->product->selling_price;
            } else {
                $this->unit_price = $this->product->purchase_price;
            }

            $this->save();
        }
    }

    /**
     * التحقق من توفر الكمية في المخزون (للبيع)
     */
    public function checkStockAvailability(): bool
    {
        if (!$this->product_id || !$this->invoice->warehouse_id || !$this->invoice->isSale()) {
            return true; // لا نحتاج للتحقق
        }

        $availableQuantity = $this->invoice->warehouse->getProductAvailableQuantity($this->product_id);
        return $availableQuantity >= $this->quantity;
    }

    /**
     * الحصول على الكمية المتاحة في المخزون
     */
    public function getAvailableStockAttribute(): int
    {
        if (!$this->product_id || !$this->invoice->warehouse_id) {
            return 0;
        }

        return $this->invoice->warehouse->getProductAvailableQuantity($this->product_id);
    }
}
