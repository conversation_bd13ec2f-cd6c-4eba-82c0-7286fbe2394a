{"version": 3, "file": "index.js", "sources": ["../src/props.ts", "../src/utils.ts", "../src/chart.ts", "../src/typedCharts.ts"], "sourcesContent": ["import type { PropType } from 'vue'\nimport type {\n  ChartType,\n  ChartData,\n  ChartOptions,\n  Plugin,\n  UpdateMode\n} from 'chart.js'\n\nexport const CommonProps = {\n  data: {\n    type: Object as PropType<ChartData>,\n    required: true\n  },\n  options: {\n    type: Object as PropType<ChartOptions>,\n    default: () => ({})\n  },\n  plugins: {\n    type: Array as PropType<Plugin[]>,\n    default: () => []\n  },\n  datasetIdKey: {\n    type: String,\n    default: 'label'\n  },\n  updateMode: {\n    type: String as PropType<UpdateMode>,\n    default: undefined\n  }\n} as const\n\nexport const A11yProps = {\n  ariaLabel: {\n    type: String\n  },\n  ariaDescribedby: {\n    type: String\n  }\n} as const\n\nexport const Props = {\n  type: {\n    type: String as PropType<ChartType>,\n    required: true\n  },\n  destroyDelay: {\n    type: Number,\n    default: 0 // No delay by default\n  },\n  ...CommonProps,\n  ...A11yProps\n} as const\n", "import { isProxy, toRaw, version } from 'vue'\nimport type {\n  Chart,\n  ChartType,\n  ChartData,\n  ChartDataset,\n  ChartOptions,\n  DefaultDataPoint\n} from 'chart.js'\n\nexport const compatProps =\n  version[0] === '2'\n    ? <I extends {}, T extends {}>(internals: I, props: T) =>\n        Object.assign(internals, { attrs: props }) as unknown as I & T\n    : <I extends {}, T extends {}>(internals: I, props: T) =>\n        Object.assign(internals, props)\n\nexport function toRawIfProxy<T>(obj: T) {\n  return isProxy(obj) ? toRaw(obj) : obj\n}\n\nexport function cloneProxy<T extends object>(obj: T, src = obj) {\n  return isProxy(src) ? new Proxy(obj, {}) : obj\n}\n\nexport function setOptions<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(chart: Chart<TType, TData, TLabel>, nextOptions: ChartOptions<TType>) {\n  const options = chart.options\n\n  if (options && nextOptions) {\n    Object.assign(options, nextOptions)\n  }\n}\n\nexport function setLabels<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(\n  currentData: ChartData<TType, TData, TLabel>,\n  nextLabels: TLabel[] | undefined\n) {\n  currentData.labels = nextLabels\n}\n\nexport function setDatasets<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(\n  currentData: ChartData<TType, TData, TLabel>,\n  nextDatasets: ChartDataset<TType, TData>[],\n  datasetIdKey: string\n) {\n  const addedDatasets: ChartDataset<TType, TData>[] = []\n\n  currentData.datasets = nextDatasets.map(\n    (nextDataset: Record<string, unknown>) => {\n      // given the new set, find it's current match\n      const currentDataset = currentData.datasets.find(\n        (dataset: Record<string, unknown>) =>\n          dataset[datasetIdKey] === nextDataset[datasetIdKey]\n      )\n\n      // There is no original to update, so simply add new one\n      if (\n        !currentDataset ||\n        !nextDataset.data ||\n        addedDatasets.includes(currentDataset)\n      ) {\n        return { ...nextDataset } as ChartDataset<TType, TData>\n      }\n\n      addedDatasets.push(currentDataset)\n\n      Object.assign(currentDataset, nextDataset)\n\n      return currentDataset\n    }\n  )\n}\n\nexport function cloneData<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(data: ChartData<TType, TData, TLabel>, datasetIdKey: string) {\n  const nextData: ChartData<TType, TData, TLabel> = {\n    labels: [],\n    datasets: []\n  }\n\n  setLabels(nextData, data.labels)\n  setDatasets(nextData, data.datasets, datasetIdKey)\n\n  return nextData\n}\n\n/**\n * Get dataset from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getDatasetAtEvent(chart: Chart, event: MouseEvent) {\n  return chart.getElementsAtEventForMode(\n    event,\n    'dataset',\n    { intersect: true },\n    false\n  )\n}\n\n/**\n * Get single dataset element from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getElementAtEvent(chart: Chart, event: MouseEvent) {\n  return chart.getElementsAtEventForMode(\n    event,\n    'nearest',\n    { intersect: true },\n    false\n  )\n}\n\n/**\n * Get all dataset elements from mouse click event\n * @param chart - Chart.js instance\n * @param event - Mouse click event\n * @returns Dataset\n */\nexport function getElementsAtEvent(chart: Chart, event: MouseEvent) {\n  return chart.getElementsAtEventForMode(\n    event,\n    'index',\n    { intersect: true },\n    false\n  )\n}\n", "import { Chart as ChartJS } from 'chart.js'\nimport {\n  defineComponent,\n  h,\n  nextTick,\n  onUnmounted,\n  onMounted,\n  ref,\n  shallowRef,\n  toRaw,\n  watch\n} from 'vue'\n\nimport type { ChartComponent } from './types.js'\nimport { Props } from './props.js'\nimport {\n  cloneData,\n  setLabels,\n  setDatasets,\n  setOptions,\n  toRawIfProxy,\n  cloneProxy\n} from './utils.js'\n\nexport const Chart = defineComponent({\n  props: Props,\n  setup(props, { expose, slots }) {\n    const canvasRef = ref<HTMLCanvasElement | null>(null)\n    const chartRef = shallowRef<ChartJS | null>(null)\n\n    expose({ chart: chartRef })\n\n    const renderChart = () => {\n      if (!canvasRef.value) return\n\n      const { type, data, options, plugins, datasetIdKey } = props\n      const clonedData = cloneData(data, datasetIdKey)\n      const proxiedData = cloneProxy(clonedData, data)\n\n      chartRef.value = new ChartJS(canvasRef.value, {\n        type,\n        data: proxiedData,\n        options: { ...options },\n        plugins\n      })\n    }\n\n    const destroyChart = () => {\n      const chart = toRaw(chartRef.value)\n\n      if (chart) {\n        if (props.destroyDelay > 0) {\n          setTimeout(() => {\n            chart.destroy()\n            chartRef.value = null\n          }, props.destroyDelay)\n        } else {\n          chart.destroy()\n          chartRef.value = null\n        }\n      }\n    }\n\n    const update = (chart: ChartJS) => {\n      chart.update(props.updateMode)\n    }\n\n    onMounted(renderChart)\n\n    onUnmounted(destroyChart)\n\n    watch(\n      [() => props.options, () => props.data],\n      (\n        [nextOptionsProxy, nextDataProxy],\n        [prevOptionsProxy, prevDataProxy]\n      ) => {\n        const chart = toRaw(chartRef.value)\n\n        if (!chart) {\n          return\n        }\n\n        let shouldUpdate = false\n\n        if (nextOptionsProxy) {\n          const nextOptions = toRawIfProxy(nextOptionsProxy)\n          const prevOptions = toRawIfProxy(prevOptionsProxy)\n\n          if (nextOptions && nextOptions !== prevOptions) {\n            setOptions(chart, nextOptions)\n            shouldUpdate = true\n          }\n        }\n\n        if (nextDataProxy) {\n          const nextLabels = toRawIfProxy(nextDataProxy.labels)\n          const prevLabels = toRawIfProxy(prevDataProxy.labels)\n          const nextDatasets = toRawIfProxy(nextDataProxy.datasets)\n          const prevDatasets = toRawIfProxy(prevDataProxy.datasets)\n\n          if (nextLabels !== prevLabels) {\n            setLabels(chart.config.data, nextLabels)\n            shouldUpdate = true\n          }\n\n          if (nextDatasets && nextDatasets !== prevDatasets) {\n            setDatasets(chart.config.data, nextDatasets, props.datasetIdKey)\n            shouldUpdate = true\n          }\n        }\n\n        if (shouldUpdate) {\n          nextTick(() => {\n            update(chart)\n          })\n        }\n      },\n      { deep: true }\n    )\n\n    return () => {\n      return h(\n        'canvas',\n        {\n          role: 'img',\n          ariaLabel: props.ariaLabel,\n          ariaDescribedby: props.ariaDescribedby,\n          ref: canvasRef\n        },\n        [h('p', {}, [slots.default ? slots.default() : ''])]\n      )\n    }\n  }\n}) as ChartComponent\n", "import { defineComponent, shallowRef, h } from 'vue'\nimport type { ChartType, ChartComponentLike, DefaultDataPoint } from 'chart.js'\nimport {\n  Chart as ChartJ<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>er,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON>roller,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Polar<PERSON>rea<PERSON>ontroller,\n  RadarController,\n  ScatterController\n} from 'chart.js'\nimport type { DistributiveArray } from 'chart.js/dist/types/utils'\nimport type { TypedChartComponent, ChartComponentRef } from './types.js'\nimport { CommonProps } from './props.js'\nimport { Chart } from './chart.js'\nimport { compatProps } from './utils.js'\n\nexport function createTypedChart<\n  TType extends ChartType = ChartType,\n  TData = DefaultDataPoint<TType>,\n  TLabel = unknown\n>(\n  type: TType,\n  registerables: ChartComponentLike\n): TypedChartComponent<TType, TData, TLabel> {\n  ChartJS.register(registerables)\n\n  return defineComponent({\n    props: CommonProps,\n    setup(props, { expose }) {\n      const ref = shallowRef<ChartJS | null>(null)\n      const reforwardRef = (chartRef: ChartComponentRef) => {\n        ref.value = chartRef?.chart\n      }\n\n      expose({ chart: ref })\n\n      return () => {\n        return h(\n          Chart,\n          compatProps(\n            {\n              ref: reforwardRef as any\n            },\n            {\n              type,\n              ...props\n            }\n          )\n        )\n      }\n    }\n  }) as any\n}\n\nexport interface ExtendedDataPoint {\n  [key: string]: string | number | null | ExtendedDataPoint\n}\n\nexport const Bar = /* #__PURE__ */ createTypedChart<\n  'bar',\n  DefaultDataPoint<'bar'> | DistributiveArray<ExtendedDataPoint>\n>('bar', BarController)\n\nexport const Doughnut = /* #__PURE__ */ createTypedChart(\n  'doughnut',\n  DoughnutController\n)\n\nexport const Line = /* #__PURE__ */ createTypedChart('line', LineController)\n\nexport const Pie = /* #__PURE__ */ createTypedChart('pie', PieController)\n\nexport const PolarArea = /* #__PURE__ */ createTypedChart(\n  'polarArea',\n  PolarAreaController\n)\n\nexport const Radar = /* #__PURE__ */ createTypedChart('radar', RadarController)\n\nexport const Bubble = /* #__PURE__ */ createTypedChart(\n  'bubble',\n  BubbleController\n)\n\nexport const Scatter = /* #__PURE__ */ createTypedChart(\n  'scatter',\n  ScatterController\n)\n"], "names": ["CommonProps", "data", "type", "Object", "required", "options", "default", "plugins", "Array", "datasetIdKey", "String", "updateMode", "undefined", "A11yProps", "aria<PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "Props", "destroyDelay", "Number", "compatProps", "version", "internals", "props", "assign", "attrs", "toRawIfProxy", "obj", "isProxy", "toRaw", "cloneProxy", "src", "Proxy", "setOptions", "chart", "nextOptions", "<PERSON><PERSON><PERSON><PERSON>", "currentData", "<PERSON><PERSON><PERSON><PERSON>", "labels", "setDatasets", "nextDatasets", "addedDatasets", "datasets", "map", "nextDataset", "currentDataset", "find", "dataset", "includes", "push", "cloneData", "nextData", "getDatasetAtEvent", "event", "getElementsAtEventForMode", "intersect", "getElementAtEvent", "getElementsAtEvent", "Chart", "defineComponent", "setup", "expose", "slots", "canvasRef", "ref", "chartRef", "shallowRef", "<PERSON><PERSON><PERSON>", "value", "clonedData", "proxiedData", "ChartJS", "destroy<PERSON>hart", "setTimeout", "destroy", "update", "onMounted", "onUnmounted", "watch", "nextOptionsProxy", "nextDataProxy", "prevOptionsProxy", "prevDataProxy", "shouldUpdate", "prevOptions", "prev<PERSON><PERSON><PERSON>", "prevDatasets", "config", "nextTick", "deep", "h", "role", "createTypedChart", "registerables", "register", "reforwardRef", "Bar", "BarController", "Doughnut", "DoughnutController", "Line", "LineController", "Pie", "PieController", "PolarArea", "PolarAreaController", "Radar", "RadarController", "Bubble", "BubbleController", "<PERSON><PERSON><PERSON>", "ScatterController"], "mappings": ";;;AASO,MAAMA,WAAc,GAAA;IACzBC,IAAM,EAAA;QACJC,IAAMC,EAAAA,MAAAA;AACNC,QAAAA,QAAAA,EAAU,IAAI;AAChB,KAAA;IACAC,OAAS,EAAA;QACPH,IAAMC,EAAAA,MAAAA;QACNG,OAAS,EAAA,KAAO,EAAC,CAAA;AACnB,KAAA;IACAC,OAAS,EAAA;QACPL,IAAMM,EAAAA,KAAAA;AACNF,QAAAA,OAAAA,EAAS,IAAM,EAAE;AACnB,KAAA;IACAG,YAAc,EAAA;QACZP,IAAMQ,EAAAA,MAAAA;QACNJ,OAAS,EAAA,OAAA;AACX,KAAA;IACAK,UAAY,EAAA;QACVT,IAAMQ,EAAAA,MAAAA;QACNJ,OAASM,EAAAA,SAAAA;AACX,KAAA;AACF,CAAU,CAAA;AAEH,MAAMC,SAAY,GAAA;IACvBC,SAAW,EAAA;QACTZ,IAAMQ,EAAAA,MAAAA;AACR,KAAA;IACAK,eAAiB,EAAA;QACfb,IAAMQ,EAAAA,MAAAA;AACR,KAAA;AACF,CAAU,CAAA;AAEH,MAAMM,KAAQ,GAAA;IACnBd,IAAM,EAAA;QACJA,IAAMQ,EAAAA,MAAAA;AACNN,QAAAA,QAAAA,EAAU,IAAI;AAChB,KAAA;IACAa,YAAc,EAAA;QACZf,IAAMgB,EAAAA,MAAAA;AACNZ,QAAAA,OAAAA,EAAS;AACX,KAAA;AACA,IAAA,GAAGN,WAAW;AACd,IAAA,GAAGa,SAAS;AACd,CAAU;;AC1CH,MAAMM,WAAAA,GACXC,OAAO,CAAC,CAAE,CAAA,KAAK,GACX,GAAA,CAA6BC,SAAcC,EAAAA,KAAAA,GACzCnB,MAAOoB,CAAAA,MAAM,CAACF,SAAW,EAAA;QAAEG,KAAOF,EAAAA,KAAAA;KACpC,CAAA,GAAA,CAA6BD,WAAcC,KACzCnB,GAAAA,MAAAA,CAAOoB,MAAM,CAACF,SAAAA,EAAWC,MAAM,CAAA;AAEhC,SAASG,YAAgBC,CAAAA,GAAM,EAAE;AACtC,IAAA,OAAOC,OAAQD,CAAAA,GAAAA,CAAAA,GAAOE,KAAMF,CAAAA,GAAAA,CAAAA,GAAOA,GAAG,CAAA;AACxC,CAAC;AAEM,SAASG,UAA6BH,CAAAA,GAAM,EAAa;AAAXI,IAAAA,IAAAA,GAAAA,GAAAA,iEAAMJ,GAAG,CAAA;AAC5D,IAAA,OAAOC,QAAQG,GAAO,CAAA,GAAA,IAAIC,MAAML,GAAK,EAAA,MAAMA,GAAG,CAAA;AAChD,CAAC;AAEM,SAASM,UAAAA,CAIdC,KAAkC,EAAEC,WAAgC,EAAE;IACtE,MAAM7B,OAAAA,GAAU4B,MAAM5B,OAAO,CAAA;AAE7B,IAAA,IAAIA,WAAW6B,WAAa,EAAA;QAC1B/B,MAAOoB,CAAAA,MAAM,CAAClB,OAAS6B,EAAAA,WAAAA,CAAAA,CAAAA;KACxB;AACH,CAAC;AAEM,SAASC,SAAAA,CAKdC,WAA4C,EAC5CC,UAAgC,EAChC;AACAD,IAAAA,WAAAA,CAAYE,MAAM,GAAGD,UAAAA,CAAAA;AACvB,CAAC;AAEM,SAASE,WAKdH,CAAAA,WAA4C,EAC5CI,YAA0C,EAC1C/B,YAAoB,EACpB;AACA,IAAA,MAAMgC,gBAA8C,EAAE,CAAA;AAEtDL,IAAAA,WAAAA,CAAYM,QAAQ,GAAGF,YAAAA,CAAaG,GAAG,CACrC,CAACC,WAAyC,GAAA;;AAExC,QAAA,MAAMC,cAAiBT,GAAAA,WAAAA,CAAYM,QAAQ,CAACI,IAAI,CAC9C,CAACC,OACCA,GAAAA,OAAO,CAACtC,YAAAA,CAAa,KAAKmC,WAAW,CAACnC,YAAa,CAAA,CAAA,CAAA;;QAIvD,IACE,CAACoC,kBACD,CAACD,WAAAA,CAAY3C,IAAI,IACjBwC,aAAAA,CAAcO,QAAQ,CAACH,cACvB,CAAA,EAAA;YACA,OAAO;AAAE,gBAAA,GAAGD,WAAW;AAAC,aAAA,CAAA;SACzB;AAEDH,QAAAA,aAAAA,CAAcQ,IAAI,CAACJ,cAAAA,CAAAA,CAAAA;QAEnB1C,MAAOoB,CAAAA,MAAM,CAACsB,cAAgBD,EAAAA,WAAAA,CAAAA,CAAAA;QAE9B,OAAOC,cAAAA,CAAAA;AACT,KAAA,CAAA,CAAA;AAEJ,CAAC;AAEM,SAASK,SAAAA,CAIdjD,IAAqC,EAAEQ,YAAoB,EAAE;AAC7D,IAAA,MAAM0C,QAA4C,GAAA;AAChDb,QAAAA,MAAAA,EAAQ,EAAE;AACVI,QAAAA,QAAAA,EAAU,EAAE;AACd,KAAA,CAAA;IAEAP,SAAUgB,CAAAA,QAAAA,EAAUlD,KAAKqC,MAAM,CAAA,CAAA;IAC/BC,WAAYY,CAAAA,QAAAA,EAAUlD,IAAKyC,CAAAA,QAAQ,EAAEjC,YAAAA,CAAAA,CAAAA;IAErC,OAAO0C,QAAAA,CAAAA;AACT,CAAC;AAED;;;;;AAKC,IACM,SAASC,iBAAAA,CAAkBnB,KAAY,EAAEoB,KAAiB,EAAE;AACjE,IAAA,OAAOpB,KAAMqB,CAAAA,yBAAyB,CACpCD,KAAAA,EACA,SACA,EAAA;AAAEE,QAAAA,SAAAA,EAAW,IAAI;AAAC,KAAA,EAClB,KAAK,CAAA,CAAA;AAET,CAAC;AAED;;;;;AAKC,IACM,SAASC,iBAAAA,CAAkBvB,KAAY,EAAEoB,KAAiB,EAAE;AACjE,IAAA,OAAOpB,KAAMqB,CAAAA,yBAAyB,CACpCD,KAAAA,EACA,SACA,EAAA;AAAEE,QAAAA,SAAAA,EAAW,IAAI;AAAC,KAAA,EAClB,KAAK,CAAA,CAAA;AAET,CAAC;AAED;;;;;AAKC,IACM,SAASE,kBAAAA,CAAmBxB,KAAY,EAAEoB,KAAiB,EAAE;AAClE,IAAA,OAAOpB,KAAMqB,CAAAA,yBAAyB,CACpCD,KAAAA,EACA,OACA,EAAA;AAAEE,QAAAA,SAAAA,EAAW,IAAI;AAAC,KAAA,EAClB,KAAK,CAAA,CAAA;AAET;;ACxHO,MAAMG,QAAQC,eAAgB,CAAA;IACnCrC,KAAON,EAAAA,KAAAA;IACP4C,KAAMtC,CAAAA,CAAAA,KAAK,EAAE,KAAiB,EAAE;AAAnB,QAAA,IAAA,EAAEuC,MAAM,GAAEC,KAAK,GAAE,GAAjB,KAAA,CAAA;QACX,MAAMC,SAAAA,GAAYC,IAA8B,IAAI,CAAA,CAAA;QACpD,MAAMC,QAAAA,GAAWC,WAA2B,IAAI,CAAA,CAAA;QAEhDL,MAAO,CAAA;YAAE5B,KAAOgC,EAAAA,QAAAA;AAAS,SAAA,CAAA,CAAA;AAEzB,QAAA,MAAME,cAAc,IAAM;YACxB,IAAI,CAACJ,SAAUK,CAAAA,KAAK,EAAE,OAAA;YAEtB,MAAM,EAAElE,IAAI,GAAED,IAAI,GAAEI,OAAO,GAAEE,OAAO,GAAEE,YAAY,GAAE,GAAGa,KAAAA,CAAAA;YACvD,MAAM+C,UAAAA,GAAanB,UAAUjD,IAAMQ,EAAAA,YAAAA,CAAAA,CAAAA;YACnC,MAAM6D,WAAAA,GAAczC,WAAWwC,UAAYpE,EAAAA,IAAAA,CAAAA,CAAAA;AAE3CgE,YAAAA,QAAAA,CAASG,KAAK,GAAG,IAAIG,OAAQR,CAAAA,SAAAA,CAAUK,KAAK,EAAE;AAC5ClE,gBAAAA,IAAAA;gBACAD,IAAMqE,EAAAA,WAAAA;gBACNjE,OAAS,EAAA;AAAE,oBAAA,GAAGA,OAAO;AAAC,iBAAA;AACtBE,gBAAAA,OAAAA;AACF,aAAA,CAAA,CAAA;AACF,SAAA,CAAA;AAEA,QAAA,MAAMiE,eAAe,IAAM;YACzB,MAAMvC,KAAAA,GAAQL,KAAMqC,CAAAA,QAAAA,CAASG,KAAK,CAAA,CAAA;AAElC,YAAA,IAAInC,KAAO,EAAA;gBACT,IAAIX,KAAAA,CAAML,YAAY,GAAG,CAAG,EAAA;AAC1BwD,oBAAAA,UAAAA,CAAW,IAAM;AACfxC,wBAAAA,KAAAA,CAAMyC,OAAO,EAAA,CAAA;wBACbT,QAASG,CAAAA,KAAK,GAAG,IAAI,CAAA;AACvB,qBAAA,EAAG9C,MAAML,YAAY,CAAA,CAAA;iBAChB,MAAA;AACLgB,oBAAAA,KAAAA,CAAMyC,OAAO,EAAA,CAAA;oBACbT,QAASG,CAAAA,KAAK,GAAG,IAAI,CAAA;iBACtB;aACF;AACH,SAAA,CAAA;QAEA,MAAMO,MAAAA,GAAS,CAAC1C,KAAmB,GAAA;YACjCA,KAAM0C,CAAAA,MAAM,CAACrD,KAAAA,CAAMX,UAAU,CAAA,CAAA;AAC/B,SAAA,CAAA;QAEAiE,SAAUT,CAAAA,WAAAA,CAAAA,CAAAA;QAEVU,WAAYL,CAAAA,YAAAA,CAAAA,CAAAA;QAEZM,KACE,CAAA;AAAC,YAAA,IAAMxD,MAAMjB,OAAO;AAAE,YAAA,IAAMiB,MAAMrB,IAAI;AAAC,SAAA,EACvC,CAGK,KAAA,EAAA,MAAA,GAAA;AAFH,YAAA,IAAA,CAAC8E,gBAAkBC,EAAAA,aAAAA,CAAc,GACjC,KAAA,EAAA,CAACC,kBAAkBC,aAAc,CAAA,GAAA,MAAA,CAAA;YAEjC,MAAMjD,KAAAA,GAAQL,KAAMqC,CAAAA,QAAAA,CAASG,KAAK,CAAA,CAAA;AAElC,YAAA,IAAI,CAACnC,KAAO,EAAA;AACV,gBAAA,OAAA;aACD;AAED,YAAA,IAAIkD,eAAe,KAAK,CAAA;AAExB,YAAA,IAAIJ,gBAAkB,EAAA;AACpB,gBAAA,MAAM7C,cAAcT,YAAasD,CAAAA,gBAAAA,CAAAA,CAAAA;AACjC,gBAAA,MAAMK,cAAc3D,YAAawD,CAAAA,gBAAAA,CAAAA,CAAAA;gBAEjC,IAAI/C,WAAAA,IAAeA,gBAAgBkD,WAAa,EAAA;AAC9CpD,oBAAAA,UAAAA,CAAWC,KAAOC,EAAAA,WAAAA,CAAAA,CAAAA;AAClBiD,oBAAAA,YAAAA,GAAe,IAAI,CAAA;iBACpB;aACF;AAED,YAAA,IAAIH,aAAe,EAAA;gBACjB,MAAM3C,UAAAA,GAAaZ,YAAauD,CAAAA,aAAAA,CAAc1C,MAAM,CAAA,CAAA;gBACpD,MAAM+C,UAAAA,GAAa5D,YAAayD,CAAAA,aAAAA,CAAc5C,MAAM,CAAA,CAAA;gBACpD,MAAME,YAAAA,GAAef,YAAauD,CAAAA,aAAAA,CAActC,QAAQ,CAAA,CAAA;gBACxD,MAAM4C,YAAAA,GAAe7D,YAAayD,CAAAA,aAAAA,CAAcxC,QAAQ,CAAA,CAAA;AAExD,gBAAA,IAAIL,eAAegD,UAAY,EAAA;AAC7BlD,oBAAAA,SAAAA,CAAUF,KAAMsD,CAAAA,MAAM,CAACtF,IAAI,EAAEoC,UAAAA,CAAAA,CAAAA;AAC7B8C,oBAAAA,YAAAA,GAAe,IAAI,CAAA;iBACpB;gBAED,IAAI3C,YAAAA,IAAgBA,iBAAiB8C,YAAc,EAAA;AACjD/C,oBAAAA,WAAAA,CAAYN,MAAMsD,MAAM,CAACtF,IAAI,EAAEuC,YAAAA,EAAclB,MAAMb,YAAY,CAAA,CAAA;AAC/D0E,oBAAAA,YAAAA,GAAe,IAAI,CAAA;iBACpB;aACF;AAED,YAAA,IAAIA,YAAc,EAAA;AAChBK,gBAAAA,QAAAA,CAAS,IAAM;oBACbb,MAAO1C,CAAAA,KAAAA,CAAAA,CAAAA;AACT,iBAAA,CAAA,CAAA;aACD;SAEH,EAAA;AAAEwD,YAAAA,IAAAA,EAAM,IAAI;AAAC,SAAA,CAAA,CAAA;AAGf,QAAA,OAAO,IAAM;AACX,YAAA,OAAOC,EACL,QACA,EAAA;gBACEC,IAAM,EAAA,KAAA;AACN7E,gBAAAA,SAAAA,EAAWQ,MAAMR,SAAS;AAC1BC,gBAAAA,eAAAA,EAAiBO,MAAMP,eAAe;gBACtCiD,GAAKD,EAAAA,SAAAA;aAEP,EAAA;gBAAC2B,CAAE,CAAA,GAAA,EAAK,EAAI,EAAA;AAAC5B,oBAAAA,KAAAA,CAAMxD,OAAO,GAAGwD,KAAMxD,CAAAA,OAAO,KAAK,EAAE;AAAC,iBAAA,CAAA;AAAE,aAAA,CAAA,CAAA;AAExD,SAAA,CAAA;AACF,KAAA;AACF,CAAoB;;ACnHb,SAASsF,gBAAAA,CAKd1F,IAAW,EACX2F,aAAiC,EACU;AAC3CtB,IAAAA,OAAAA,CAAQuB,QAAQ,CAACD,aAAAA,CAAAA,CAAAA;AAEjB,IAAA,OAAOlC,eAAgB,CAAA;QACrBrC,KAAOtB,EAAAA,WAAAA;QACP4D,KAAMtC,CAAAA,CAAAA,KAAK,EAAE,KAAU,EAAE;gBAAZ,EAAEuC,MAAAA,GAAQ,GAAV,KAAA,CAAA;YACX,MAAMG,GAAAA,GAAME,WAA2B,IAAI,CAAA,CAAA;YAC3C,MAAM6B,YAAAA,GAAe,CAAC9B,QAAgC,GAAA;gBACpDD,GAAII,CAAAA,KAAK,GAAGH,QAAUhC,EAAAA,KAAAA,CAAAA;AACxB,aAAA,CAAA;YAEA4B,MAAO,CAAA;gBAAE5B,KAAO+B,EAAAA,GAAAA;AAAI,aAAA,CAAA,CAAA;AAEpB,YAAA,OAAO,IAAM;gBACX,OAAO0B,CAAAA,CACLhC,OACAvC,WACE,CAAA;oBACE6C,GAAK+B,EAAAA,YAAAA;iBAEP,EAAA;AACE7F,oBAAAA,IAAAA;AACA,oBAAA,GAAGoB,KAAK;AACV,iBAAA,CAAA,CAAA,CAAA;AAGN,aAAA,CAAA;AACF,SAAA;AACF,KAAA,CAAA,CAAA;AACF,CAAC;MAMY0E,GAAM,mBAAgBJ,gBAAAA,CAGjC,OAAOK,aAAc,EAAA;MAEVC,QAAW,mBAAgBN,gBAAAA,CACtC,YACAO,kBACD,EAAA;MAEYC,IAAO,mBAAgBR,gBAAAA,CAAiB,QAAQS,cAAe,EAAA;MAE/DC,GAAM,mBAAgBV,gBAAAA,CAAiB,OAAOW,aAAc,EAAA;MAE5DC,SAAY,mBAAgBZ,gBAAAA,CACvC,aACAa,mBACD,EAAA;MAEYC,KAAQ,mBAAgBd,gBAAAA,CAAiB,SAASe,eAAgB,EAAA;MAElEC,MAAS,mBAAgBhB,gBAAAA,CACpC,UACAiB,gBACD,EAAA;MAEYC,OAAU,mBAAgBlB,gBAAAA,CACrC,WACAmB,iBACD;;;;"}