<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\SimpleTenant;

class CustomerController extends Controller
{
    /**
     * عرض قائمة العملاء
     */
    public function index(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $query = Customer::query();

        // البحث
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // فلترة حسب النوع
        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        $customers = $query->paginate(20);

        return view('customers.index', compact('customers', 'tenant'));
    }

    /**
     * عرض نموذج إضافة عميل جديد
     */
    public function create(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        return view('customers.create', compact('tenant'));
    }

    /**
     * حفظ عميل جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'type' => 'required|in:individual,company',
            'tax_number' => 'nullable|string|max:50',
            'credit_limit' => 'nullable|numeric|min:0',
        ], [
            'name.required' => 'اسم العميل مطلوب',
            'phone.required' => 'رقم الهاتف مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'type.required' => 'نوع العميل مطلوب',
        ]);

        Customer::create($request->all());

        return redirect()->route('customers.index', ['tenant' => $request->get('tenant')])
            ->with('success', 'تم إضافة العميل بنجاح');
    }

    /**
     * عرض تفاصيل العميل
     */
    public function show(Request $request, Customer $customer)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        // جلب فواتير العميل
        $invoices = $customer->invoices()->latest()->take(10)->get();

        return view('customers.show', compact('customer', 'tenant', 'invoices'));
    }

    /**
     * عرض نموذج تعديل العميل
     */
    public function edit(Request $request, Customer $customer)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        return view('customers.edit', compact('customer', 'tenant'));
    }

    /**
     * تحديث العميل
     */
    public function update(Request $request, Customer $customer)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'type' => 'required|in:individual,company',
            'tax_number' => 'nullable|string|max:50',
            'credit_limit' => 'nullable|numeric|min:0',
        ]);

        $customer->update($request->all());

        return redirect()->route('customers.index', ['tenant' => $request->get('tenant')])
            ->with('success', 'تم تحديث العميل بنجاح');
    }

    /**
     * حذف العميل
     */
    public function destroy(Request $request, Customer $customer)
    {
        // التحقق من وجود فواتير للعميل
        if ($customer->invoices()->count() > 0) {
            return redirect()->route('customers.index', ['tenant' => $request->get('tenant')])
                ->with('error', 'لا يمكن حذف العميل لوجود فواتير مرتبطة به');
        }

        $customer->delete();

        return redirect()->route('customers.index', ['tenant' => $request->get('tenant')])
            ->with('success', 'تم حذف العميل بنجاح');
    }
}
