<?php

namespace App\Services;

use App\Models\ChartOfAccount;
use App\Models\Tenant;
use Illuminate\Support\Facades\DB;

class ChartOfAccountService
{
    /**
     * إنشاء دليل الحسابات الأساسي للشركة الجديدة
     */
    public function createDefaultChartOfAccounts(Tenant $tenant): void
    {
        $accounts = $this->getDefaultAccounts($tenant->id);
        
        DB::beginTransaction();
        
        try {
            $createdAccounts = [];
            
            // إنشاء الحسابات بالترتيب الصحيح
            foreach ($accounts as $accountData) {
                // تحديث parent_id إذا كان موجود
                if ($accountData['parent_id'] && isset($createdAccounts[$accountData['parent_id']])) {
                    $accountData['parent_id'] = $createdAccounts[$accountData['parent_id']]->id;
                } else {
                    $accountData['parent_id'] = null;
                }
                
                $account = ChartOfAccount::create($accountData);
                $createdAccounts[$accountData['temp_id'] ?? $account->id] = $account;
                
                // تحديث المستوى والمسار
                $account->updateLevel();
                $account->updatePath();
            }
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
    
    /**
     * الحصول على الحسابات الافتراضية
     */
    private function getDefaultAccounts(int $tenantId): array
    {
        return [
            // الأصول المتداولة
            [
                'temp_id' => 1,
                'tenant_id' => $tenantId,
                'account_code' => '1000',
                'account_name' => 'الأصول المتداولة',
                'account_name_en' => 'Current Assets',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 2,
                'tenant_id' => $tenantId,
                'account_code' => '1010',
                'account_name' => 'النقدية والبنوك',
                'account_name_en' => 'Cash and Banks',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 1,
                'level' => 2,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 3,
                'tenant_id' => $tenantId,
                'account_code' => '1011',
                'account_name' => 'الخزينة',
                'account_name_en' => 'Cash',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 2,
                'level' => 3,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 4,
                'tenant_id' => $tenantId,
                'account_code' => '1012',
                'account_name' => 'البنك الأهلي المصري',
                'account_name_en' => 'National Bank of Egypt',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 2,
                'level' => 3,
                'nature' => 'debit',
                'is_system' => false,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 5,
                'tenant_id' => $tenantId,
                'account_code' => '1020',
                'account_name' => 'العملاء',
                'account_name_en' => 'Accounts Receivable',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 1,
                'level' => 2,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 6,
                'tenant_id' => $tenantId,
                'account_code' => '1030',
                'account_name' => 'المخزون',
                'account_name_en' => 'Inventory',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 1,
                'level' => 2,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            
            // الأصول الثابتة
            [
                'temp_id' => 7,
                'tenant_id' => $tenantId,
                'account_code' => '1500',
                'account_name' => 'الأصول الثابتة',
                'account_name_en' => 'Fixed Assets',
                'account_type' => 'assets',
                'account_category' => 'fixed_assets',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 8,
                'tenant_id' => $tenantId,
                'account_code' => '1510',
                'account_name' => 'الأثاث والمعدات',
                'account_name_en' => 'Furniture and Equipment',
                'account_type' => 'assets',
                'account_category' => 'fixed_assets',
                'parent_id' => 7,
                'level' => 2,
                'nature' => 'debit',
                'is_system' => false,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            
            // الخصوم المتداولة
            [
                'temp_id' => 9,
                'tenant_id' => $tenantId,
                'account_code' => '2000',
                'account_name' => 'الخصوم المتداولة',
                'account_name_en' => 'Current Liabilities',
                'account_type' => 'liabilities',
                'account_category' => 'current_liabilities',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 10,
                'tenant_id' => $tenantId,
                'account_code' => '2010',
                'account_name' => 'الموردين',
                'account_name_en' => 'Accounts Payable',
                'account_type' => 'liabilities',
                'account_category' => 'current_liabilities',
                'parent_id' => 9,
                'level' => 2,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 11,
                'tenant_id' => $tenantId,
                'account_code' => '2020',
                'account_name' => 'ضريبة القيمة المضافة',
                'account_name_en' => 'VAT Payable',
                'account_type' => 'liabilities',
                'account_category' => 'current_liabilities',
                'parent_id' => 9,
                'level' => 2,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            
            // حقوق الملكية
            [
                'temp_id' => 12,
                'tenant_id' => $tenantId,
                'account_code' => '3000',
                'account_name' => 'حقوق الملكية',
                'account_name_en' => 'Equity',
                'account_type' => 'equity',
                'account_category' => 'capital',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 13,
                'tenant_id' => $tenantId,
                'account_code' => '3010',
                'account_name' => 'رأس المال',
                'account_name_en' => 'Capital',
                'account_type' => 'equity',
                'account_category' => 'capital',
                'parent_id' => 12,
                'level' => 2,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            
            // الإيرادات
            [
                'temp_id' => 14,
                'tenant_id' => $tenantId,
                'account_code' => '4000',
                'account_name' => 'الإيرادات',
                'account_name_en' => 'Revenue',
                'account_type' => 'revenue',
                'account_category' => 'sales_revenue',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 15,
                'tenant_id' => $tenantId,
                'account_code' => '4010',
                'account_name' => 'مبيعات السيراميك',
                'account_name_en' => 'Ceramic Sales',
                'account_type' => 'revenue',
                'account_category' => 'sales_revenue',
                'parent_id' => 14,
                'level' => 2,
                'nature' => 'credit',
                'is_system' => false,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 16,
                'tenant_id' => $tenantId,
                'account_code' => '4020',
                'account_name' => 'مبيعات الأدوات الصحية',
                'account_name_en' => 'Sanitary Sales',
                'account_type' => 'revenue',
                'account_category' => 'sales_revenue',
                'parent_id' => 14,
                'level' => 2,
                'nature' => 'credit',
                'is_system' => false,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            
            // تكلفة البضاعة المباعة
            [
                'temp_id' => 17,
                'tenant_id' => $tenantId,
                'account_code' => '5000',
                'account_name' => 'تكلفة البضاعة المباعة',
                'account_name_en' => 'Cost of Goods Sold',
                'account_type' => 'cost_of_goods_sold',
                'account_category' => 'cogs',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            
            // المصروفات
            [
                'temp_id' => 18,
                'tenant_id' => $tenantId,
                'account_code' => '6000',
                'account_name' => 'المصروفات التشغيلية',
                'account_name_en' => 'Operating Expenses',
                'account_type' => 'expenses',
                'account_category' => 'operating_expenses',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 19,
                'tenant_id' => $tenantId,
                'account_code' => '6010',
                'account_name' => 'مصروفات الإيجار',
                'account_name_en' => 'Rent Expense',
                'account_type' => 'expenses',
                'account_category' => 'operating_expenses',
                'parent_id' => 18,
                'level' => 2,
                'nature' => 'debit',
                'is_system' => false,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
            [
                'temp_id' => 20,
                'tenant_id' => $tenantId,
                'account_code' => '6020',
                'account_name' => 'مصروفات الكهرباء',
                'account_name_en' => 'Electricity Expense',
                'account_type' => 'expenses',
                'account_category' => 'operating_expenses',
                'parent_id' => 18,
                'level' => 2,
                'nature' => 'debit',
                'is_system' => false,
                'is_active' => true,
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
        ];
    }
    
    /**
     * إنشاء شجرة الحسابات للعرض
     */
    public function buildAccountTree(): array
    {
        $accounts = ChartOfAccount::with('children')
            ->whereNull('parent_id')
            ->orderBy('account_code')
            ->get();
            
        return $this->formatAccountTree($accounts);
    }
    
    /**
     * تنسيق شجرة الحسابات
     */
    private function formatAccountTree($accounts): array
    {
        $tree = [];
        
        foreach ($accounts as $account) {
            $tree[] = [
                'id' => $account->id,
                'account_code' => $account->account_code,
                'account_name' => $account->account_name,
                'account_type' => $account->account_type_name,
                'nature' => $account->nature_name,
                'current_balance' => $account->current_balance,
                'is_active' => $account->is_active,
                'children' => $this->formatAccountTree($account->children),
            ];
        }
        
        return $tree;
    }
    
    /**
     * البحث في الحسابات
     */
    public function searchAccounts(string $query): array
    {
        $accounts = ChartOfAccount::search($query)
            ->active()
            ->orderBy('account_code')
            ->limit(20)
            ->get();
            
        return $accounts->map(function ($account) {
            return [
                'id' => $account->id,
                'account_code' => $account->account_code,
                'account_name' => $account->account_name,
                'full_path' => $account->full_path,
                'current_balance' => $account->current_balance,
            ];
        })->toArray();
    }
}
