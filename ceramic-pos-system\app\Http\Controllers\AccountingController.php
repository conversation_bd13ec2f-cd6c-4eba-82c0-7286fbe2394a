<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChartOfAccount;
use App\Models\Payment;
use App\Models\Invoice;
use App\Models\SimpleTenant;
use Illuminate\Support\Facades\DB;

class AccountingController extends Controller
{
    /**
     * عرض لوحة تحكم المحاسبة
     */
    public function index(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        // إحصائيات مالية
        $stats = [
            'total_sales' => Invoice::where('type', 'sale')->where('status', 'completed')->sum('total_amount'),
            'total_purchases' => Invoice::where('type', 'purchase')->where('status', 'completed')->sum('total_amount'),
            'pending_payments' => Invoice::where('payment_status', 'pending')->sum('remaining_amount'),
            'cash_balance' => Payment::where('payment_method', 'cash')->sum('amount'),
        ];

        // آخر المعاملات
        $recentTransactions = Payment::with(['invoice.customer'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // دليل الحسابات الرئيسية
        $mainAccounts = ChartOfAccount::whereNull('parent_id')
            ->with('children')
            ->get();

        return view('accounting.index', compact('tenant', 'stats', 'recentTransactions', 'mainAccounts'));
    }

    /**
     * عرض دليل الحسابات
     */
    public function chartOfAccounts(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $accounts = ChartOfAccount::with('parent', 'children')
            ->orderBy('account_code')
            ->get();

        return view('accounting.chart-of-accounts', compact('tenant', 'accounts'));
    }

    /**
     * عرض تقرير الأرباح والخسائر
     */
    public function profitLoss(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $dateFrom = $request->get('date_from', now()->startOfMonth());
        $dateTo = $request->get('date_to', now()->endOfMonth());

        // الإيرادات
        $revenues = [
            'sales' => Invoice::where('type', 'sale')
                ->where('status', 'completed')
                ->whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->sum('total_amount'),
        ];

        // المصروفات
        $expenses = [
            'purchases' => Invoice::where('type', 'purchase')
                ->where('status', 'completed')
                ->whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->sum('total_amount'),
        ];

        $totalRevenue = array_sum($revenues);
        $totalExpenses = array_sum($expenses);
        $netProfit = $totalRevenue - $totalExpenses;

        return view('accounting.profit-loss', compact(
            'tenant', 'revenues', 'expenses', 'totalRevenue',
            'totalExpenses', 'netProfit', 'dateFrom', 'dateTo'
        ));
    }

    /**
     * عرض الميزانية العمومية
     */
    public function balanceSheet(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        // الأصول
        $assets = [
            'cash' => Payment::where('payment_method', 'cash')->sum('amount'),
            'accounts_receivable' => Invoice::where('payment_status', '!=', 'paid')->sum('remaining_amount'),
            'inventory' => DB::table('products')->sum(DB::raw('purchase_price * COALESCE((SELECT SUM(quantity) FROM product_stock WHERE product_id = products.id), 0)')),
        ];

        // الخصوم
        $liabilities = [
            'accounts_payable' => Invoice::where('type', 'purchase')
                ->where('payment_status', '!=', 'paid')
                ->sum('remaining_amount'),
        ];

        // حقوق الملكية
        $equity = [
            'retained_earnings' => Invoice::where('type', 'sale')->where('status', 'completed')->sum('total_amount') -
                                 Invoice::where('type', 'purchase')->where('status', 'completed')->sum('total_amount'),
        ];

        $totalAssets = array_sum($assets);
        $totalLiabilities = array_sum($liabilities);
        $totalEquity = array_sum($equity);

        return view('accounting.balance-sheet', compact(
            'tenant', 'assets', 'liabilities', 'equity',
            'totalAssets', 'totalLiabilities', 'totalEquity'
        ));
    }

    /**
     * عرض تقرير التدفق النقدي
     */
    public function cashFlow(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $dateFrom = $request->get('date_from', now()->startOfMonth());
        $dateTo = $request->get('date_to', now()->endOfMonth());

        // التدفقات النقدية من العمليات التشغيلية
        $operatingCashFlow = [
            'cash_from_sales' => Payment::whereHas('invoice', function($q) {
                    $q->where('type', 'sale');
                })
                ->whereBetween('payment_date', [$dateFrom, $dateTo])
                ->sum('amount'),
            'cash_to_suppliers' => Payment::whereHas('invoice', function($q) {
                    $q->where('type', 'purchase');
                })
                ->whereBetween('payment_date', [$dateFrom, $dateTo])
                ->sum('amount'),
        ];

        $netOperatingCashFlow = $operatingCashFlow['cash_from_sales'] - $operatingCashFlow['cash_to_suppliers'];

        return view('accounting.cash-flow', compact(
            'tenant', 'operatingCashFlow', 'netOperatingCashFlow', 'dateFrom', 'dateTo'
        ));
    }
}
