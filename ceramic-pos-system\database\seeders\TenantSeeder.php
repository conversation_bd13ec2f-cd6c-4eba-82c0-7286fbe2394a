<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tenant;
use Stancl\Tenancy\Database\Models\Domain;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء tenant تجريبي
        $tenant = Tenant::create([
            'id' => 'demo-company',
            'data' => [
                'name' => 'شركة السيراميك التجريبية',
                'email' => '<EMAIL>',
                'phone' => '01234567890',
                'address' => 'القاهرة، مصر',
                'currency' => 'EGP',
                'timezone' => 'Africa/Cairo',
                'status' => 'active',
                'subscription_plan' => 'premium',
                'subscription_expires_at' => now()->addYear(),
            ]
        ]);

        // إنشاء domain للـ tenant
        $tenant->domains()->create([
            'domain' => 'demo.127.0.0.1.nip.io',
        ]);

        // إنشاء tenant آخر
        $tenant2 = Tenant::create([
            'id' => 'ceramic-store',
            'data' => [
                'name' => 'متجر السيراميك الحديث',
                'email' => '<EMAIL>',
                'phone' => '01098765432',
                'address' => 'الإسكندرية، مصر',
                'currency' => 'EGP',
                'timezone' => 'Africa/Cairo',
                'status' => 'active',
                'subscription_plan' => 'basic',
                'subscription_expires_at' => now()->addMonths(6),
            ]
        ]);

        // إنشاء domain للـ tenant الثاني
        $tenant2->domains()->create([
            'domain' => 'store.127.0.0.1.nip.io',
        ]);

        $this->command->info('تم إنشاء Tenants تجريبية بنجاح!');
        $this->command->info('- demo.127.0.0.1.nip.io (شركة السيراميك التجريبية)');
        $this->command->info('- store.127.0.0.1.nip.io (متجر السيراميك الحديث)');
    }
}
