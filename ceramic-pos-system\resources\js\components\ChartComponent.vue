<template>
  <div class="chart-container">
    <div v-if="loading" class="d-flex justify-content-center align-items-center" style="height: 300px;">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">جاري التحميل...</span>
      </div>
    </div>
    <canvas v-else :ref="chartRef" :height="height"></canvas>
  </div>
</template>

<script>
import { Chart, registerables } from 'chart.js'

Chart.register(...registerables)

export default {
  name: 'ChartComponent',
  props: {
    type: {
      type: String,
      default: 'line',
      validator: value => ['line', 'bar', 'pie', 'doughnut', 'area'].includes(value)
    },
    data: {
      type: Object,
      required: true
    },
    options: {
      type: Object,
      default: () => ({})
    },
    height: {
      type: Number,
      default: 300
    },
    loading: {
      type: Boolean,
      default: false
    },
    colors: {
      type: Array,
      default: () => [
        '#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1',
        '#fd7e14', '#20c997', '#6c757d', '#e83e8c', '#17a2b8'
      ]
    }
  },
  data() {
    return {
      chart: null,
      chartRef: `chart-${Math.random().toString(36).substr(2, 9)}`
    }
  },
  computed: {
    chartType() {
      return this.type === 'area' ? 'line' : this.type
    },
    chartData() {
      const data = { ...this.data }
      
      // Apply colors to datasets
      if (data.datasets) {
        data.datasets.forEach((dataset, index) => {
          const color = this.colors[index % this.colors.length]
          
          if (!dataset.backgroundColor) {
            if (this.type === 'pie' || this.type === 'doughnut') {
              dataset.backgroundColor = this.colors.slice(0, data.labels?.length || 5)
            } else if (this.type === 'area') {
              dataset.backgroundColor = color + '20'
              dataset.fill = true
            } else {
              dataset.backgroundColor = color + '20'
            }
          }
          
          if (!dataset.borderColor) {
            dataset.borderColor = color
          }
          
          if (!dataset.borderWidth) {
            dataset.borderWidth = 2
          }
        })
      }
      
      return data
    },
    chartOptions() {
      const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 20,
              font: {
                family: 'Cairo, sans-serif'
              }
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0,0,0,0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: '#ddd',
            borderWidth: 1,
            cornerRadius: 6,
            displayColors: true,
            callbacks: {
              label: (context) => {
                let label = context.dataset.label || ''
                if (label) {
                  label += ': '
                }
                label += this.$formatNumber(context.parsed.y || context.parsed)
                return label
              }
            }
          }
        },
        scales: this.getScalesConfig(),
        animation: {
          duration: 1000,
          easing: 'easeInOutQuart'
        }
      }
      
      return { ...defaultOptions, ...this.options }
    }
  },
  mounted() {
    if (!this.loading) {
      this.initChart()
    }
  },
  watch: {
    data: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    loading(newVal) {
      if (!newVal) {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.destroy()
    }
  },
  methods: {
    initChart() {
      const ctx = this.$refs[this.chartRef]
      if (!ctx) return

      this.chart = new Chart(ctx, {
        type: this.chartType,
        data: this.chartData,
        options: this.chartOptions
      })
    },
    updateChart() {
      if (!this.chart) {
        this.initChart()
        return
      }

      this.chart.data = this.chartData
      this.chart.options = this.chartOptions
      this.chart.update()
    },
    getScalesConfig() {
      if (this.type === 'pie' || this.type === 'doughnut') {
        return {}
      }

      return {
        x: {
          grid: {
            display: true,
            color: 'rgba(0,0,0,0.1)'
          },
          ticks: {
            font: {
              family: 'Cairo, sans-serif'
            }
          }
        },
        y: {
          grid: {
            display: true,
            color: 'rgba(0,0,0,0.1)'
          },
          ticks: {
            font: {
              family: 'Cairo, sans-serif'
            },
            callback: (value) => this.$formatNumber(value)
          }
        }
      }
    },
    downloadChart(filename = 'chart') {
      if (!this.chart) return

      const url = this.chart.toBase64Image()
      const link = document.createElement('a')
      link.download = `${filename}.png`
      link.href = url
      link.click()
    },
    getChartData() {
      return this.chart?.data || null
    }
  }
}
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
}

canvas {
  max-width: 100%;
}
</style>
