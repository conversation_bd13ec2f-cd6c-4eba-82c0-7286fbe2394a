<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\Tenant;
use Illuminate\Support\Facades\DB;

class InvoiceService
{
    /**
     * إنشاء فواتير تجريبية للشركة الجديدة
     */
    public function createSampleInvoices(Tenant $tenant): void
    {
        DB::beginTransaction();
        
        try {
            // الحصول على البيانات المطلوبة
            $customers = Customer::where('tenant_id', $tenant->id)->get();
            $suppliers = Supplier::where('tenant_id', $tenant->id)->get();
            $products = Product::where('tenant_id', $tenant->id)->get();
            $warehouses = Warehouse::where('tenant_id', $tenant->id)->get();
            
            if ($customers->isEmpty() || $suppliers->isEmpty() || $products->isEmpty() || $warehouses->isEmpty()) {
                throw new \Exception('يجب إنشاء العملاء والموردين والمنتجات والمخازن أولاً');
            }
            
            // إنشاء فواتير بيع
            $this->createSampleSalesInvoices($tenant, $customers, $products, $warehouses);
            
            // إنشاء فواتير شراء
            $this->createSamplePurchaseInvoices($tenant, $suppliers, $products, $warehouses);
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
    
    /**
     * إنشاء فواتير بيع تجريبية
     */
    private function createSampleSalesInvoices(Tenant $tenant, $customers, $products, $warehouses): void
    {
        $salesInvoices = [
            [
                'customer' => $customers->first(),
                'products' => $products->take(3),
                'status' => 'confirmed',
                'payment_status' => 'paid',
                'days_ago' => 15,
            ],
            [
                'customer' => $customers->skip(1)->first(),
                'products' => $products->skip(1)->take(2),
                'status' => 'confirmed',
                'payment_status' => 'partial',
                'days_ago' => 10,
            ],
            [
                'customer' => $customers->last(),
                'products' => $products->skip(2)->take(4),
                'status' => 'confirmed',
                'payment_status' => 'unpaid',
                'days_ago' => 5,
            ],
            [
                'customer' => $customers->first(),
                'products' => $products->take(2),
                'status' => 'draft',
                'payment_status' => 'unpaid',
                'days_ago' => 1,
            ],
        ];
        
        foreach ($salesInvoices as $invoiceData) {
            $invoiceDate = now()->subDays($invoiceData['days_ago']);
            $dueDate = $invoiceDate->copy()->addDays(30);
            
            $invoice = Invoice::create([
                'tenant_id' => $tenant->id,
                'invoice_number' => Invoice::generateInvoiceNumber('sale'),
                'invoice_type' => 'sale',
                'invoice_date' => $invoiceDate,
                'due_date' => $dueDate,
                'customer_id' => $invoiceData['customer']->id,
                'customer_name' => $invoiceData['customer']->full_name,
                'customer_address' => $invoiceData['customer']->address,
                'customer_phone' => $invoiceData['customer']->phone,
                'customer_tax_number' => $invoiceData['customer']->tax_number,
                'warehouse_id' => $warehouses->first()->id,
                'status' => $invoiceData['status'],
                'payment_status' => $invoiceData['payment_status'],
                'payment_method' => 'cash',
                'notes' => 'فاتورة بيع تجريبية',
                'created_by' => 1,
                'confirmed_at' => $invoiceData['status'] === 'confirmed' ? $invoiceDate : null,
                'confirmed_by' => $invoiceData['status'] === 'confirmed' ? 1 : null,
                'created_at' => $invoiceDate,
                'updated_at' => $invoiceDate,
            ]);
            
            // إنشاء بنود الفاتورة
            foreach ($invoiceData['products'] as $product) {
                $quantity = rand(1, 10);
                $unitPrice = $product->selling_price;
                $discountPercentage = rand(0, 10);
                $taxPercentage = 14; // ضريبة القيمة المضافة
                
                $item = InvoiceItem::create([
                    'tenant_id' => $tenant->id,
                    'invoice_id' => $invoice->id,
                    'product_id' => $product->id,
                    'product_code' => $product->product_code,
                    'product_name' => $product->name,
                    'product_description' => $product->description,
                    'unit' => $product->unit,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'discount_percentage' => $discountPercentage,
                    'tax_percentage' => $taxPercentage,
                ]);
                
                $item->calculateTotal();
            }
            
            // حساب مجاميع الفاتورة
            $invoice->calculateTotals();
            
            // تحديد المبلغ المدفوع حسب حالة الدفع
            if ($invoiceData['payment_status'] === 'paid') {
                $invoice->paid_amount = $invoice->total_amount;
                $invoice->remaining_amount = 0;
            } elseif ($invoiceData['payment_status'] === 'partial') {
                $invoice->paid_amount = $invoice->total_amount * 0.5;
                $invoice->remaining_amount = $invoice->total_amount - $invoice->paid_amount;
            }
            
            $invoice->save();
        }
    }
    
    /**
     * إنشاء فواتير شراء تجريبية
     */
    private function createSamplePurchaseInvoices(Tenant $tenant, $suppliers, $products, $warehouses): void
    {
        $purchaseInvoices = [
            [
                'supplier' => $suppliers->first(),
                'products' => $products->take(5),
                'status' => 'confirmed',
                'payment_status' => 'paid',
                'days_ago' => 20,
            ],
            [
                'supplier' => $suppliers->skip(1)->first(),
                'products' => $products->skip(2)->take(3),
                'status' => 'confirmed',
                'payment_status' => 'unpaid',
                'days_ago' => 12,
            ],
            [
                'supplier' => $suppliers->last(),
                'products' => $products->skip(1)->take(4),
                'status' => 'confirmed',
                'payment_status' => 'partial',
                'days_ago' => 8,
            ],
        ];
        
        foreach ($purchaseInvoices as $invoiceData) {
            $invoiceDate = now()->subDays($invoiceData['days_ago']);
            $dueDate = $invoiceDate->copy()->addDays(45);
            
            $invoice = Invoice::create([
                'tenant_id' => $tenant->id,
                'invoice_number' => Invoice::generateInvoiceNumber('purchase'),
                'invoice_type' => 'purchase',
                'invoice_date' => $invoiceDate,
                'due_date' => $dueDate,
                'supplier_id' => $invoiceData['supplier']->id,
                'warehouse_id' => $warehouses->first()->id,
                'status' => $invoiceData['status'],
                'payment_status' => $invoiceData['payment_status'],
                'payment_method' => 'bank_transfer',
                'notes' => 'فاتورة شراء تجريبية',
                'created_by' => 1,
                'confirmed_at' => $invoiceDate,
                'confirmed_by' => 1,
                'created_at' => $invoiceDate,
                'updated_at' => $invoiceDate,
            ]);
            
            // إنشاء بنود الفاتورة
            foreach ($invoiceData['products'] as $product) {
                $quantity = rand(10, 50);
                $unitPrice = $product->purchase_price;
                $discountPercentage = rand(0, 5);
                $taxPercentage = 14;
                
                $item = InvoiceItem::create([
                    'tenant_id' => $tenant->id,
                    'invoice_id' => $invoice->id,
                    'product_id' => $product->id,
                    'product_code' => $product->product_code,
                    'product_name' => $product->name,
                    'product_description' => $product->description,
                    'unit' => $product->unit,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'discount_percentage' => $discountPercentage,
                    'tax_percentage' => $taxPercentage,
                ]);
                
                $item->calculateTotal();
            }
            
            // حساب مجاميع الفاتورة
            $invoice->calculateTotals();
            
            // تحديد المبلغ المدفوع حسب حالة الدفع
            if ($invoiceData['payment_status'] === 'paid') {
                $invoice->paid_amount = $invoice->total_amount;
                $invoice->remaining_amount = 0;
            } elseif ($invoiceData['payment_status'] === 'partial') {
                $invoice->paid_amount = $invoice->total_amount * 0.7;
                $invoice->remaining_amount = $invoice->total_amount - $invoice->paid_amount;
            }
            
            $invoice->save();
        }
    }
    
    /**
     * الحصول على إحصائيات الفواتير
     */
    public function getInvoiceStatistics(): array
    {
        return [
            'sales' => [
                'total_invoices' => Invoice::sales()->count(),
                'confirmed_invoices' => Invoice::sales()->confirmed()->count(),
                'paid_invoices' => Invoice::sales()->paid()->count(),
                'unpaid_invoices' => Invoice::sales()->unpaid()->count(),
                'overdue_invoices' => Invoice::sales()->overdue()->count(),
                'total_amount' => Invoice::sales()->confirmed()->sum('total_amount'),
                'paid_amount' => Invoice::sales()->sum('paid_amount'),
                'remaining_amount' => Invoice::sales()->sum('remaining_amount'),
            ],
            'purchases' => [
                'total_invoices' => Invoice::purchases()->count(),
                'confirmed_invoices' => Invoice::purchases()->confirmed()->count(),
                'paid_invoices' => Invoice::purchases()->paid()->count(),
                'unpaid_invoices' => Invoice::purchases()->unpaid()->count(),
                'overdue_invoices' => Invoice::purchases()->overdue()->count(),
                'total_amount' => Invoice::purchases()->confirmed()->sum('total_amount'),
                'paid_amount' => Invoice::purchases()->sum('paid_amount'),
                'remaining_amount' => Invoice::purchases()->sum('remaining_amount'),
            ],
            'overall' => [
                'total_invoices' => Invoice::count(),
                'confirmed_invoices' => Invoice::confirmed()->count(),
                'paid_invoices' => Invoice::paid()->count(),
                'overdue_invoices' => Invoice::overdue()->count(),
                'gross_profit' => $this->calculateGrossProfit(),
            ],
        ];
    }
    
    /**
     * حساب إجمالي الربح
     */
    private function calculateGrossProfit(): float
    {
        $salesRevenue = Invoice::sales()->confirmed()->sum('total_amount');
        $purchaseCost = Invoice::purchases()->confirmed()->sum('total_amount');
        
        return $salesRevenue - $purchaseCost;
    }
    
    /**
     * الحصول على الفواتير المستحقة
     */
    public function getOverdueInvoices(): array
    {
        return [
            'sales' => Invoice::sales()->overdue()->with(['customer'])->get(),
            'purchases' => Invoice::purchases()->overdue()->with(['supplier'])->get(),
        ];
    }
    
    /**
     * الحصول على أفضل العملاء
     */
    public function getTopCustomers(int $limit = 10): array
    {
        return Customer::withSum(['invoices' => function($query) {
                $query->where('status', 'confirmed');
            }], 'total_amount')
            ->orderBy('invoices_sum_total_amount', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }
    
    /**
     * الحصول على أفضل المنتجات مبيعاً
     */
    public function getTopSellingProducts(int $limit = 10): array
    {
        return Product::withSum(['invoiceItems' => function($query) {
                $query->whereHas('invoice', function($invoiceQuery) {
                    $invoiceQuery->where('invoice_type', 'sale')
                                ->where('status', 'confirmed');
                });
            }], 'quantity')
            ->orderBy('invoice_items_sum_quantity', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }
}
