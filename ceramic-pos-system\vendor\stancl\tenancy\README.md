<p align="center">
    <a href="https://tenancyforlaravel.com"><img width="800" src="/art/logo.png" alt="Tenancy for Laravel logo" /></a>
</p>

<p align="center">
    <a href="https://laravel.com"><img alt="Laravel 10.x" src="https://img.shields.io/badge/laravel-10.x-red.svg"></a>
    <a href="https://packagist.org/packages/stancl/tenancy"><img alt="Latest Stable Version" src="https://poser.pugx.org/stancl/tenancy/version"></a>
    <a href="https://github.com/stancl/tenancy/actions"><img alt="GitHub Actions CI status" src="https://github.com/stancl/tenancy/workflows/CI/badge.svg"></a>
    <a href="https://github.com/stancl/tenancy/blob/3.x/DONATIONS.md"><img alt="Donate" src="https://img.shields.io/badge/Donate-%3C3-red"></a>
</p>

<h1><a href="https://tenancyforlaravel.com">Tenancy for Laravel &mdash; stancl/tenancy</a></h1>

### *Automatic multi-tenancy for your Laravel app.*

You won't have to change a thing in your application's code.

- :heavy_check_mark: No model traits to change database connection
- :heavy_check_mark: No replacing of Laravel classes (`Cache`, `Storage`, ...) with tenancy-aware classes
- :heavy_check_mark: Built-in tenant identification based on hostname (including second level domains)

### [Documentation](https://tenancyforlaravel.com/docs/v3/)

Documentation can be found here: https://tenancyforlaravel.com/docs/v3/

The repository with the documentation source code can be found here: [stancl/tenancy-docs](https://github.com/stancl/tenancy-docs).

### [Need help?](https://github.com/stancl/tenancy/blob/3.x/SUPPORT.md)

### Credits

- Package created by [Samuel Štancl](https://twitter.com/samuelstancl)
- Design help & current logo by [Florian Karsten](https://floriankarsten.com/)
