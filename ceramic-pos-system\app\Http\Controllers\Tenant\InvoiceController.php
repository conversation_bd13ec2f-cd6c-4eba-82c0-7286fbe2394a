<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\Tax;
use Illuminate\Support\Facades\DB;

class InvoiceController extends Controller
{
    /**
     * عرض قائمة الفواتير
     */
    public function index(Request $request)
    {
        $query = Invoice::with(['customer', 'supplier', 'warehouse', 'createdBy']);

        // التصفية حسب نوع الفاتورة
        if ($request->filled('invoice_type')) {
            $query->where('invoice_type', $request->invoice_type);
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية حسب حالة الدفع
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // التصفية حسب العميل
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // التصفية حسب المورد
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // التصفية حسب التاريخ
        if ($request->filled('date_from')) {
            $query->where('invoice_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('invoice_date', '<=', $request->date_to);
        }

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // الترتيب
        $query->orderBy('invoice_date', 'desc')->orderBy('id', 'desc');

        $invoices = $query->paginate(20);

        // البيانات المساعدة
        $customers = Customer::active()->orderBy('name')->get(['id', 'name', 'customer_code']);
        $suppliers = Supplier::active()->orderBy('name')->get(['id', 'name', 'supplier_code']);

        // إحصائيات
        $stats = [
            'total_invoices' => Invoice::count(),
            'sales_invoices' => Invoice::sales()->count(),
            'purchase_invoices' => Invoice::purchases()->count(),
            'confirmed_invoices' => Invoice::confirmed()->count(),
            'paid_invoices' => Invoice::paid()->count(),
            'overdue_invoices' => Invoice::overdue()->count(),
            'total_sales_amount' => Invoice::sales()->confirmed()->sum('total_amount'),
            'total_purchases_amount' => Invoice::purchases()->confirmed()->sum('total_amount'),
        ];

        return view('tenant.invoices.index', compact('invoices', 'customers', 'suppliers', 'stats'));
    }

    /**
     * عرض نموذج إنشاء فاتورة جديدة
     */
    public function create(Request $request)
    {
        $type = $request->get('type', 'sale'); // sale or purchase

        $customers = Customer::active()->orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();
        $products = Product::active()->orderBy('name')->get();
        $warehouses = Warehouse::active()->orderBy('name')->get();
        $taxes = Tax::active()->orderBy('name')->get();

        // إنشاء رقم فاتورة تلقائي
        $invoiceNumber = Invoice::generateInvoiceNumber($type);

        return view('tenant.invoices.create', compact(
            'type', 'customers', 'suppliers', 'products', 'warehouses', 'taxes', 'invoiceNumber'
        ));
    }

    /**
     * حفظ فاتورة جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'invoice_type' => 'required|in:sale,purchase',
            'invoice_number' => 'required|string|max:50|unique:invoices,invoice_number',
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date|after_or_equal:invoice_date',
            'customer_id' => 'required_if:invoice_type,sale|nullable|exists:customers,id',
            'supplier_id' => 'required_if:invoice_type,purchase|nullable|exists:suppliers,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'payment_method' => 'nullable|in:' . implode(',', array_keys(Invoice::PAYMENT_METHODS)),
            'notes' => 'nullable|string',
            'terms_conditions' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.001',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.tax_percentage' => 'nullable|numeric|min:0|max:100',
        ], [
            'invoice_type.required' => 'نوع الفاتورة مطلوب',
            'invoice_number.required' => 'رقم الفاتورة مطلوب',
            'invoice_number.unique' => 'رقم الفاتورة مستخدم من قبل',
            'invoice_date.required' => 'تاريخ الفاتورة مطلوب',
            'customer_id.required_if' => 'العميل مطلوب لفاتورة البيع',
            'supplier_id.required_if' => 'المورد مطلوب لفاتورة الشراء',
            'warehouse_id.required' => 'المخزن مطلوب',
            'items.required' => 'يجب إضافة بند واحد على الأقل',
            'items.*.product_id.required' => 'المنتج مطلوب',
            'items.*.quantity.required' => 'الكمية مطلوبة',
            'items.*.unit_price.required' => 'سعر الوحدة مطلوب',
        ]);

        DB::beginTransaction();

        try {
            // إنشاء الفاتورة
            $invoice = Invoice::create([
                'invoice_number' => $request->invoice_number,
                'invoice_type' => $request->invoice_type,
                'invoice_date' => $request->invoice_date,
                'due_date' => $request->due_date,
                'customer_id' => $request->customer_id,
                'supplier_id' => $request->supplier_id,
                'warehouse_id' => $request->warehouse_id,
                'payment_method' => $request->payment_method,
                'notes' => $request->notes,
                'terms_conditions' => $request->terms_conditions,
                'status' => 'draft',
                'payment_status' => 'unpaid',
                'created_by' => auth()->id(),
            ]);

            // نسخ بيانات العميل/المورد
            if ($request->invoice_type === 'sale' && $invoice->customer) {
                $invoice->update([
                    'customer_name' => $invoice->customer->full_name,
                    'customer_address' => $invoice->customer->address,
                    'customer_phone' => $invoice->customer->phone,
                    'customer_tax_number' => $invoice->customer->tax_number,
                ]);
            }

            // إنشاء بنود الفاتورة
            foreach ($request->items as $itemData) {
                $product = Product::find($itemData['product_id']);

                $item = InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_id' => $itemData['product_id'],
                    'product_code' => $product->product_code,
                    'product_name' => $product->name,
                    'product_description' => $product->description,
                    'unit' => $product->unit,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'discount_percentage' => $itemData['discount_percentage'] ?? 0,
                    'tax_percentage' => $itemData['tax_percentage'] ?? 0,
                ]);

                // حساب إجمالي البند
                $item->calculateTotal();
            }

            // حساب مجاميع الفاتورة
            $invoice->calculateTotals();

            DB::commit();

            return redirect()->route('tenant.invoices.show', $invoice)
                ->with('success', 'تم إنشاء الفاتورة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل الفاتورة
     */
    public function show(Invoice $invoice)
    {
        $invoice->load(['customer', 'supplier', 'warehouse', 'items.product', 'createdBy', 'confirmedBy']);

        return view('tenant.invoices.show', compact('invoice'));
    }

    /**
     * عرض نموذج تعديل الفاتورة
     */
    public function edit(Invoice $invoice)
    {
        if (!$invoice->canEdit()) {
            return redirect()->route('tenant.invoices.show', $invoice)
                ->with('error', 'لا يمكن تعديل هذه الفاتورة');
        }

        $invoice->load(['items.product']);

        $customers = Customer::active()->orderBy('name')->get();
        $suppliers = Supplier::active()->orderBy('name')->get();
        $products = Product::active()->orderBy('name')->get();
        $warehouses = Warehouse::active()->orderBy('name')->get();
        $taxes = Tax::active()->orderBy('name')->get();

        return view('tenant.invoices.edit', compact(
            'invoice', 'customers', 'suppliers', 'products', 'warehouses', 'taxes'
        ));
    }

    /**
     * تحديث الفاتورة
     */
    public function update(Request $request, Invoice $invoice)
    {
        if (!$invoice->canEdit()) {
            return redirect()->route('tenant.invoices.show', $invoice)
                ->with('error', 'لا يمكن تعديل هذه الفاتورة');
        }

        $request->validate([
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date|after_or_equal:invoice_date',
            'customer_id' => 'required_if:invoice_type,sale|nullable|exists:customers,id',
            'supplier_id' => 'required_if:invoice_type,purchase|nullable|exists:suppliers,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'payment_method' => 'nullable|in:' . implode(',', array_keys(Invoice::PAYMENT_METHODS)),
            'notes' => 'nullable|string',
            'terms_conditions' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.001',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.tax_percentage' => 'nullable|numeric|min:0|max:100',
        ]);

        DB::beginTransaction();

        try {
            // تحديث الفاتورة
            $invoice->update([
                'invoice_date' => $request->invoice_date,
                'due_date' => $request->due_date,
                'customer_id' => $request->customer_id,
                'supplier_id' => $request->supplier_id,
                'warehouse_id' => $request->warehouse_id,
                'payment_method' => $request->payment_method,
                'notes' => $request->notes,
                'terms_conditions' => $request->terms_conditions,
                'updated_by' => auth()->id(),
            ]);

            // حذف البنود القديمة
            $invoice->items()->delete();

            // إنشاء البنود الجديدة
            foreach ($request->items as $itemData) {
                $product = Product::find($itemData['product_id']);

                $item = InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_id' => $itemData['product_id'],
                    'product_code' => $product->product_code,
                    'product_name' => $product->name,
                    'product_description' => $product->description,
                    'unit' => $product->unit,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'discount_percentage' => $itemData['discount_percentage'] ?? 0,
                    'tax_percentage' => $itemData['tax_percentage'] ?? 0,
                ]);

                $item->calculateTotal();
            }

            // إعادة حساب المجاميع
            $invoice->calculateTotals();

            DB::commit();

            return redirect()->route('tenant.invoices.show', $invoice)
                ->with('success', 'تم تحديث الفاتورة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * حذف الفاتورة
     */
    public function destroy(Invoice $invoice)
    {
        if (!$invoice->canDelete()) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف هذه الفاتورة');
        }

        try {
            $invoice->items()->delete();
            $invoice->delete();

            return redirect()->route('tenant.invoices.index')
                ->with('success', 'تم حذف الفاتورة بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * تأكيد الفاتورة
     */
    public function confirm(Invoice $invoice)
    {
        if ($invoice->status !== 'draft') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تأكيد هذه الفاتورة'
            ], 400);
        }

        // التحقق من توفر المخزون للبيع
        if ($invoice->isSale()) {
            foreach ($invoice->items as $item) {
                if (!$item->checkStockAvailability()) {
                    return response()->json([
                        'success' => false,
                        'message' => "الكمية المطلوبة من المنتج '{$item->product_name}' غير متوفرة في المخزون"
                    ], 400);
                }
            }
        }

        try {
            $invoice->confirm();

            return response()->json([
                'success' => true,
                'message' => 'تم تأكيد الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تأكيد الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إلغاء الفاتورة
     */
    public function cancel(Invoice $invoice)
    {
        if (!in_array($invoice->status, ['draft', 'confirmed'])) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن إلغاء هذه الفاتورة'
            ], 400);
        }

        try {
            $invoice->cancel();

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء الفاتورة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء الفاتورة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * طباعة الفاتورة
     */
    public function print(Invoice $invoice)
    {
        $invoice->load(['customer', 'supplier', 'warehouse', 'items.product']);

        return view('tenant.invoices.print', compact('invoice'));
    }

    /**
     * تقرير الفواتير
     */
    public function report(Request $request)
    {
        $query = Invoice::with(['customer', 'supplier', 'warehouse']);

        // التصفية حسب نوع الفاتورة
        if ($request->filled('invoice_type')) {
            $query->where('invoice_type', $request->invoice_type);
        }

        // التصفية حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // التصفية حسب الفترة الزمنية
        if ($request->filled('date_from')) {
            $query->where('invoice_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('invoice_date', '<=', $request->date_to);
        }

        $invoices = $query->orderBy('invoice_date', 'desc')->get();

        return view('tenant.invoices.report', compact('invoices'));
    }
}
