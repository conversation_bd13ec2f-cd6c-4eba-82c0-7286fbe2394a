<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Tenant;
use App\Models\User;
use App\Models\Role;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Warehouse;
use App\Models\ChartOfAccount;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Setting;
use Illuminate\Support\Facades\Hash;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('إنشاء البيانات التجريبية...');

        // إنشاء المستخدمين
        $users = $this->createUsers();

        // إنشاء الإعدادات
        $this->createSettings();

        $this->command->info('تم إنشاء البيانات التجريبية بنجاح!');
    }

    /**
     * إنشاء شركة تجريبية
     */
    private function createDemoTenant(): Tenant
    {
        // التحقق من وجود الشركة أولاً
        $tenant = Tenant::where('slug', 'demo-company')->first();

        if (!$tenant) {
            // إنشاء الشركة مباشرة في قاعدة البيانات
            \DB::table('tenants')->insert([
                'name' => 'شركة السيراميك التجريبية',
                'slug' => 'demo-company',
                'subdomain' => 'demo',
                'domain' => 'ceramic-pos.local',
                'database_name' => 'ceramic_pos',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '02-********',
                'address' => 'القاهرة، مصر',
                'city' => 'القاهرة',
                'country' => 'EG',
                'currency' => 'EGP',
                'timezone' => 'Africa/Cairo',
                'language' => 'ar',
                'status' => 'active',
                'plan' => 'premium',
                'max_users' => 50,
                'subscription_start' => now()->toDateString(),
                'subscription_end' => now()->addYear()->toDateString(),
                'data' => json_encode([
                    'database' => 'ceramic_pos',
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $tenant = Tenant::where('slug', 'demo-company')->first();
        }

        return $tenant;
    }

    /**
     * إنشاء المستخدمين
     */
    private function createUsers(): array
    {
        $users = [];

        // مدير النظام
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => Hash::make('password'),
                'is_active' => true,
            ]
        );

        // إنشاء الأدوار إذا لم تكن موجودة
        if (!Role::where('name', 'super_admin')->exists()) {
            Role::create([
                'name' => 'super_admin',
                'display_name' => 'مدير النظام',
                'guard_name' => 'web',
                'tenant_id' => 'demo-company',
                'is_system_role' => true,
                'is_active' => true,
            ]);
        }

        $admin->assignRole('super_admin');
        $users['admin'] = $admin;

        // إنشاء باقي الأدوار
        $roles = [
            ['name' => 'accountant', 'display_name' => 'محاسب'],
            ['name' => 'sales_manager', 'display_name' => 'مدير مبيعات'],
            ['name' => 'warehouse_manager', 'display_name' => 'مدير مخزن'],
        ];

        foreach ($roles as $roleData) {
            if (!Role::where('name', $roleData['name'])->exists()) {
                Role::create([
                    'name' => $roleData['name'],
                    'display_name' => $roleData['display_name'],
                    'guard_name' => 'web',
                    'tenant_id' => 'demo-company',
                    'is_system_role' => false,
                    'is_active' => true,
                ]);
            }
        }

        // محاسب
        $accountant = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'أحمد المحاسب',
                'password' => Hash::make('password'),
                'is_active' => true,
            ]
        );
        $accountant->assignRole('accountant');
        $users['accountant'] = $accountant;

        // مدير مبيعات
        $salesManager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'سارة مدير المبيعات',
                'password' => Hash::make('password'),
                'is_active' => true,
            ]
        );
        $salesManager->assignRole('sales_manager');
        $users['sales_manager'] = $salesManager;

        // مدير مخزن
        $warehouseManager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'محمد مدير المخزن',
                'password' => Hash::make('password'),
                'is_active' => true,
            ]
        );
        $warehouseManager->assignRole('warehouse_manager');
        $users['warehouse_manager'] = $warehouseManager;

        return $users;
    }

    /**
     * إنشاء دليل الحسابات
     */
    private function createChartOfAccounts(): void
    {
        $accounts = [
            // الأصول
            ['account_code' => '1000', 'account_name' => 'الأصول', 'account_type' => 'assets', 'parent_id' => null],
            ['account_code' => '1100', 'account_name' => 'الأصول المتداولة', 'account_type' => 'assets', 'parent_code' => '1000'],
            ['account_code' => '1110', 'account_name' => 'النقدية', 'account_type' => 'assets', 'parent_code' => '1100'],
            ['account_code' => '1120', 'account_name' => 'العملاء', 'account_type' => 'assets', 'parent_code' => '1100'],
            ['account_code' => '1130', 'account_name' => 'المخزون', 'account_type' => 'assets', 'parent_code' => '1100'],

            // الخصوم
            ['account_code' => '2000', 'account_name' => 'الخصوم', 'account_type' => 'liabilities', 'parent_id' => null],
            ['account_code' => '2100', 'account_name' => 'الخصوم المتداولة', 'account_type' => 'liabilities', 'parent_code' => '2000'],
            ['account_code' => '2110', 'account_name' => 'الموردين', 'account_type' => 'liabilities', 'parent_code' => '2100'],

            // حقوق الملكية
            ['account_code' => '3000', 'account_name' => 'حقوق الملكية', 'account_type' => 'equity', 'parent_id' => null],
            ['account_code' => '3100', 'account_name' => 'رأس المال', 'account_type' => 'equity', 'parent_code' => '3000'],

            // الإيرادات
            ['account_code' => '4000', 'account_name' => 'الإيرادات', 'account_type' => 'revenue', 'parent_id' => null],
            ['account_code' => '4100', 'account_name' => 'إيرادات المبيعات', 'account_type' => 'revenue', 'parent_code' => '4000'],

            // المصروفات
            ['account_code' => '5000', 'account_name' => 'المصروفات', 'account_type' => 'expenses', 'parent_id' => null],
            ['account_code' => '5100', 'account_name' => 'تكلفة البضاعة المباعة', 'account_type' => 'expenses', 'parent_code' => '5000'],
            ['account_code' => '5200', 'account_name' => 'مصروفات التشغيل', 'account_type' => 'expenses', 'parent_code' => '5000'],
        ];

        foreach ($accounts as $account) {
            // تحقق من وجود الحساب
            if (ChartOfAccount::where('account_code', $account['account_code'])->exists()) {
                continue;
            }

            $parentId = null;
            if (isset($account['parent_code'])) {
                $parent = ChartOfAccount::where('account_code', $account['parent_code'])->first();
                $parentId = $parent?->id;
            }

            ChartOfAccount::create([
                'account_code' => $account['account_code'],
                'account_name' => $account['account_name'],
                'account_type' => $account['account_type'],
                'parent_id' => $parentId,
                'is_active' => true,
                'tenant_id' => 1,
            ]);
        }
    }

    /**
     * إنشاء المخازن
     */
    private function createWarehouses(): array
    {
        $warehouses = [
            ['name' => 'المخزن الرئيسي', 'location' => 'القاهرة'],
            ['name' => 'مخزن الإسكندرية', 'location' => 'الإسكندرية'],
            ['name' => 'مخزن الجيزة', 'location' => 'الجيزة'],
        ];

        $created = [];
        foreach ($warehouses as $warehouse) {
            $created[] = Warehouse::create([
                'name' => $warehouse['name'],
                'location' => $warehouse['location'],
                'is_active' => true,
            ]);
        }

        return $created;
    }

    /**
     * إنشاء فئات المنتجات
     */
    private function createProductCategories(): array
    {
        $categories = [
            'سيراميك أرضيات',
            'سيراميك حوائط',
            'بورسلين',
            'أدوات صحية',
            'خلاطات',
            'إكسسوارات',
        ];

        $created = [];
        foreach ($categories as $category) {
            $created[] = ProductCategory::create([
                'name' => $category,
                'description' => "فئة {$category}",
                'is_active' => true,
            ]);
        }

        return $created;
    }

    /**
     * إنشاء المنتجات
     */
    private function createProducts($categories, $warehouses): array
    {
        $products = [
            ['name' => 'سيراميك أرضيات 60x60', 'code' => 'FLOOR-001', 'category' => 0, 'price' => 45.50],
            ['name' => 'سيراميك حوائط 30x60', 'code' => 'WALL-001', 'category' => 1, 'price' => 32.75],
            ['name' => 'بورسلين 80x80', 'code' => 'POR-001', 'category' => 2, 'price' => 89.25],
            ['name' => 'مرحاض إفرنجي', 'code' => 'TOILET-001', 'category' => 3, 'price' => 450.00],
            ['name' => 'خلاط حوض', 'code' => 'TAP-001', 'category' => 4, 'price' => 125.50],
            ['name' => 'مرآة حمام', 'code' => 'MIRROR-001', 'category' => 5, 'price' => 85.00],
        ];

        $created = [];
        foreach ($products as $index => $product) {
            $created[] = Product::create([
                'name' => $product['name'],
                'product_code' => $product['code'],
                'category_id' => $categories[$product['category']]->id,
                'product_type' => 'finished',
                'unit' => 'قطعة',
                'selling_price' => $product['price'],
                'purchase_price' => $product['price'] * 0.7,
                'minimum_stock' => 10,
                'is_active' => true,
            ]);
        }

        return $created;
    }

    /**
     * إنشاء العملاء
     */
    private function createCustomers(): array
    {
        $customers = [
            ['name' => 'شركة المقاولات الحديثة', 'phone' => '0********90', 'email' => '<EMAIL>'],
            ['name' => 'مؤسسة البناء المتطور', 'phone' => '0********91', 'email' => '<EMAIL>'],
            ['name' => 'شركة التشييد الذهبية', 'phone' => '0********92', 'email' => '<EMAIL>'],
            ['name' => 'أحمد محمد علي', 'phone' => '0********93', 'email' => '<EMAIL>'],
            ['name' => 'فاطمة حسن محمود', 'phone' => '0********94', 'email' => '<EMAIL>'],
        ];

        $created = [];
        foreach ($customers as $index => $customer) {
            $created[] = Customer::create([
                'customer_code' => 'CUST-' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                'name' => $customer['name'],
                'customer_type' => $index < 3 ? 'company' : 'individual',
                'phone' => $customer['phone'],
                'email' => $customer['email'],
                'address' => 'القاهرة، مصر',
                'is_active' => true,
            ]);
        }

        return $created;
    }

    /**
     * إنشاء الموردين
     */
    private function createSuppliers(): array
    {
        $suppliers = [
            ['name' => 'مصنع السيراميك المصري', 'phone' => '02234567890', 'email' => '<EMAIL>'],
            ['name' => 'شركة الخزف الحديث', 'phone' => '02234567891', 'email' => '<EMAIL>'],
            ['name' => 'مؤسسة الأدوات الصحية', 'phone' => '02234567892', 'email' => '<EMAIL>'],
        ];

        $created = [];
        foreach ($suppliers as $index => $supplier) {
            $created[] = Supplier::create([
                'supplier_code' => 'SUPP-' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                'name' => $supplier['name'],
                'supplier_type' => 'company',
                'phone' => $supplier['phone'],
                'email' => $supplier['email'],
                'address' => 'القاهرة، مصر',
                'is_active' => true,
            ]);
        }

        return $created;
    }

    /**
     * إنشاء الفواتير
     */
    private function createInvoices($customers, $suppliers, $products): void
    {
        // فواتير البيع
        for ($i = 1; $i <= 10; $i++) {
            $customer = $customers[array_rand($customers)];
            $invoice = Invoice::create([
                'invoice_number' => 'INV-S-' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'invoice_type' => 'sale',
                'customer_id' => $customer->id,
                'invoice_date' => now()->subDays(rand(1, 30)),
                'due_date' => now()->addDays(rand(1, 30)),
                'subtotal' => 0,
                'tax_amount' => 0,
                'total_amount' => 0,
                'status' => 'confirmed',
                'payment_status' => rand(0, 1) ? 'paid' : 'partial',
            ]);

            $total = 0;
            for ($j = 0; $j < rand(1, 3); $j++) {
                $product = $products[array_rand($products)];
                $quantity = rand(1, 5);
                $price = $product->selling_price;
                $amount = $quantity * $price;
                $total += $amount;

                $invoice->items()->create([
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $price,
                    'total_amount' => $amount,
                ]);
            }

            $invoice->update([
                'subtotal' => $total,
                'total_amount' => $total,
                'paid_amount' => $invoice->payment_status === 'paid' ? $total : $total * 0.5,
                'remaining_amount' => $invoice->payment_status === 'paid' ? 0 : $total * 0.5,
            ]);
        }

        // فواتير الشراء
        for ($i = 1; $i <= 5; $i++) {
            $supplier = $suppliers[array_rand($suppliers)];
            $invoice = Invoice::create([
                'invoice_number' => 'INV-P-' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'invoice_type' => 'purchase',
                'supplier_id' => $supplier->id,
                'invoice_date' => now()->subDays(rand(1, 30)),
                'due_date' => now()->addDays(rand(1, 30)),
                'subtotal' => 0,
                'tax_amount' => 0,
                'total_amount' => 0,
                'status' => 'confirmed',
                'payment_status' => rand(0, 1) ? 'paid' : 'partial',
            ]);

            $total = 0;
            for ($j = 0; $j < rand(2, 4); $j++) {
                $product = $products[array_rand($products)];
                $quantity = rand(10, 50);
                $price = $product->purchase_price;
                $amount = $quantity * $price;
                $total += $amount;

                $invoice->items()->create([
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $price,
                    'total_amount' => $amount,
                ]);
            }

            $invoice->update([
                'subtotal' => $total,
                'total_amount' => $total,
                'paid_amount' => $invoice->payment_status === 'paid' ? $total : $total * 0.7,
                'remaining_amount' => $invoice->payment_status === 'paid' ? 0 : $total * 0.3,
            ]);
        }
    }

    /**
     * إنشاء المدفوعات
     */
    private function createPayments($customers, $suppliers): void
    {
        // مقبوضات من العملاء
        for ($i = 1; $i <= 8; $i++) {
            $customer = $customers[array_rand($customers)];
            Payment::create([
                'payment_number' => 'PAY-R-' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'payment_type' => 'received',
                'customer_id' => $customer->id,
                'payment_date' => now()->subDays(rand(1, 20)),
                'amount' => rand(500, 5000),
                'payment_method' => ['cash', 'bank_transfer', 'check'][array_rand(['cash', 'bank_transfer', 'check'])],
                'status' => 'cleared',
                'notes' => 'مقبوضات من العميل',
            ]);
        }

        // مدفوعات للموردين
        for ($i = 1; $i <= 5; $i++) {
            $supplier = $suppliers[array_rand($suppliers)];
            Payment::create([
                'payment_number' => 'PAY-P-' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'payment_type' => 'paid',
                'supplier_id' => $supplier->id,
                'payment_date' => now()->subDays(rand(1, 15)),
                'amount' => rand(1000, 10000),
                'payment_method' => ['cash', 'bank_transfer', 'check'][array_rand(['cash', 'bank_transfer', 'check'])],
                'status' => 'cleared',
                'notes' => 'مدفوعات للمورد',
            ]);
        }
    }

    /**
     * إنشاء الإعدادات
     */
    private function createSettings(): void
    {
        $settings = [
            ['key' => 'company_name', 'value' => 'شركة السيراميك التجريبية', 'group' => 'company'],
            ['key' => 'company_address', 'value' => 'القاهرة، مصر', 'group' => 'company'],
            ['key' => 'company_phone', 'value' => '02-********', 'group' => 'company'],
            ['key' => 'company_email', 'value' => '<EMAIL>', 'group' => 'company'],
            ['key' => 'currency', 'value' => 'EGP', 'group' => 'general'],
            ['key' => 'timezone', 'value' => 'Africa/Cairo', 'group' => 'general'],
            ['key' => 'language', 'value' => 'ar', 'group' => 'general'],
        ];

        foreach ($settings as $setting) {
            \DB::table('settings')->updateOrInsert(
                [
                    'tenant_id' => 'demo-company',
                    'key' => $setting['key']
                ],
                [
                    'value' => $setting['value'],
                    'type' => 'string',
                    'group' => $setting['group'],
                    'is_public' => false,
                    'is_encrypted' => false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }
}
