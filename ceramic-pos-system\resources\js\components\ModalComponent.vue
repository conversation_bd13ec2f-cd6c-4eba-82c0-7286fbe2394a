<template>
  <teleport to="body">
    <div
      v-if="show"
      class="modal fade show"
      :class="{ 'd-block': show }"
      tabindex="-1"
      @click="onBackdropClick"
    >
      <div
        class="modal-dialog"
        :class="modalSizeClass"
        @click.stop
      >
        <div class="modal-content">
          <!-- Header -->
          <div class="modal-header" v-if="showHeader">
            <h5 class="modal-title">
              <i v-if="icon" :class="icon" class="me-2"></i>
              {{ title }}
            </h5>
            <button
              v-if="closable"
              type="button"
              class="btn-close"
              @click="close"
            ></button>
          </div>

          <!-- Body -->
          <div class="modal-body" :class="bodyClass">
            <div v-if="loading" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
              </div>
              <div class="mt-2">{{ loadingText }}</div>
            </div>
            <div v-else>
              <slot></slot>
            </div>
          </div>

          <!-- Footer -->
          <div class="modal-footer" v-if="showFooter">
            <slot name="footer">
              <button
                v-if="showCancelButton"
                type="button"
                class="btn btn-secondary"
                @click="cancel"
                :disabled="processing"
              >
                {{ cancelText }}
              </button>
              <button
                v-if="showConfirmButton"
                type="button"
                :class="confirmButtonClass"
                @click="confirm"
                :disabled="processing"
              >
                <span v-if="processing" class="spinner-border spinner-border-sm me-2"></span>
                {{ confirmText }}
              </button>
            </slot>
          </div>
        </div>
      </div>
    </div>

    <!-- Backdrop -->
    <div
      v-if="show"
      class="modal-backdrop fade show"
    ></div>
  </teleport>
</template>

<script>
export default {
  name: 'ModalComponent',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'md',
      validator: value => ['sm', 'md', 'lg', 'xl'].includes(value)
    },
    closable: {
      type: Boolean,
      default: true
    },
    closeOnBackdrop: {
      type: Boolean,
      default: true
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    showCancelButton: {
      type: Boolean,
      default: true
    },
    showConfirmButton: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: 'إلغاء'
    },
    confirmText: {
      type: String,
      default: 'موافق'
    },
    confirmVariant: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
    },
    loading: {
      type: Boolean,
      default: false
    },
    loadingText: {
      type: String,
      default: 'جاري التحميل...'
    },
    processing: {
      type: Boolean,
      default: false
    },
    bodyClass: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'cancel', 'confirm'],
  computed: {
    modalSizeClass() {
      const sizes = {
        sm: 'modal-sm',
        md: '',
        lg: 'modal-lg',
        xl: 'modal-xl'
      }
      return sizes[this.size] || ''
    },
    confirmButtonClass() {
      return `btn btn-${this.confirmVariant}`
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.addBodyClass()
        this.addEscapeListener()
      } else {
        this.removeBodyClass()
        this.removeEscapeListener()
      }
    }
  },
  mounted() {
    if (this.show) {
      this.addBodyClass()
      this.addEscapeListener()
    }
  },
  beforeUnmount() {
    this.removeBodyClass()
    this.removeEscapeListener()
  },
  methods: {
    close() {
      this.$emit('close')
    },
    cancel() {
      this.$emit('cancel')
      this.close()
    },
    confirm() {
      this.$emit('confirm')
    },
    onBackdropClick() {
      if (this.closeOnBackdrop && this.closable) {
        this.close()
      }
    },
    onEscapeKey(event) {
      if (event.key === 'Escape' && this.closable) {
        this.close()
      }
    },
    addBodyClass() {
      document.body.classList.add('modal-open')
    },
    removeBodyClass() {
      document.body.classList.remove('modal-open')
    },
    addEscapeListener() {
      document.addEventListener('keydown', this.onEscapeKey)
    },
    removeEscapeListener() {
      document.removeEventListener('keydown', this.onEscapeKey)
    }
  }
}
</script>

<style scoped>
.modal {
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  padding: 1.25rem 1.5rem;
}

.modal-title {
  font-weight: 600;
  color: #212529;
  margin: 0;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  padding: 1rem 1.5rem;
  gap: 0.5rem;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  opacity: 0.5;
  cursor: pointer;
}

.btn-close:hover {
  opacity: 0.75;
}

/* Animation */
.modal.fade.show {
  animation: modalFadeIn 0.3s ease-out;
}

.modal-backdrop.fade.show {
  animation: backdropFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.5;
  }
}

/* Responsive */
@media (max-width: 576px) {
  .modal-dialog {
    margin: 1rem;
    max-width: none;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>
