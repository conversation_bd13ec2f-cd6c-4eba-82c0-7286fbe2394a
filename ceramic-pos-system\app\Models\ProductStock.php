<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Traits\BelongsToTenant;

class ProductStock extends Model
{
    use BelongsToTenant;

    protected $table = 'product_stock';

    protected $fillable = [
        'tenant_id',
        'product_id',
        'warehouse_id',
        'quantity',
        'reserved_quantity',
        'available_quantity',
        'average_cost',
        'last_purchase_date',
        'last_sale_date',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'reserved_quantity' => 'integer',
        'available_quantity' => 'integer',
        'average_cost' => 'decimal:2',
        'last_purchase_date' => 'date',
        'last_sale_date' => 'date',
    ];

    /**
     * العلاقة مع المنتج
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * العلاقة مع المخزن
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Scope للمخزون المنخفض
     */
    public function scopeLowStock($query)
    {
        return $query->whereHas('product', function($q) {
            $q->whereRaw('product_stock.quantity <= products.minimum_stock');
        });
    }

    /**
     * Scope للمخزون النافد
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('quantity', '<=', 0);
    }

    /**
     * Scope للمخزون المتاح
     */
    public function scopeAvailable($query)
    {
        return $query->where('available_quantity', '>', 0);
    }

    /**
     * Scope للمخزون في مخزن محدد
     */
    public function scopeInWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    /**
     * الحصول على قيمة المخزون
     */
    public function getStockValueAttribute(): float
    {
        return $this->quantity * $this->average_cost;
    }

    /**
     * الحصول على قيمة المخزون المتاح
     */
    public function getAvailableValueAttribute(): float
    {
        return $this->available_quantity * $this->average_cost;
    }

    /**
     * التحقق من كون المخزون منخفض
     */
    public function isLowStock(): bool
    {
        return $this->quantity <= $this->product->minimum_stock;
    }

    /**
     * التحقق من كون المخزون نافد
     */
    public function isOutOfStock(): bool
    {
        return $this->quantity <= 0;
    }

    /**
     * التحقق من توفر كمية محددة
     */
    public function hasAvailableQuantity(int $requestedQuantity): bool
    {
        return $this->available_quantity >= $requestedQuantity;
    }

    /**
     * تحديث متوسط التكلفة
     */
    public function updateAverageCost(float $newCost, int $newQuantity): void
    {
        if ($this->quantity > 0) {
            $totalValue = ($this->quantity * $this->average_cost) + ($newQuantity * $newCost);
            $totalQuantity = $this->quantity + $newQuantity;
            $this->average_cost = $totalValue / $totalQuantity;
        } else {
            $this->average_cost = $newCost;
        }

        $this->save();
    }

    /**
     * إضافة كمية للمخزون
     */
    public function addQuantity(int $quantity, float $cost = null): void
    {
        if ($cost !== null) {
            $this->updateAverageCost($cost, $quantity);
        }

        $this->quantity += $quantity;
        $this->available_quantity += $quantity;
        $this->last_purchase_date = now();
        $this->save();
    }

    /**
     * خصم كمية من المخزون
     */
    public function subtractQuantity(int $quantity): bool
    {
        if ($this->available_quantity < $quantity) {
            return false;
        }

        $this->quantity -= $quantity;
        $this->available_quantity -= $quantity;
        $this->last_sale_date = now();
        $this->save();

        return true;
    }

    /**
     * حجز كمية
     */
    public function reserveQuantity(int $quantity): bool
    {
        if ($this->available_quantity < $quantity) {
            return false;
        }

        $this->reserved_quantity += $quantity;
        $this->available_quantity -= $quantity;
        $this->save();

        return true;
    }

    /**
     * إلغاء حجز كمية
     */
    public function unreserveQuantity(int $quantity): void
    {
        $unreserveAmount = min($quantity, $this->reserved_quantity);
        $this->reserved_quantity -= $unreserveAmount;
        $this->available_quantity += $unreserveAmount;
        $this->save();
    }
}
