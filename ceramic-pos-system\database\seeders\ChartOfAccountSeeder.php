<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ChartOfAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // ملاحظة: هذا Seeder سيتم تشغيله لكل tenant عند إنشائه
        // يحتوي على دليل الحسابات الأساسي للشركات المصرية

        $tenantId = 1; // سيتم تحديثه عند الاستخدام

        $accounts = [
            // الأصول المتداولة
            [
                'tenant_id' => $tenantId,
                'account_code' => '1000',
                'account_name' => 'الأصول المتداولة',
                'account_name_en' => 'Current Assets',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '1010',
                'account_name' => 'النقدية والبنوك',
                'account_name_en' => 'Cash and Banks',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 1, // سيتم تحديثه
                'level' => 2,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '1011',
                'account_name' => 'الخزينة',
                'account_name_en' => 'Cash',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 2, // سيتم تحديثه
                'level' => 3,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '1012',
                'account_name' => 'البنك الأهلي المصري',
                'account_name_en' => 'National Bank of Egypt',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 2, // سيتم تحديثه
                'level' => 3,
                'nature' => 'debit',
                'is_system' => false,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '1020',
                'account_name' => 'العملاء',
                'account_name_en' => 'Accounts Receivable',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 1, // سيتم تحديثه
                'level' => 2,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '1030',
                'account_name' => 'المخزون',
                'account_name_en' => 'Inventory',
                'account_type' => 'assets',
                'account_category' => 'current_assets',
                'parent_id' => 1, // سيتم تحديثه
                'level' => 2,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
            ],

            // الأصول الثابتة
            [
                'tenant_id' => $tenantId,
                'account_code' => '1500',
                'account_name' => 'الأصول الثابتة',
                'account_name_en' => 'Fixed Assets',
                'account_type' => 'assets',
                'account_category' => 'fixed_assets',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '1510',
                'account_name' => 'الأثاث والمعدات',
                'account_name_en' => 'Furniture and Equipment',
                'account_type' => 'assets',
                'account_category' => 'fixed_assets',
                'parent_id' => 7, // سيتم تحديثه
                'level' => 2,
                'nature' => 'debit',
                'is_system' => false,
                'is_active' => true,
            ],

            // الخصوم المتداولة
            [
                'tenant_id' => $tenantId,
                'account_code' => '2000',
                'account_name' => 'الخصوم المتداولة',
                'account_name_en' => 'Current Liabilities',
                'account_type' => 'liabilities',
                'account_category' => 'current_liabilities',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '2010',
                'account_name' => 'الموردين',
                'account_name_en' => 'Accounts Payable',
                'account_type' => 'liabilities',
                'account_category' => 'current_liabilities',
                'parent_id' => 9, // سيتم تحديثه
                'level' => 2,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '2020',
                'account_name' => 'ضريبة القيمة المضافة',
                'account_name_en' => 'VAT Payable',
                'account_type' => 'liabilities',
                'account_category' => 'current_liabilities',
                'parent_id' => 9, // سيتم تحديثه
                'level' => 2,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
            ],

            // حقوق الملكية
            [
                'tenant_id' => $tenantId,
                'account_code' => '3000',
                'account_name' => 'حقوق الملكية',
                'account_name_en' => 'Equity',
                'account_type' => 'equity',
                'account_category' => 'capital',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '3010',
                'account_name' => 'رأس المال',
                'account_name_en' => 'Capital',
                'account_type' => 'equity',
                'account_category' => 'capital',
                'parent_id' => 12, // سيتم تحديثه
                'level' => 2,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
            ],

            // الإيرادات
            [
                'tenant_id' => $tenantId,
                'account_code' => '4000',
                'account_name' => 'الإيرادات',
                'account_name_en' => 'Revenue',
                'account_type' => 'revenue',
                'account_category' => 'sales_revenue',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'credit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '4010',
                'account_name' => 'مبيعات السيراميك',
                'account_name_en' => 'Ceramic Sales',
                'account_type' => 'revenue',
                'account_category' => 'sales_revenue',
                'parent_id' => 14, // سيتم تحديثه
                'level' => 2,
                'nature' => 'credit',
                'is_system' => false,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '4020',
                'account_name' => 'مبيعات الأدوات الصحية',
                'account_name_en' => 'Sanitary Sales',
                'account_type' => 'revenue',
                'account_category' => 'sales_revenue',
                'parent_id' => 14, // سيتم تحديثه
                'level' => 2,
                'nature' => 'credit',
                'is_system' => false,
                'is_active' => true,
            ],

            // تكلفة البضاعة المباعة
            [
                'tenant_id' => $tenantId,
                'account_code' => '5000',
                'account_name' => 'تكلفة البضاعة المباعة',
                'account_name_en' => 'Cost of Goods Sold',
                'account_type' => 'cost_of_goods_sold',
                'account_category' => 'cogs',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
            ],

            // المصروفات
            [
                'tenant_id' => $tenantId,
                'account_code' => '6000',
                'account_name' => 'المصروفات التشغيلية',
                'account_name_en' => 'Operating Expenses',
                'account_type' => 'expenses',
                'account_category' => 'operating_expenses',
                'parent_id' => null,
                'level' => 1,
                'nature' => 'debit',
                'is_system' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '6010',
                'account_name' => 'مصروفات الإيجار',
                'account_name_en' => 'Rent Expense',
                'account_type' => 'expenses',
                'account_category' => 'operating_expenses',
                'parent_id' => 18, // سيتم تحديثه
                'level' => 2,
                'nature' => 'debit',
                'is_system' => false,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenantId,
                'account_code' => '6020',
                'account_name' => 'مصروفات الكهرباء',
                'account_name_en' => 'Electricity Expense',
                'account_type' => 'expenses',
                'account_category' => 'operating_expenses',
                'parent_id' => 18, // سيتم تحديثه
                'level' => 2,
                'nature' => 'debit',
                'is_system' => false,
                'is_active' => true,
            ],
        ];

        // سيتم استخدام هذا في factory أو عند إنشاء tenant جديد
        // return $accounts;
    }
}
