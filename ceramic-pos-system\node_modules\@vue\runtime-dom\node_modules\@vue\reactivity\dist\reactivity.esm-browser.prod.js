/**
* @vue/reactivity v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/let e,t,i,s,r;let n={},l=()=>{},o=Object.assign,a=Object.prototype.hasOwnProperty,u=(e,t)=>a.call(e,t),h=Array.isArray,c=e=>"[object Map]"===_(e),f=e=>"symbol"==typeof e,p=e=>null!==e&&"object"==typeof e,d=Object.prototype.toString,_=e=>d.call(e),v=e=>"string"==typeof e&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,g=(e,t)=>!Object.is(e,t);class y{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=e,!t&&e&&(this.index=(e.scopes||(e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(t){if(this._active){let i=e;try{return e=this,t()}finally{e=i}}}on(){1==++this._on&&(this.prevScope=e,e=this)}off(){this._on>0&&0==--this._on&&(e=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,i;for(t=0,this._active=!1,i=this.effects.length;t<i;t++)this.effects[t].stop();for(t=0,this.effects.length=0,i=this.cleanups.length;t<i;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,i=this.scopes.length;t<i;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function R(e){return new y(e)}function b(){return e}function w(t,i=!1){e&&e.cleanups.push(t)}let S={ACTIVE:1,1:"ACTIVE",RUNNING:2,2:"RUNNING",TRACKING:4,4:"TRACKING",NOTIFIED:8,8:"NOTIFIED",DIRTY:16,16:"DIRTY",ALLOW_RECURSE:32,32:"ALLOW_RECURSE",PAUSED:64,64:"PAUSED",EVALUATED:128,128:"EVALUATED"},E=new WeakSet;class x{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,e&&e.active&&e.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,E.has(this)&&(E.delete(this),this.trigger()))}notify(){(!(2&this.flags)||32&this.flags)&&(8&this.flags||m(this))}run(){if(!(1&this.flags))return this.fn();this.flags|=2,U(this),k(this);let e=t,i=P;t=this,P=!0;try{return this.fn()}finally{D(this),t=e,P=i,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)L(e);this.deps=this.depsTail=void 0,U(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?E.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){O(this)&&this.run()}get dirty(){return O(this)}}let T=0;function m(e,t=!1){if(e.flags|=8,t){e.next=s,s=e;return}e.next=i,i=e}function A(){let e;if(!(--T>0)){if(s){let e=s;for(s=void 0;e;){let t=e.next;e.next=void 0,e.flags&=-9,e=t}}for(;i;){let t=i;for(i=void 0;t;){let i=t.next;if(t.next=void 0,t.flags&=-9,1&t.flags)try{t.trigger()}catch(t){e||(e=t)}t=i}}if(e)throw e}}function k(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function D(e){let t,i=e.depsTail,s=i;for(;s;){let e=s.prevDep;-1===s.version?(s===i&&(i=e),L(s),function(e){let{prevDep:t,nextDep:i}=e;t&&(t.nextDep=i,e.prevDep=void 0),i&&(i.prevDep=t,e.nextDep=void 0)}(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=e}e.deps=t,e.depsTail=i}function O(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(I(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function I(e){if(4&e.flags&&!(16&e.flags)||(e.flags&=-17,e.globalVersion===H)||(e.globalVersion=H,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!O(e))))return;e.flags|=2;let i=e.dep,s=t,r=P;t=e,P=!0;try{k(e);let t=e.fn(e._value);(0===i.version||g(t,e._value))&&(e.flags|=128,e._value=t,i.version++)}catch(e){throw i.version++,e}finally{t=s,P=r,D(e),e.flags&=-3}}function L(e,t=!1){let{dep:i,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),i.subs===e&&(i.subs=s,!s&&i.computed)){i.computed.flags&=-5;for(let e=i.computed.deps;e;e=e.nextDep)L(e,!0)}t||--i.sc||!i.map||i.map.delete(i.key)}function j(e,t){e.effect instanceof x&&(e=e.effect.fn);let i=new x(e);t&&o(i,t);try{i.run()}catch(e){throw i.stop(),e}let s=i.run.bind(i);return s.effect=i,s}function C(e){e.effect.stop()}let P=!0,W=[];function N(){W.push(P),P=!1}function V(){W.push(P),P=!0}function K(){let e=W.pop();P=void 0===e||e}function M(e,i=!1){t instanceof x&&(t.cleanup=e)}function U(e){let{cleanup:i}=e;if(e.cleanup=void 0,i){let e=t;t=void 0;try{i()}finally{t=e}}}let H=0;class Y{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class G{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!t||!P||t===this.computed)return;let i=this.activeLink;if(void 0===i||i.sub!==t)i=this.activeLink=new Y(t,this),t.deps?(i.prevDep=t.depsTail,t.depsTail.nextDep=i,t.depsTail=i):t.deps=t.depsTail=i,function e(t){if(t.dep.sc++,4&t.sub.flags){let i=t.dep.computed;if(i&&!t.dep.subs){i.flags|=20;for(let t=i.deps;t;t=t.nextDep)e(t)}let s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}}(i);else if(-1===i.version&&(i.version=this.version,i.nextDep)){let e=i.nextDep;e.prevDep=i.prevDep,i.prevDep&&(i.prevDep.nextDep=e),i.prevDep=t.depsTail,i.nextDep=void 0,t.depsTail.nextDep=i,t.depsTail=i,t.deps===i&&(t.deps=e)}return i}trigger(e){this.version++,H++,this.notify(e)}notify(e){T++;try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{A()}}}let F=new WeakMap,z=Symbol(""),B=Symbol(""),q=Symbol("");function J(e,i,s){if(P&&t){let t=F.get(e);t||F.set(e,t=new Map);let i=t.get(s);i||(t.set(s,i=new G),i.map=t,i.key=s),i.track()}}function Q(e,t,i,s,r,n){let l=F.get(e);if(!l)return void H++;let o=e=>{e&&e.trigger()};if(T++,"clear"===t)l.forEach(o);else{let r=h(e),n=r&&v(i);if(r&&"length"===i){let e=Number(s);l.forEach((t,i)=>{("length"===i||i===q||!f(i)&&i>=e)&&o(t)})}else switch((void 0!==i||l.has(void 0))&&o(l.get(i)),n&&o(l.get(q)),t){case"add":r?n&&o(l.get("length")):(o(l.get(z)),c(e)&&o(l.get(B)));break;case"delete":!r&&(o(l.get(z)),c(e)&&o(l.get(B)));break;case"set":c(e)&&o(l.get(z))}}A()}function X(e){let t=eN(e);return t===e?t:(J(t,"iterate",q),eP(e)?t:t.map(eK))}function Z(e){return J(e=eN(e),"iterate",q),e}let $={__proto__:null,[Symbol.iterator](){return ee(this,Symbol.iterator,eK)},concat(...e){return X(this).concat(...e.map(e=>h(e)?X(e):e))},entries(){return ee(this,"entries",e=>(e[1]=eK(e[1]),e))},every(e,t){return ei(this,"every",e,t,void 0,arguments)},filter(e,t){return ei(this,"filter",e,t,e=>e.map(eK),arguments)},find(e,t){return ei(this,"find",e,t,eK,arguments)},findIndex(e,t){return ei(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ei(this,"findLast",e,t,eK,arguments)},findLastIndex(e,t){return ei(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ei(this,"forEach",e,t,void 0,arguments)},includes(...e){return er(this,"includes",e)},indexOf(...e){return er(this,"indexOf",e)},join(e){return X(this).join(e)},lastIndexOf(...e){return er(this,"lastIndexOf",e)},map(e,t){return ei(this,"map",e,t,void 0,arguments)},pop(){return en(this,"pop")},push(...e){return en(this,"push",e)},reduce(e,...t){return es(this,"reduce",e,t)},reduceRight(e,...t){return es(this,"reduceRight",e,t)},shift(){return en(this,"shift")},some(e,t){return ei(this,"some",e,t,void 0,arguments)},splice(...e){return en(this,"splice",e)},toReversed(){return X(this).toReversed()},toSorted(e){return X(this).toSorted(e)},toSpliced(...e){return X(this).toSpliced(...e)},unshift(...e){return en(this,"unshift",e)},values(){return ee(this,"values",eK)}};function ee(e,t,i){let s=Z(e),r=s[t]();return s===e||eP(e)||(r._next=r.next,r.next=()=>{let e=r._next();return e.value&&(e.value=i(e.value)),e}),r}let et=Array.prototype;function ei(e,t,i,s,r,n){let l=Z(e),o=l!==e&&!eP(e),a=l[t];if(a!==et[t]){let t=a.apply(e,n);return o?eK(t):t}let u=i;l!==e&&(o?u=function(t,s){return i.call(this,eK(t),s,e)}:i.length>2&&(u=function(t,s){return i.call(this,t,s,e)}));let h=a.call(l,u,s);return o&&r?r(h):h}function es(e,t,i,s){let r=Z(e),n=i;return r!==e&&(eP(e)?i.length>3&&(n=function(t,s,r){return i.call(this,t,s,r,e)}):n=function(t,s,r){return i.call(this,t,eK(s),r,e)}),r[t](n,...s)}function er(e,t,i){let s=eN(e);J(s,"iterate",q);let r=s[t](...i);return(-1===r||!1===r)&&eW(i[0])?(i[0]=eN(i[0]),s[t](...i)):r}function en(e,t,i=[]){N(),T++;let s=eN(e)[t].apply(e,i);return A(),K(),s}let el=function(e){let t=Object.create(null);for(let i of e.split(","))t[i]=1;return e=>e in t}("__proto__,__v_isRef,__isVue"),eo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(f));function ea(e){f(e)||(e=String(e));let t=eN(this);return J(t,"has",e),t.hasOwnProperty(e)}class eu{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,i){if("__v_skip"===t)return e.__v_skip;let s=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!s;if("__v_isReadonly"===t)return s;if("__v_isShallow"===t)return r;if("__v_raw"===t)return i===(s?r?eA:em:r?eT:ex).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(i)?e:void 0;let n=h(e);if(!s){let e;if(n&&(e=$[t]))return e;if("hasOwnProperty"===t)return ea}let l=Reflect.get(e,t,eU(e)?e:i);return(f(t)?eo.has(t):el(t))||(s||J(e,"get",t),r)?l:eU(l)?n&&v(t)?l:l.value:p(l)?s?eO(l):ek(l):l}}class eh extends eu{constructor(e=!1){super(!1,e)}set(e,t,i,s){let r=e[t];if(!this._isShallow){let t=eC(r);if(eP(i)||eC(i)||(r=eN(r),i=eN(i)),!h(e)&&eU(r)&&!eU(i))if(t)return!1;else return r.value=i,!0}let n=h(e)&&v(t)?Number(t)<e.length:u(e,t),l=Reflect.set(e,t,i,eU(e)?e:s);return e===eN(s)&&(n?g(i,r)&&Q(e,"set",t,i):Q(e,"add",t,i)),l}deleteProperty(e,t){let i=u(e,t);e[t];let s=Reflect.deleteProperty(e,t);return s&&i&&Q(e,"delete",t,void 0),s}has(e,t){let i=Reflect.has(e,t);return f(t)&&eo.has(t)||J(e,"has",t),i}ownKeys(e){return J(e,"iterate",h(e)?"length":z),Reflect.ownKeys(e)}}class ec extends eu{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}let ef=new eh,ep=new ec,ed=new eh(!0),e_=new ec(!0),ev=e=>e,eg=e=>Reflect.getPrototypeOf(e);function ey(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function eR(e,t){let i=function(e,t){let i={get(i){let s=this.__v_raw,r=eN(s),n=eN(i);e||(g(i,n)&&J(r,"get",i),J(r,"get",n));let{has:l}=eg(r),o=t?ev:e?eM:eK;return l.call(r,i)?o(s.get(i)):l.call(r,n)?o(s.get(n)):void(s!==r&&s.get(i))},get size(){let t=this.__v_raw;return e||J(eN(t),"iterate",z),Reflect.get(t,"size",t)},has(t){let i=this.__v_raw,s=eN(i),r=eN(t);return e||(g(t,r)&&J(s,"has",t),J(s,"has",r)),t===r?i.has(t):i.has(t)||i.has(r)},forEach(i,s){let r=this,n=r.__v_raw,l=eN(n),o=t?ev:e?eM:eK;return e||J(l,"iterate",z),n.forEach((e,t)=>i.call(s,o(e),o(t),r))}};return o(i,e?{add:ey("add"),set:ey("set"),delete:ey("delete"),clear:ey("clear")}:{add(e){t||eP(e)||eC(e)||(e=eN(e));let i=eN(this);return eg(i).has.call(i,e)||(i.add(e),Q(i,"add",e,e)),this},set(e,i){t||eP(i)||eC(i)||(i=eN(i));let s=eN(this),{has:r,get:n}=eg(s),l=r.call(s,e);l||(e=eN(e),l=r.call(s,e));let o=n.call(s,e);return s.set(e,i),l?g(i,o)&&Q(s,"set",e,i):Q(s,"add",e,i),this},delete(e){let t=eN(this),{has:i,get:s}=eg(t),r=i.call(t,e);r||(e=eN(e),r=i.call(t,e)),s&&s.call(t,e);let n=t.delete(e);return r&&Q(t,"delete",e,void 0),n},clear(){let e=eN(this),t=0!==e.size,i=e.clear();return t&&Q(e,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{i[s]=function(...i){let r=this.__v_raw,n=eN(r),l=c(n),o="entries"===s||s===Symbol.iterator&&l,a=r[s](...i),u=t?ev:e?eM:eK;return e||J(n,"iterate","keys"===s&&l?B:z),{next(){let{value:e,done:t}=a.next();return t?{value:e,done:t}:{value:o?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}),i}(e,t);return(t,s,r)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(u(i,s)&&s in t?i:t,s,r)}let eb={get:eR(!1,!1)},ew={get:eR(!1,!0)},eS={get:eR(!0,!1)},eE={get:eR(!0,!0)},ex=new WeakMap,eT=new WeakMap,em=new WeakMap,eA=new WeakMap;function ek(e){return eC(e)?e:eL(e,!1,ef,eb,ex)}function eD(e){return eL(e,!1,ed,ew,eT)}function eO(e){return eL(e,!0,ep,eS,em)}function eI(e){return eL(e,!0,e_,eE,eA)}function eL(e,t,i,s,r){var n;if(!p(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;let l=(n=e).__v_skip||!Object.isExtensible(n)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(_(n).slice(8,-1));if(0===l)return e;let o=r.get(e);if(o)return o;let a=new Proxy(e,2===l?s:i);return r.set(e,a),a}function ej(e){return eC(e)?ej(e.__v_raw):!!(e&&e.__v_isReactive)}function eC(e){return!!(e&&e.__v_isReadonly)}function eP(e){return!!(e&&e.__v_isShallow)}function eW(e){return!!e&&!!e.__v_raw}function eN(e){let t=e&&e.__v_raw;return t?eN(t):e}function eV(e){return!u(e,"__v_skip")&&Object.isExtensible(e)&&((e,t,i,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:i})})(e,"__v_skip",!0),e}let eK=e=>p(e)?ek(e):e,eM=e=>p(e)?eO(e):e;function eU(e){return!!e&&!0===e.__v_isRef}function eH(e){return eG(e,!1)}function eY(e){return eG(e,!0)}function eG(e,t){return eU(e)?e:new eF(e,t)}class eF{constructor(e,t){this.dep=new G,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:eN(e),this._value=t?e:eK(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,i=this.__v_isShallow||eP(e)||eC(e);g(e=i?e:eN(e),t)&&(this._rawValue=e,this._value=i?e:eK(e),this.dep.trigger())}}function ez(e){e.dep&&e.dep.trigger()}function eB(e){return eU(e)?e.value:e}function eq(e){return"function"==typeof e?e():eB(e)}let eJ={get:(e,t,i)=>"__v_raw"===t?e:eB(Reflect.get(e,t,i)),set:(e,t,i,s)=>{let r=e[t];return eU(r)&&!eU(i)?(r.value=i,!0):Reflect.set(e,t,i,s)}};function eQ(e){return ej(e)?e:new Proxy(e,eJ)}class eX{constructor(e){this.__v_isRef=!0,this._value=void 0;let t=this.dep=new G,{get:i,set:s}=e(t.track.bind(t),t.trigger.bind(t));this._get=i,this._set=s}get value(){return this._value=this._get()}set value(e){this._set(e)}}function eZ(e){return new eX(e)}function e$(e){let t=h(e)?Array(e.length):{};for(let i in e)t[i]=e4(e,i);return t}class e0{constructor(e,t,i){this._object=e,this._key=t,this._defaultValue=i,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){let i=F.get(e);return i&&i.get(t)}(eN(this._object),this._key)}}class e1{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function e2(e,t,i){return eU(e)?e:"function"==typeof e?new e1(e):p(e)&&arguments.length>1?e4(e,t,i):eH(e)}function e4(e,t,i){let s=e[t];return eU(s)?s:new e0(e,t,i)}class e6{constructor(e,t,i){this.fn=e,this.setter=t,this._value=void 0,this.dep=new G(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=H-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=i}notify(){if(this.flags|=16,!(8&this.flags)&&t!==this)return m(this,!0),!0}get value(){let e=this.dep.track();return I(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function e3(e,t,i=!1){let s,r;return"function"==typeof e?s=e:(s=e.get,r=e.set),new e6(s,r,i)}let e8={GET:"get",HAS:"has",ITERATE:"iterate"},e5={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},e9={SKIP:"__v_skip",IS_REACTIVE:"__v_isReactive",IS_READONLY:"__v_isReadonly",IS_SHALLOW:"__v_isShallow",RAW:"__v_raw",IS_REF:"__v_isRef"},e7={WATCH_GETTER:2,2:"WATCH_GETTER",WATCH_CALLBACK:3,3:"WATCH_CALLBACK",WATCH_CLEANUP:4,4:"WATCH_CLEANUP"},te={},tt=new WeakMap;function ti(){return r}function ts(e,t=!1,i=r){if(i){let t=tt.get(i);t||tt.set(i,t=[]),t.push(e)}}function tr(e,t,i=n){let s,o,a,u,{immediate:c,deep:f,once:p,scheduler:d,augmentJob:_,call:v}=i,y=e=>f?e:eP(e)||!1===f||0===f?tn(e,1):tn(e),R=!1,w=!1;if(eU(e)?(o=()=>e.value,R=eP(e)):ej(e)?(o=()=>y(e),R=!0):h(e)?(w=!0,R=e.some(e=>ej(e)||eP(e)),o=()=>e.map(e=>eU(e)?e.value:ej(e)?y(e):"function"==typeof e?v?v(e,2):e():void 0)):o="function"==typeof e?t?v?()=>v(e,2):e:()=>{if(a){N();try{a()}finally{K()}}let t=r;r=s;try{return v?v(e,3,[u]):e(u)}finally{r=t}}:l,t&&f){let e=o,t=!0===f?1/0:f;o=()=>tn(e(),t)}let S=b(),E=()=>{s.stop(),S&&S.active&&((e,t)=>{let i=e.indexOf(t);i>-1&&e.splice(i,1)})(S.effects,s)};if(p&&t){let e=t;t=(...t)=>{e(...t),E()}}let T=w?Array(e.length).fill(te):te,m=e=>{if(1&s.flags&&(s.dirty||e))if(t){let e=s.run();if(f||R||(w?e.some((e,t)=>g(e,T[t])):g(e,T))){a&&a();let i=r;r=s;try{let i=[e,T===te?void 0:w&&T[0]===te?[]:T,u];T=e,v?v(t,3,i):t(...i)}finally{r=i}}}else s.run()};return _&&_(m),(s=new x(o)).scheduler=d?()=>d(m,!1):m,u=e=>ts(e,!1,s),a=s.onStop=()=>{let e=tt.get(s);if(e){if(v)v(e,4);else for(let t of e)t();tt.delete(s)}},t?c?m(!0):T=s.run():d?d(m.bind(null,!0),!0):s.run(),E.pause=s.pause.bind(s),E.resume=s.resume.bind(s),E.stop=E,E}function tn(e,t=1/0,i){if(t<=0||!p(e)||e.__v_skip||(i=i||new Set).has(e))return e;if(i.add(e),t--,eU(e))tn(e.value,t,i);else if(h(e))for(let s=0;s<e.length;s++)tn(e[s],t,i);else if("[object Set]"===_(e)||c(e))e.forEach(e=>{tn(e,t,i)});else if("[object Object]"===_(e)){for(let s in e)tn(e[s],t,i);for(let s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&tn(e[s],t,i)}return e}export{q as ARRAY_ITERATE_KEY,S as EffectFlags,y as EffectScope,z as ITERATE_KEY,B as MAP_KEY_ITERATE_KEY,x as ReactiveEffect,e9 as ReactiveFlags,e8 as TrackOpTypes,e5 as TriggerOpTypes,e7 as WatchErrorCodes,e3 as computed,eZ as customRef,j as effect,R as effectScope,V as enableTracking,b as getCurrentScope,ti as getCurrentWatcher,eW as isProxy,ej as isReactive,eC as isReadonly,eU as isRef,eP as isShallow,eV as markRaw,M as onEffectCleanup,w as onScopeDispose,ts as onWatcherCleanup,N as pauseTracking,eQ as proxyRefs,ek as reactive,X as reactiveReadArray,eO as readonly,eH as ref,K as resetTracking,eD as shallowReactive,Z as shallowReadArray,eI as shallowReadonly,eY as shallowRef,C as stop,eN as toRaw,eK as toReactive,eM as toReadonly,e2 as toRef,e$ as toRefs,eq as toValue,J as track,tn as traverse,Q as trigger,ez as triggerRef,eB as unref,tr as watch};
