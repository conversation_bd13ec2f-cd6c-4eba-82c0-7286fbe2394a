@extends('layouts.app')

@section('title', 'التقارير')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-chart-line me-2 text-primary"></i>
                التقارير
            </h2>
            <p class="text-muted mb-0">{{ $tenant->company_name }}</p>
        </div>
    </div>

    <!-- Reports Grid -->
    <div class="row">
        <!-- Sales Reports -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 report-card">
                <div class="card-body text-center">
                    <div class="report-icon text-success mb-3">
                        <i class="fas fa-chart-line fa-3x"></i>
                    </div>
                    <h4 class="card-title">تقارير المبيعات</h4>
                    <p class="card-text text-muted">
                        تقارير شاملة عن المبيعات اليومية والشهرية، أفضل العملاء، والمنتجات الأكثر مبيعاً
                    </p>
                    <div class="mt-auto">
                        <a href="{{ route('reports.sales', ['tenant' => $tenant->id]) }}" class="btn btn-success">
                            <i class="fas fa-eye me-2"></i>
                            عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Reports -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 report-card">
                <div class="card-body text-center">
                    <div class="report-icon text-warning mb-3">
                        <i class="fas fa-boxes fa-3x"></i>
                    </div>
                    <h4 class="card-title">تقارير المخزون</h4>
                    <p class="card-text text-muted">
                        تقارير المخزون الحالي، المنتجات منخفضة المخزون، وقيمة المخزون الإجمالية
                    </p>
                    <div class="mt-auto">
                        <a href="{{ route('reports.inventory', ['tenant' => $tenant->id]) }}" class="btn btn-warning">
                            <i class="fas fa-eye me-2"></i>
                            عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Reports -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 report-card">
                <div class="card-body text-center">
                    <div class="report-icon text-info mb-3">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                    <h4 class="card-title">تقارير العملاء</h4>
                    <p class="card-text text-muted">
                        تحليل سلوك العملاء، أفضل العملاء، والعملاء الجدد خلال فترة محددة
                    </p>
                    <div class="mt-auto">
                        <a href="{{ route('reports.customers', ['tenant' => $tenant->id]) }}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i>
                            عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Reports -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 report-card">
                <div class="card-body text-center">
                    <div class="report-icon text-primary mb-3">
                        <i class="fas fa-credit-card fa-3x"></i>
                    </div>
                    <h4 class="card-title">تقارير المدفوعات</h4>
                    <p class="card-text text-muted">
                        تقارير المدفوعات حسب طريقة الدفع، المدفوعات المعلقة، والتدفق النقدي
                    </p>
                    <div class="mt-auto">
                        <a href="{{ route('reports.payments', ['tenant' => $tenant->id]) }}" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>
                            عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Reports -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 report-card">
                <div class="card-body text-center">
                    <div class="report-icon text-danger mb-3">
                        <i class="fas fa-calculator fa-3x"></i>
                    </div>
                    <h4 class="card-title">التقارير المالية</h4>
                    <p class="card-text text-muted">
                        الأرباح والخسائر، الميزانية العمومية، والتدفق النقدي
                    </p>
                    <div class="mt-auto">
                        <a href="{{ route('accounting.profit-loss', ['tenant' => $tenant->id]) }}" class="btn btn-danger">
                            <i class="fas fa-eye me-2"></i>
                            عرض التقرير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom Reports -->
        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card h-100 report-card">
                <div class="card-body text-center">
                    <div class="report-icon text-secondary mb-3">
                        <i class="fas fa-cog fa-3x"></i>
                    </div>
                    <h4 class="card-title">تقارير مخصصة</h4>
                    <p class="card-text text-muted">
                        إنشاء تقارير مخصصة حسب احتياجاتك مع إمكانية تصدير البيانات
                    </p>
                    <div class="mt-auto">
                        <button class="btn btn-secondary" disabled>
                            <i class="fas fa-tools me-2"></i>
                            قريباً
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        إحصائيات سريعة - اليوم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">
                                    {{ \App\Models\Invoice::where('type', 'sale')->whereDate('created_at', today())->count() }}
                                </h4>
                                <p class="text-muted mb-0">فواتير اليوم</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary">
                                    {{ number_format(\App\Models\Invoice::where('type', 'sale')->whereDate('created_at', today())->sum('total_amount'), 2) }}
                                </h4>
                                <p class="text-muted mb-0">مبيعات اليوم (ج.م)</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-warning">
                                    {{ \App\Models\Customer::whereDate('created_at', today())->count() }}
                                </h4>
                                <p class="text-muted mb-0">عملاء جدد</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info">
                                {{ \App\Models\Product::whereRaw('COALESCE((SELECT SUM(quantity) FROM product_stock WHERE product_id = products.id), 0) <= minimum_stock')->count() }}
                            </h4>
                            <p class="text-muted mb-0">منتجات منخفضة المخزون</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-download me-2"></i>
                        خيارات التصدير
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">يمكنك تصدير التقارير بصيغ مختلفة:</p>
                    <div class="btn-group">
                        <button class="btn btn-outline-success" disabled>
                            <i class="fas fa-file-excel me-2"></i>
                            Excel
                        </button>
                        <button class="btn btn-outline-danger" disabled>
                            <i class="fas fa-file-pdf me-2"></i>
                            PDF
                        </button>
                        <button class="btn btn-outline-primary" disabled>
                            <i class="fas fa-file-csv me-2"></i>
                            CSV
                        </button>
                    </div>
                    <small class="text-muted d-block mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        ميزة التصدير ستكون متاحة قريباً
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.report-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.report-icon {
    opacity: 0.8;
}

.report-card:hover .report-icon {
    opacity: 1;
}
</style>
@endpush
@endsection
