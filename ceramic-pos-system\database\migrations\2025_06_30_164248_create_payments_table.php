<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('payment_number')->unique(); // رقم المدفوعة
            $table->string('payment_type'); // received, paid
            $table->date('payment_date'); // تاريخ المدفوعة

            // معلومات العميل/المورد
            $table->foreignId('customer_id')->nullable()->constrained('customers')->onDelete('set null');
            $table->foreignId('supplier_id')->nullable()->constrained('suppliers')->onDelete('set null');
            $table->string('payer_name')->nullable(); // اسم الدافع/المستلم

            // معلومات الدفع
            $table->string('payment_method'); // cash, bank_transfer, check, card, other
            $table->decimal('amount', 15, 2); // المبلغ
            $table->string('currency', 3)->default('EGP'); // العملة
            $table->decimal('exchange_rate', 10, 4)->default(1); // سعر الصرف
            $table->decimal('amount_in_base_currency', 15, 2); // المبلغ بالعملة الأساسية

            // تفاصيل طريقة الدفع
            $table->string('bank_name')->nullable(); // اسم البنك
            $table->string('account_number')->nullable(); // رقم الحساب
            $table->string('check_number')->nullable(); // رقم الشيك
            $table->date('check_date')->nullable(); // تاريخ الشيك
            $table->string('reference_number')->nullable(); // رقم المرجع
            $table->string('transaction_id')->nullable(); // معرف المعاملة

            // الحالة
            $table->string('status')->default('pending'); // pending, cleared, bounced, cancelled
            $table->date('cleared_date')->nullable(); // تاريخ المقاصة

            // ملاحظات
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // بيانات إضافية

            // معلومات النظام
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('cleared_at')->nullable(); // تاريخ المقاصة
            $table->foreignId('cleared_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['tenant_id', 'payment_type']);
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'payment_date']);
            $table->index(['tenant_id', 'customer_id']);
            $table->index(['tenant_id', 'supplier_id']);
            $table->index(['tenant_id', 'payment_method']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
