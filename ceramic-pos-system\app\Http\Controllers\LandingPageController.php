<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PlanFeature;

class LandingPageController extends Controller
{
    /**
     * عرض الصفحة الرئيسية
     */
    public function index()
    {
        // مؤقتاً - إنشاء خطط تجريبية
        $plans = collect([
            (object)[
                'id' => 1,
                'plan_key' => 'basic',
                'name' => 'الخطة الأساسية',
                'description' => 'مناسبة للشركات الصغيرة',
                'price' => 99.00,
                'max_users' => 2,
                'max_products' => 500,
                'max_customers' => 200,
                'max_invoices_per_month' => 100,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 1
            ],
            (object)[
                'id' => 2,
                'plan_key' => 'premium',
                'name' => 'الخطة المتقدمة',
                'description' => 'مناسبة للشركات المتوسطة',
                'price' => 199.00,
                'max_users' => 5,
                'max_products' => 2000,
                'max_customers' => 1000,
                'max_invoices_per_month' => 500,
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 2
            ],
            (object)[
                'id' => 3,
                'plan_key' => 'enterprise',
                'name' => 'خطة المؤسسات',
                'description' => 'مناسبة للشركات الكبيرة',
                'price' => 399.00,
                'max_users' => 20,
                'max_products' => 10000,
                'max_customers' => 5000,
                'max_invoices_per_month' => 2000,
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 3
            ]
        ]);

        $settings = $this->getLandingPageSettings();

        return view('saas.landing', compact('plans', 'settings'));
    }

    /**
     * إدارة محتوى الصفحة الرئيسية (Super Admin)
     */
    public function manage()
    {
        $settings = $this->getLandingPageSettings();

        return view('super-admin.landing-page', compact('settings'));
    }

    /**
     * تحديث محتوى الصفحة الرئيسية
     */
    public function update(Request $request)
    {
        $request->validate([
            'hero_title' => 'required|string|max:255',
            'hero_subtitle' => 'required|string|max:500',
            'hero_description' => 'required|string|max:1000',
            'features_title' => 'required|string|max:255',
            'features_subtitle' => 'required|string|max:500',
            'pricing_title' => 'required|string|max:255',
            'pricing_subtitle' => 'required|string|max:500',
            'contact_email' => 'required|email',
            'contact_phone' => 'required|string|max:20',
            'contact_address' => 'required|string|max:255',
            'social_facebook' => 'nullable|url',
            'social_twitter' => 'nullable|url',
            'social_linkedin' => 'nullable|url',
            'social_instagram' => 'nullable|url',
        ]);

        // حفظ الإعدادات في ملف أو قاعدة بيانات
        $settings = $request->only([
            'hero_title', 'hero_subtitle', 'hero_description',
            'features_title', 'features_subtitle',
            'pricing_title', 'pricing_subtitle',
            'contact_email', 'contact_phone', 'contact_address',
            'social_facebook', 'social_twitter', 'social_linkedin', 'social_instagram'
        ]);

        // حفظ في ملف JSON مؤقتاً
        file_put_contents(
            storage_path('app/landing_page_settings.json'),
            json_encode($settings, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)
        );

        return back()->with('success', 'تم تحديث محتوى الصفحة الرئيسية بنجاح');
    }

    /**
     * الحصول على إعدادات الصفحة الرئيسية
     */
    private function getLandingPageSettings()
    {
        $defaultSettings = [
            'hero_title' => 'نظام إدارة نقاط البيع الأكثر تطوراً',
            'hero_subtitle' => 'مخصص لصناعة السيراميك والأدوات الصحية',
            'hero_description' => 'نظام شامل لإدارة نقاط البيع والمخزون والعملاء مع تقارير متقدمة وواجهة سهلة الاستخدام',
            'features_title' => 'مميزات النظام',
            'features_subtitle' => 'كل ما تحتاجه لإدارة أعمالك بكفاءة',
            'pricing_title' => 'خطط الأسعار',
            'pricing_subtitle' => 'اختر الخطة المناسبة لحجم أعمالك',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+20 ************',
            'contact_address' => 'القاهرة، مصر',
            'social_facebook' => 'https://facebook.com/ceramicpos',
            'social_twitter' => 'https://twitter.com/ceramicpos',
            'social_linkedin' => 'https://linkedin.com/company/ceramicpos',
            'social_instagram' => 'https://instagram.com/ceramicpos',
        ];

        $settingsFile = storage_path('app/landing_page_settings.json');

        if (file_exists($settingsFile)) {
            $savedSettings = json_decode(file_get_contents($settingsFile), true);
            return array_merge($defaultSettings, $savedSettings);
        }

        return $defaultSettings;
    }
}
