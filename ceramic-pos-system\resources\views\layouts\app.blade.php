<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'نظام إدارة السيراميك')</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }

        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }

        .alert {
            border: none;
            border-radius: 10px;
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ route('dashboard', ['tenant' => request('tenant')]) }}">
                <i class="fas fa-cube me-2"></i>
                Ceramic POS
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    @if(isset($tenant))
                        <span class="navbar-text me-3">
                            {{ $tenant->company_name }} | {{ auth()->user()->name }}
                        </span>
                    @endif

                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ auth()->user()->name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('tenant.select') }}">
                                <i class="fas fa-exchange-alt me-2"></i>تغيير الشركة
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <nav class="nav flex-column py-3">
                        <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}"
                           href="{{ route('dashboard', ['tenant' => request('tenant')]) }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>

                        <hr class="my-2">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1 text-muted">
                            نقاط البيع
                        </h6>
                        <a class="nav-link {{ request()->routeIs('pos.*') ? 'active' : '' }}"
                           href="{{ route('pos.index', ['tenant' => request('tenant')]) }}">
                            <i class="fas fa-cash-register me-2"></i>
                            نقطة البيع
                        </a>
                        <a class="nav-link {{ request()->routeIs('invoices.*') ? 'active' : '' }}"
                           href="{{ route('invoices.index', ['tenant' => request('tenant')]) }}">
                            <i class="fas fa-file-invoice me-2"></i>
                            الفواتير
                        </a>

                        <hr class="my-2">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1 text-muted">
                            إدارة المخزون
                        </h6>
                        <a class="nav-link {{ request()->routeIs('inventory.products.*') ? 'active' : '' }}"
                           href="{{ route('inventory.products.index', ['tenant' => request('tenant')]) }}">
                            <i class="fas fa-boxes me-2"></i>
                            المنتجات
                        </a>
                        <a class="nav-link" href="/inventory/categories?tenant={{ request('tenant') }}">
                            <i class="fas fa-tags me-2"></i>
                            الفئات
                        </a>
                        <a class="nav-link" href="/inventory?tenant={{ request('tenant') }}">
                            <i class="fas fa-warehouse me-2"></i>
                            حركات المخزون
                        </a>

                        <hr class="my-2">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1 text-muted">
                            إدارة العملاء
                        </h6>
                        <a class="nav-link {{ request()->routeIs('customers.*') ? 'active' : '' }}"
                           href="{{ route('customers.index', ['tenant' => request('tenant')]) }}">
                            <i class="fas fa-users me-2"></i>
                            العملاء
                        </a>
                        <a class="nav-link" href="/suppliers?tenant={{ request('tenant') }}">
                            <i class="fas fa-truck me-2"></i>
                            الموردين
                        </a>

                        <hr class="my-2">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1 text-muted">
                            المحاسبة
                        </h6>
                        <a class="nav-link {{ request()->routeIs('accounting.*') ? 'active' : '' }}"
                           href="{{ route('accounting.index', ['tenant' => request('tenant')]) }}">
                            <i class="fas fa-calculator me-2"></i>
                            المحاسبة
                        </a>
                        <a class="nav-link {{ request()->routeIs('payments.*') ? 'active' : '' }}"
                           href="{{ route('payments.index', ['tenant' => request('tenant')]) }}">
                            <i class="fas fa-credit-card me-2"></i>
                            المدفوعات
                        </a>

                        <hr class="my-2">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1 text-muted">
                            التقارير
                        </h6>
                        <a class="nav-link {{ request()->routeIs('reports.*') ? 'active' : '' }}"
                           href="{{ route('reports.index', ['tenant' => request('tenant')]) }}">
                            <i class="fas fa-chart-line me-2"></i>
                            التقارير
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
                    <!-- Alerts -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>يرجى تصحيح الأخطاء التالية:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Page Content -->
                    @yield('content')
                    {{ $slot ?? '' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    @stack('scripts')
</body>
</html>
