<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chart_of_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('account_code', 20)->unique(); // رقم الحساب
            $table->string('account_name'); // اسم الحساب
            $table->string('account_name_en')->nullable(); // اسم الحساب بالإنجليزية
            $table->enum('account_type', [
                'assets', // الأصول
                'liabilities', // الخصوم
                'equity', // حقوق الملكية
                'revenue', // الإيرادات
                'expenses', // المصروفات
                'cost_of_goods_sold' // تكلفة البضاعة المباعة
            ]);
            $table->enum('account_category', [
                'current_assets', // الأصول المتداولة
                'fixed_assets', // الأصول الثابتة
                'current_liabilities', // الخصوم المتداولة
                'long_term_liabilities', // الخصوم طويلة الأجل
                'capital', // رأس المال
                'retained_earnings', // الأرباح المحتجزة
                'sales_revenue', // إيرادات المبيعات
                'other_revenue', // إيرادات أخرى
                'operating_expenses', // مصروفات تشغيلية
                'administrative_expenses', // مصروفات إدارية
                'financial_expenses', // مصروفات مالية
                'cogs' // تكلفة البضاعة المباعة
            ]);
            $table->unsignedBigInteger('parent_id')->nullable(); // الحساب الأب
            $table->integer('level')->default(1); // مستوى الحساب في الشجرة
            $table->string('path')->nullable(); // مسار الحساب في الشجرة
            $table->enum('nature', ['debit', 'credit']); // طبيعة الحساب
            $table->decimal('opening_balance', 15, 2)->default(0); // الرصيد الافتتاحي
            $table->decimal('current_balance', 15, 2)->default(0); // الرصيد الحالي
            $table->boolean('is_active')->default(true); // نشط/غير نشط
            $table->boolean('is_system')->default(false); // حساب نظام (لا يمكن حذفه)
            $table->text('description')->nullable(); // وصف الحساب
            $table->json('settings')->nullable(); // إعدادات إضافية
            $table->timestamps();

            $table->foreign('parent_id')->references('id')->on('chart_of_accounts')->onDelete('cascade');
            $table->index(['tenant_id', 'account_type']);
            $table->index(['tenant_id', 'parent_id']);
            $table->index(['tenant_id', 'account_code']);
            $table->index(['tenant_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chart_of_accounts');
    }
};
