<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PlanFeature extends Model
{
    protected $fillable = [
        'plan_key',
        'name',
        'description',
        'price',
        'max_users',
        'max_products',
        'max_customers',
        'max_invoices_per_month',
        'modules',
        'features',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'modules' => 'array',
        'features' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'price' => 'decimal:2',
    ];

    /**
     * الحصول على جميع الوحدات المتاحة
     */
    public static function getAvailableModules()
    {
        return [
            'pos' => 'نقاط البيع',
            'inventory' => 'إدارة المخزون',
            'customers' => 'إدارة العملاء',
            'suppliers' => 'إدارة الموردين',
            'invoices' => 'الفواتير',
            'accounting' => 'المحاسبة',
            'reports' => 'التقارير',
            'payments' => 'المدفوعات',
            'users' => 'إدارة المستخدمين',
            'settings' => 'الإعدادات',
            'api' => 'واجهة برمجة التطبيقات',
            'backup' => 'النسخ الاحتياطي',
        ];
    }

    /**
     * الحصول على المميزات الإضافية المتاحة
     */
    public static function getAvailableFeatures()
    {
        return [
            'multi_branch' => 'فروع متعددة',
            'barcode_scanner' => 'قارئ الباركود',
            'receipt_printer' => 'طابعة الإيصالات',
            'sms_notifications' => 'إشعارات SMS',
            'email_notifications' => 'إشعارات البريد الإلكتروني',
            'custom_reports' => 'تقارير مخصصة',
            'data_export' => 'تصدير البيانات',
            'advanced_analytics' => 'تحليلات متقدمة',
            'inventory_alerts' => 'تنبيهات المخزون',
            'customer_loyalty' => 'برنامج الولاء',
            'discount_coupons' => 'كوبونات الخصم',
            'online_ordering' => 'الطلب عبر الإنترنت',
        ];
    }

    /**
     * التحقق من تفعيل وحدة معينة
     */
    public function hasModule($module)
    {
        return in_array($module, $this->modules ?? []);
    }

    /**
     * التحقق من تفعيل ميزة معينة
     */
    public function hasFeature($feature)
    {
        return in_array($feature, $this->features ?? []);
    }
}
