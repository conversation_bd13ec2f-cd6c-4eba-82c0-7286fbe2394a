<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Setting;
use App\Services\DashboardService;
use App\Services\ReportService;
use Illuminate\Http\JsonResponse;

class ApiController extends Controller
{
    protected $dashboardService;
    protected $reportService;

    public function __construct(DashboardService $dashboardService, ReportService $reportService)
    {
        $this->dashboardService = $dashboardService;
        $this->reportService = $reportService;
    }

    /**
     * إحصائيات لوحة التحكم
     */
    public function dashboardStats(Request $request): JsonResponse
    {
        $period = $request->get('period', 'month');
        $stats = $this->dashboardService->getComprehensiveStats($period);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * قائمة العملاء
     */
    public function customers(Request $request): JsonResponse
    {
        $query = Customer::query();

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('active')) {
            $query->active();
        }

        $customers = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $customers,
        ]);
    }

    /**
     * تفاصيل عميل
     */
    public function customer($id): JsonResponse
    {
        $customer = Customer::with(['salesInvoices', 'payments'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $customer,
        ]);
    }

    /**
     * قائمة الموردين
     */
    public function suppliers(Request $request): JsonResponse
    {
        $query = Supplier::query();

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('active')) {
            $query->active();
        }

        $suppliers = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $suppliers,
        ]);
    }

    /**
     * قائمة المنتجات
     */
    public function products(Request $request): JsonResponse
    {
        $query = Product::with(['category', 'stock']);

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('active')) {
            $query->active();
        }

        $products = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $products,
        ]);
    }

    /**
     * تفاصيل منتج
     */
    public function product($id): JsonResponse
    {
        $product = Product::with(['category', 'stock', 'invoiceItems'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $product,
        ]);
    }

    /**
     * قائمة الفواتير
     */
    public function invoices(Request $request): JsonResponse
    {
        $query = Invoice::with(['customer', 'supplier']);

        if ($request->filled('type')) {
            if ($request->type === 'sales') {
                $query->sales();
            } elseif ($request->type === 'purchases') {
                $query->purchases();
            }
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $invoices = $query->orderBy('created_at', 'desc')
                         ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $invoices,
        ]);
    }

    /**
     * تفاصيل فاتورة
     */
    public function invoice($id): JsonResponse
    {
        $invoice = Invoice::with(['customer', 'supplier', 'items.product', 'payments'])
                         ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $invoice,
        ]);
    }

    /**
     * قائمة المدفوعات
     */
    public function payments(Request $request): JsonResponse
    {
        $query = Payment::with(['customer', 'supplier']);

        if ($request->filled('type')) {
            if ($request->type === 'received') {
                $query->received();
            } elseif ($request->type === 'paid') {
                $query->paid();
            }
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $payments = $query->orderBy('created_at', 'desc')
                         ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $payments,
        ]);
    }

    /**
     * تفاصيل مدفوعة
     */
    public function payment($id): JsonResponse
    {
        $payment = Payment::with(['customer', 'supplier', 'paymentInvoices.invoice'])
                         ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $payment,
        ]);
    }

    /**
     * تقرير الأرباح والخسائر
     */
    public function profitLossReport(Request $request): JsonResponse
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());
        $comparison = $request->get('comparison', false);

        $report = $this->reportService->generateProfitLossReport($startDate, $endDate, $comparison);

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * تقرير الميزانية العمومية
     */
    public function balanceSheetReport(Request $request): JsonResponse
    {
        $asOfDate = $request->get('as_of_date', now());
        $comparison = $request->get('comparison', false);

        $report = $this->reportService->generateBalanceSheetReport($asOfDate, $comparison);

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * تقرير التدفق النقدي
     */
    public function cashFlowReport(Request $request): JsonResponse
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());
        $method = $request->get('method', 'direct');

        $report = $this->reportService->generateCashFlowReport($startDate, $endDate, $method);

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * الإعدادات
     */
    public function settings(Request $request): JsonResponse
    {
        if ($request->filled('group')) {
            $settings = Setting::getGroup($request->group);
        } else {
            $settings = Setting::getAllGrouped();
        }

        return response()->json([
            'success' => true,
            'data' => $settings,
        ]);
    }

    /**
     * تحديث إعداد
     */
    public function updateSetting(Request $request): JsonResponse
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required',
            'group' => 'string',
        ]);

        $setting = Setting::set(
            $request->key,
            $request->value,
            $request->only(['group', 'type', 'description'])
        );

        return response()->json([
            'success' => true,
            'data' => $setting,
            'message' => 'تم تحديث الإعداد بنجاح',
        ]);
    }

    /**
     * البحث العام
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q');
        $type = $request->get('type', 'all'); // all, customers, suppliers, products, invoices

        if (!$query) {
            return response()->json([
                'success' => false,
                'message' => 'يجب إدخال نص للبحث',
            ], 400);
        }

        $results = [];

        if ($type === 'all' || $type === 'customers') {
            $results['customers'] = Customer::search($query)->limit(5)->get();
        }

        if ($type === 'all' || $type === 'suppliers') {
            $results['suppliers'] = Supplier::search($query)->limit(5)->get();
        }

        if ($type === 'all' || $type === 'products') {
            $results['products'] = Product::search($query)->limit(5)->get();
        }

        if ($type === 'all' || $type === 'invoices') {
            $results['invoices'] = Invoice::search($query)->limit(5)->get();
        }

        return response()->json([
            'success' => true,
            'data' => $results,
        ]);
    }

    /**
     * معلومات النظام
     */
    public function systemInfo(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'version' => '1.0.0',
                'tenant' => tenant(),
                'user' => auth()->user(),
                'permissions' => auth()->user()->roles->pluck('permissions')->flatten()->unique(),
                'settings' => Setting::getCached(),
            ],
        ]);
    }
}
