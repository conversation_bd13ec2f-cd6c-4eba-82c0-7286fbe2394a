<?php $__env->startSection('title', 'تفاصيل المشترك'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-building me-2 text-primary"></i>
                تفاصيل المشترك
            </h2>
            <p class="text-muted mb-0"><?php echo e($tenant->company_name); ?></p>
        </div>
        <div>
            <a href="<?php echo e(route('super-admin.tenants')); ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للمشتركين
            </a>
            <a href="<?php echo e(route('dashboard', ['tenant' => $tenant->id])); ?>" target="_blank" class="btn btn-primary">
                <i class="fas fa-sign-in-alt me-2"></i>
                دخول للنظام
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Tenant Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الشركة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم الشركة:</label>
                                <p class="mb-0"><?php echo e($tenant->company_name); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">النطاق الفرعي:</label>
                                <p class="mb-0">
                                    <a href="http://<?php echo e($tenant->subdomain); ?>.ceramicpos.com" target="_blank" class="text-decoration-none">
                                        <?php echo e($tenant->subdomain); ?>.ceramicpos.com
                                        <i class="fas fa-external-link-alt ms-1 small"></i>
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم المالك:</label>
                                <p class="mb-0"><?php echo e($tenant->owner_name); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">البريد الإلكتروني:</label>
                                <p class="mb-0">
                                    <a href="mailto:<?php echo e($tenant->email); ?>" class="text-decoration-none">
                                        <?php echo e($tenant->email); ?>

                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">رقم الهاتف:</label>
                                <p class="mb-0">
                                    <a href="tel:<?php echo e($tenant->phone); ?>" class="text-decoration-none">
                                        <?php echo e($tenant->phone); ?>

                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">المدينة:</label>
                                <p class="mb-0"><?php echo e($tenant->city); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ التسجيل:</label>
                                <p class="mb-0"><?php echo e($tenant->created_at->format('Y/m/d H:i')); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">آخر تحديث:</label>
                                <p class="mb-0"><?php echo e($tenant->updated_at->format('Y/m/d H:i')); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        معلومات الاشتراك
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الخطة:</label>
                                <p class="mb-0">
                                    <span class="badge bg-secondary fs-6"><?php echo e($tenant->getPlanName()); ?></span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="mb-0">
                                    <span class="badge bg-<?php echo e($tenant->getStatusColor()); ?> fs-6"><?php echo e($tenant->getStatusName()); ?></span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">السعر الشهري:</label>
                                <p class="mb-0 text-success fw-bold"><?php echo e(number_format($tenant->monthly_price)); ?> ج.م</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">انتهاء الفترة التجريبية:</label>
                                <p class="mb-0">
                                    <?php if($tenant->trial_ends_at): ?>
                                        <?php echo e($tenant->trial_ends_at->format('Y/m/d')); ?>

                                        <small class="text-muted">(<?php echo e($tenant->trial_ends_at->diffForHumans()); ?>)</small>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">انتهاء الاشتراك:</label>
                                <p class="mb-0">
                                    <?php if($tenant->subscription_ends_at): ?>
                                        <?php echo e($tenant->subscription_ends_at->format('Y/m/d')); ?>

                                        <small class="text-muted">(<?php echo e($tenant->subscription_ends_at->diffForHumans()); ?>)</small>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        المستخدمين (<?php echo e($users->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($users->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>آخر دخول</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo e($user->name); ?></strong>
                                            <?php if($user->is_owner): ?>
                                                <span class="badge bg-warning ms-2">مالك</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($user->email); ?></td>
                                        <td>
                                            <?php if($user->is_owner): ?>
                                                <span class="text-warning">مالك الحساب</span>
                                            <?php else: ?>
                                                <span class="text-muted">مستخدم</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($user->is_active): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">معطل</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($user->created_at->format('Y/m/d')); ?></td>
                                        <td>
                                            <?php if($user->last_login_at): ?>
                                                <?php echo e($user->last_login_at->format('Y/m/d H:i')); ?>

                                            <?php else: ?>
                                                <span class="text-muted">لم يسجل دخول</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا يوجد مستخدمين</h6>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('dashboard', ['tenant' => $tenant->id])); ?>" target="_blank" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            دخول للنظام
                        </a>
                        
                        <button class="btn btn-outline-warning" 
                                onclick="showStatusModal(<?php echo e($tenant->id); ?>, '<?php echo e($tenant->status); ?>')">
                            <i class="fas fa-edit me-2"></i>
                            تغيير الحالة
                        </button>
                        
                        <button class="btn btn-outline-info" onclick="sendEmail('<?php echo e($tenant->email); ?>')">
                            <i class="fas fa-envelope me-2"></i>
                            إرسال بريد إلكتروني
                        </button>
                        
                        <button class="btn btn-outline-success" onclick="extendTrial(<?php echo e($tenant->id); ?>)">
                            <i class="fas fa-calendar-plus me-2"></i>
                            تمديد الفترة التجريبية
                        </button>
                        
                        <hr>
                        
                        <button class="btn btn-outline-danger" 
                                onclick="confirmDelete(<?php echo e($tenant->id); ?>, '<?php echo e($tenant->company_name); ?>')">
                            <i class="fas fa-trash me-2"></i>
                            حذف المشترك
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <h4 class="text-primary"><?php echo e($users->count()); ?></h4>
                            <p class="text-muted mb-0 small">المستخدمين</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo e($tenant->created_at->diffInDays()); ?></h4>
                            <p class="text-muted mb-0 small">يوم منذ التسجيل</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <h4 class="text-info">0</h4>
                            <p class="text-muted mb-0 small">المنتجات</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">0</h4>
                            <p class="text-muted mb-0 small">الفواتير</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Log -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        آخر الأنشطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء الحساب</h6>
                                <p class="text-muted small mb-0"><?php echo e($tenant->created_at->format('Y/m/d H:i')); ?></p>
                            </div>
                        </div>
                        
                        <?php if($tenant->updated_at != $tenant->created_at): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">آخر تحديث</h6>
                                <p class="text-muted small mb-0"><?php echo e($tenant->updated_at->format('Y/m/d H:i')); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php $__currentLoopData = $users->where('created_at', '>', $tenant->created_at); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">انضم مستخدم جديد</h6>
                                <p class="text-muted small mb-0"><?php echo e($user->name); ?> - <?php echo e($user->created_at->format('Y/m/d H:i')); ?></p>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة المشترك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="statusForm" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" id="statusSelect" class="form-select" required>
                            <option value="trial">تجريبي</option>
                            <option value="active">نشط</option>
                            <option value="suspended">معلق</option>
                            <option value="expired">منتهي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تاريخ انتهاء الاشتراك</label>
                        <input type="date" name="subscription_ends_at" class="form-control">
                        <small class="text-muted">اتركه فارغاً للاشتراكات التجريبية</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-content h6 {
    font-size: 0.9rem;
    font-weight: 600;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function showStatusModal(tenantId, currentStatus) {
    document.getElementById('statusForm').action = `/super-admin/tenants/${tenantId}/status`;
    document.getElementById('statusSelect').value = currentStatus;
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function sendEmail(email) {
    window.location.href = `mailto:${email}`;
}

function extendTrial(tenantId) {
    if (confirm('هل تريد تمديد الفترة التجريبية لـ 30 يوم إضافي؟')) {
        // هنا يمكن إضافة AJAX call لتمديد الفترة التجريبية
        alert('تم تمديد الفترة التجريبية بنجاح!');
        location.reload();
    }
}

function confirmDelete(tenantId, companyName) {
    if (confirm(`هل أنت متأكد من حذف المشترك "${companyName}"؟\n\nسيتم حذف جميع البيانات نهائياً ولا يمكن استرجاعها.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/super-admin/tenants/${tenantId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.super-admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Work\ceramic pos\ceramic-pos-system\resources\views/super-admin/tenants/show.blade.php ENDPATH**/ ?>