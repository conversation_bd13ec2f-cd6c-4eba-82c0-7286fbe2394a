<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\SimpleTenant;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class SaasController extends Controller
{
    /**
     * تسجيل عميل جديد في النظام
     */
    public function register(Request $request)
    {
        $request->validate([
            'company_name' => 'required|string|max:255',
            'subdomain' => 'required|string|max:50|unique:tenants,subdomain|alpha_dash',
            'owner_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20',
            'city' => 'required|string|max:100',
            'password' => 'required|string|min:8|confirmed',
            'plan' => 'required|in:basic,premium,enterprise',
            'terms' => 'required|accepted',
        ], [
            'company_name.required' => 'اسم الشركة مطلوب',
            'subdomain.required' => 'النطاق الفرعي مطلوب',
            'subdomain.unique' => 'هذا النطاق الفرعي مستخدم بالفعل',
            'subdomain.alpha_dash' => 'النطاق الفرعي يجب أن يحتوي على أحرف وأرقام فقط',
            'owner_name.required' => 'اسم المدير مطلوب',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.unique' => 'هذا البريد الإلكتروني مستخدم بالفعل',
            'phone.required' => 'رقم الهاتف مطلوب',
            'city.required' => 'المدينة مطلوبة',
            'password.required' => 'كلمة المرور مطلوبة',
            'password.min' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق',
            'terms.accepted' => 'يجب الموافقة على شروط الاستخدام',
        ]);

        try {
            DB::beginTransaction();

            // إنشاء الـ Tenant
            $tenant = SimpleTenant::create([
                'company_name' => $request->company_name,
                'subdomain' => $request->subdomain,
                'owner_name' => $request->owner_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'city' => $request->city,
                'plan' => $request->plan,
                'status' => 'trial', // فترة تجريبية
                'trial_ends_at' => now()->addDays(30),
                'monthly_price' => $this->getPlanPrice($request->plan),
            ]);

            // إنشاء المستخدم الرئيسي
            $user = User::create([
                'name' => $request->owner_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'tenant_id' => $tenant->id,
                'is_owner' => true,
                'is_active' => true,
            ]);

            DB::commit();

            // تسجيل دخول المستخدم
            auth()->login($user);

            return redirect()->route('dashboard', ['tenant' => $tenant->id])
                ->with('success', 'تم إنشاء حسابك بنجاح! مرحباً بك في فترتك التجريبية المجانية لمدة 30 يوماً.');

        } catch (\Exception $e) {
            DB::rollback();

            return back()->withErrors([
                'error' => 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.'
            ])->withInput();
        }
    }

    /**
     * الحصول على سعر الخطة
     */
    private function getPlanPrice($plan)
    {
        $prices = [
            'basic' => 299,
            'premium' => 599,
            'enterprise' => 999,
        ];

        return $prices[$plan] ?? 599;
    }

    /**
     * لوحة تحكم إدارة SaaS
     */
    public function admin()
    {
        // التحقق من صلاحية المدير (يمكن تحسينها لاحقاً)
        if (!auth()->user() || !in_array(auth()->user()->email, ['<EMAIL>', '<EMAIL>'])) {
            abort(403, 'غير مصرح لك بالوصول لهذه الصفحة');
        }

        $tenants = SimpleTenant::orderBy('created_at', 'desc')->get();

        $stats = [
            'total_tenants' => SimpleTenant::count(),
            'active_tenants' => SimpleTenant::where('status', 'active')->count(),
            'trial_tenants' => SimpleTenant::where('status', 'trial')->count(),
            'monthly_revenue' => SimpleTenant::where('status', 'active')->sum('monthly_price'),
        ];

        $expiring_trials = SimpleTenant::where('status', 'trial')
            ->where('trial_ends_at', '<=', now()->addDays(7))
            ->orderBy('trial_ends_at')
            ->get();

        return view('saas.admin', compact('tenants', 'stats', 'expiring_trials'));
    }
}
