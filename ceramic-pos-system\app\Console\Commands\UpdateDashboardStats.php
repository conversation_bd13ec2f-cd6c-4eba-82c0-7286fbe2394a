<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Tenant;
use App\Services\DashboardService;
use Illuminate\Support\Facades\Cache;

class UpdateDashboardStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dashboard:update-stats {--tenant=} {--period=month}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تحديث إحصائيات لوحة التحكم وحفظها في الكاش';

    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        parent::__construct();
        $this->dashboardService = $dashboardService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantId = $this->option('tenant');
        $period = $this->option('period');

        if ($tenantId) {
            $this->updateTenantStats($tenantId, $period);
        } else {
            $this->updateAllTenantsStats($period);
        }

        $this->info('تم تحديث إحصائيات لوحة التحكم بنجاح');
    }

    /**
     * تحديث إحصائيات شركة محددة
     */
    private function updateTenantStats($tenantId, $period)
    {
        $tenant = Tenant::find($tenantId);

        if (!$tenant) {
            $this->error("الشركة غير موجودة: {$tenantId}");
            return;
        }

        $this->info("تحديث إحصائيات الشركة: {$tenant->name}");

        // تعيين السياق للشركة
        tenancy()->initialize($tenant);

        try {
            // الحصول على الإحصائيات الشاملة
            $stats = $this->dashboardService->getComprehensiveStats($period);

            // حفظ الإحصائيات في الكاش
            $cacheKey = "dashboard_stats_{$tenant->id}_{$period}";
            Cache::put($cacheKey, $stats, now()->addHours(1));

            // حفظ إحصائيات سريعة منفصلة
            $quickStats = [
                'total_sales' => $stats['financial']['revenue'] ?? 0,
                'total_purchases' => $stats['financial']['expenses'] ?? 0,
                'net_profit' => $stats['financial']['gross_profit'] ?? 0,
                'cash_flow' => $stats['financial']['net_cash_flow'] ?? 0,
                'low_stock_count' => $stats['inventory']['low_stock_products'] ?? 0,
                'overdue_invoices_count' => $stats['sales']['overdue_invoices'] ?? 0,
            ];

            $quickCacheKey = "dashboard_quick_stats_{$tenant->id}";
            Cache::put($quickCacheKey, $quickStats, now()->addMinutes(30));

            $this->line("✓ تم تحديث إحصائيات الشركة: {$tenant->name}");

        } catch (\Exception $e) {
            $this->error("خطأ في تحديث إحصائيات الشركة {$tenant->name}: " . $e->getMessage());
        }
    }

    /**
     * تحديث إحصائيات جميع الشركات
     */
    private function updateAllTenantsStats($period)
    {
        $tenants = Tenant::all();
        $progressBar = $this->output->createProgressBar($tenants->count());
        $progressBar->start();

        foreach ($tenants as $tenant) {
            $this->updateTenantStats($tenant->id, $period);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->line('');
    }
}
