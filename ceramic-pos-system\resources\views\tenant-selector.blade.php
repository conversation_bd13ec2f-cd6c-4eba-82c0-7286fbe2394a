<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>اختيار الشركة - نظام إدارة السيراميك</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .tenant-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 800px;
        }
        
        .tenant-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .tenant-body {
            padding: 40px 30px;
        }
        
        .tenant-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .tenant-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .tenant-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .btn-create {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            width: 100%;
        }
        
        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="tenant-container">
        <div class="tenant-header">
            <h1>
                <i class="fas fa-building me-2"></i>
                اختر شركتك
            </h1>
            <p>نظام إدارة السيراميك متعدد الشركات</p>
        </div>
        
        <div class="tenant-body">
            <div class="row">
                @forelse($tenants as $tenant)
                <div class="col-md-6 mb-4">
                    <div class="tenant-card text-center" onclick="selectTenant('{{ $tenant->id }}')">
                        <div class="tenant-icon">
                            <i class="fas fa-{{ $loop->even ? 'warehouse' : 'store' }}"></i>
                        </div>
                        <h4>{{ $tenant->company_name }}</h4>
                        <p class="text-muted mb-3">
                            {{ $tenant->city }} - {{ $tenant->getPlanName() }}
                        </p>

                        <!-- حالة الاشتراك -->
                        <div class="mb-3">
                            @if($tenant->status === 'trial')
                                <span class="badge bg-info">
                                    <i class="fas fa-clock me-1"></i>
                                    فترة تجريبية ({{ $tenant->trial_ends_at->diffForHumans() }})
                                </span>
                            @elseif($tenant->status === 'active')
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>
                                    نشط
                                </span>
                            @elseif($tenant->status === 'suspended')
                                <span class="badge bg-warning">
                                    <i class="fas fa-pause me-1"></i>
                                    معلق
                                </span>
                            @endif
                        </div>

                        <div class="row text-center">
                            <div class="col-4">
                                <small class="text-muted">الخطة</small>
                                <div class="fw-bold">{{ $tenant->getPlanName() }}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">السعر</small>
                                <div class="fw-bold">{{ $tenant->monthly_price }} ج.م</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">المدينة</small>
                                <div class="fw-bold">{{ $tenant->city }}</div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                {{ $tenant->owner_name }}
                            </small>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <h5>لا توجد شركات متاحة</h5>
                        <p class="mb-0">يرجى التواصل مع الإدارة لإنشاء شركة جديدة.</p>
                    </div>
                </div>
                @endforelse
            </div>
            
            <!-- إنشاء شركة جديدة -->
            <div class="text-center mt-4">
                <a href="#" class="btn-create" onclick="createNewTenant()">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء شركة جديدة
                </a>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="alert alert-info mt-4">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>
                    نظام متعدد الشركات
                </h6>
                <p class="mb-0">
                    يمكنك إدارة عدة شركات من نفس النظام. كل شركة لها بياناتها المستقلة والمنفصلة.
                </p>
            </div>
            
            <div class="text-center mt-3">
                <a href="{{ route('home') }}" class="text-muted">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function selectTenant(tenantId) {
            // حفظ الشركة المختارة في session storage
            sessionStorage.setItem('selected_tenant', tenantId);
            
            // توجيه للوحة التحكم
            window.location.href = '/dashboard?tenant=' + tenantId;
        }
        
        function createNewTenant() {
            alert('سيتم إضافة هذه الميزة قريباً!');
        }
    </script>
</body>
</html>
