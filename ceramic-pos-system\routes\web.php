<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Models\Tenant;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\POSController;
use App\Http\Controllers\AccountingController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\Tenant\ProductController as TenantProductController;
use App\Http\Controllers\Tenant\CustomerController as TenantCustomerController;
use App\Http\Controllers\Tenant\InvoiceController as TenantInvoiceController;
use App\Http\Controllers\Tenant\PaymentController as TenantPaymentController;
use App\Http\Controllers\Tenant\ReportController as TenantReportController;
use App\Http\Controllers\SuperAdminController;
use App\Http\Controllers\LandingPageController;

/*
|--------------------------------------------------------------------------
| Central Routes (النطاق المركزي)
|--------------------------------------------------------------------------
|
| هذه الصفحات متاحة فقط من النطاق المركزي (localhost, ceramicpos.com)
| وتشمل: التسجيل، لوحة تحكم المشرف العام، صفحات التسويق
|
*/

// الصفحة الرئيسية للنظام (SaaS Landing Page)
Route::get('/', [LandingPageController::class, 'index'])->name('home');

// صفحات SaaS
Route::get('/saas/register', function () {
    return view('saas.register');
})->name('saas.register');

Route::post('/saas/register', [\App\Http\Controllers\SaasController::class, 'register'])->name('saas.register.store');

// إدارة SaaS (للمدير فقط)
Route::get('/saas/admin', [\App\Http\Controllers\SaasController::class, 'admin'])->name('saas.admin');

// صفحة حالة الوحدات
Route::get('/modules-status', function () {
    return view('modules-status');
})->name('modules.status');

// routes المصادقة
Route::get('/login', [\App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [\App\Http\Controllers\Auth\LoginController::class, 'login']);
Route::post('/logout', [\App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');

Route::get('/register', [\App\Http\Controllers\Auth\RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [\App\Http\Controllers\Auth\RegisterController::class, 'register']);

// اختيار الشركة (Multi-Tenant)
Route::get('/select-tenant', function () {
    $tenants = \App\Models\SimpleTenant::where('status', '!=', 'cancelled')->get();
    return view('tenant-selector', compact('tenants'));
})->middleware('auth')->name('tenant.select');

// لوحة التحكم (تحتاج تسجيل دخول)
Route::get('/dashboard', function () {
    $tenantId = request('tenant');

    if (!$tenantId) {
        return redirect()->route('tenant.select');
    }

    $tenant = \App\Models\SimpleTenant::find($tenantId);

    if (!$tenant) {
        return redirect()->route('tenant.select')->with('error', 'الشركة المطلوبة غير موجودة.');
    }

    if (!$tenant->isActive()) {
        return redirect()->route('tenant.select')->with('error', 'هذه الشركة غير نشطة أو انتهت فترتها التجريبية.');
    }

    try {
        return view('dashboard', [
            'tenant' => $tenant,
            'tenant_id' => $tenant->id,
            'tenant_name' => $tenant->company_name
        ]);
    } catch (\Exception $e) {
        // في حالة وجود خطأ، اعرض صفحة بسيطة
        return response()->view('simple-dashboard', [
            'user' => auth()->user(),
            'error' => $e->getMessage(),
            'tenant' => $tenant,
            'tenant_id' => $tenant->id
        ]);
    }
})->middleware('auth')->name('dashboard');

// وحدات النظام (تحتاج تسجيل دخول)
Route::middleware('auth')->group(function () {

    // نقاط البيع
    Route::get('/pos', function(Request $request) {
        $tenantId = $request->get('tenant');
        $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
        return view('pos.index', compact('tenant'));
    })->name('pos.index');
    Route::get('/pos/search-products', [POSController::class, 'searchProducts'])->name('pos.search-products');
    Route::post('/pos/checkout', [POSController::class, 'checkout'])->name('pos.checkout');

    // إدارة المنتجات
    Route::prefix('inventory')->group(function () {
        Route::get('/products', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            $products = \App\Models\Product::paginate(20);
            return view('inventory.products.index', compact('tenant', 'products'));
        })->name('inventory.products.index');
        Route::get('/products/create', [TenantProductController::class, 'create'])->name('inventory.products.create');
        Route::post('/products', [TenantProductController::class, 'store'])->name('inventory.products.store');
        Route::get('/products/{product}', [TenantProductController::class, 'show'])->name('inventory.products.show');
        Route::get('/products/{product}/edit', [TenantProductController::class, 'edit'])->name('inventory.products.edit');
        Route::put('/products/{product}', [TenantProductController::class, 'update'])->name('inventory.products.update');
        Route::delete('/products/{product}', [TenantProductController::class, 'destroy'])->name('inventory.products.destroy');
        Route::get('/products/search', [TenantProductController::class, 'search'])->name('inventory.products.search');
    });

    // إدارة العملاء
    Route::get('/customers', function(Request $request) {
        $tenantId = $request->get('tenant');
        $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
        $customers = \App\Models\Customer::paginate(20);
        return view('customers.index', compact('tenant', 'customers'));
    })->name('customers.index');
    Route::get('/customers/create', [TenantCustomerController::class, 'create'])->name('customers.create');
    Route::post('/customers', [TenantCustomerController::class, 'store'])->name('customers.store');
    Route::get('/customers/{customer}', [TenantCustomerController::class, 'show'])->name('customers.show');
    Route::get('/customers/{customer}/edit', [TenantCustomerController::class, 'edit'])->name('customers.edit');
    Route::put('/customers/{customer}', [TenantCustomerController::class, 'update'])->name('customers.update');
    Route::delete('/customers/{customer}', [TenantCustomerController::class, 'destroy'])->name('customers.destroy');

    // الفواتير
    Route::get('/invoices', function(Request $request) {
        $tenantId = $request->get('tenant');
        $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
        $invoices = \App\Models\Invoice::latest()->paginate(20);
        return view('invoices.index', compact('tenant', 'invoices'));
    })->name('invoices.index');
    Route::get('/invoices/create', [TenantInvoiceController::class, 'create'])->name('invoices.create');
    Route::post('/invoices', [TenantInvoiceController::class, 'store'])->name('invoices.store');
    Route::get('/invoices/{invoice}', [TenantInvoiceController::class, 'show'])->name('invoices.show');
    Route::get('/invoices/{invoice}/print', [TenantInvoiceController::class, 'print'])->name('invoices.print');

    // المحاسبة
    Route::prefix('accounting')->group(function () {
        Route::get('/', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('accounting.index', compact('tenant'));
        })->name('accounting.index');
        Route::get('/chart-of-accounts', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('accounting.chart-of-accounts', compact('tenant'));
        })->name('accounting.chart-of-accounts');
        Route::get('/profit-loss', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('accounting.profit-loss', compact('tenant'));
        })->name('accounting.profit-loss');
        Route::get('/balance-sheet', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('accounting.balance-sheet', compact('tenant'));
        })->name('accounting.balance-sheet');
        Route::get('/cash-flow', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('accounting.cash-flow', compact('tenant'));
        })->name('accounting.cash-flow');
    });

    // التقارير
    Route::prefix('reports')->group(function () {
        Route::get('/', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('reports.index', compact('tenant'));
        })->name('reports.index');
        Route::get('/sales', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            $dateFrom = now()->startOfMonth();
            $dateTo = now()->endOfMonth();
            $salesStats = ['total_invoices' => 25, 'total_amount' => 45000, 'average_invoice' => 1800, 'paid_amount' => 42000];
            $dailySales = collect([]);
            $topCustomers = collect([]);
            return view('reports.sales', compact('tenant', 'dateFrom', 'dateTo', 'salesStats', 'dailySales', 'topCustomers'));
        })->name('reports.sales');
        Route::get('/inventory', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('reports.inventory', compact('tenant'));
        })->name('reports.inventory');
        Route::get('/customers', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('reports.customers', compact('tenant'));
        })->name('reports.customers');
        Route::get('/payments', function(Request $request) {
            $tenantId = $request->get('tenant');
            $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
            return view('reports.payments', compact('tenant'));
        })->name('reports.payments');
    });

    // المدفوعات
    Route::get('/payments', function(Request $request) {
        $tenantId = $request->get('tenant');
        $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
        return view('payments.index', compact('tenant'));
    })->name('payments.index');

    // الإعدادات
    Route::get('/settings', function () {
        $tenantId = request('tenant', 'demo-company');
        return view('modules.settings', ['tenant_id' => $tenantId]);
    })->name('settings');
});

// Super Admin Routes (صاحب النظام)
Route::middleware(['super.admin'])->prefix('super-admin')->name('super-admin.')->group(function () {
    Route::get('/dashboard', [SuperAdminController::class, 'dashboard'])->name('dashboard');

    // إدارة المشتركين
    Route::get('/tenants', [SuperAdminController::class, 'tenants'])->name('tenants');
    Route::get('/tenants/create', [SuperAdminController::class, 'createTenant'])->name('tenants.create');
    Route::post('/tenants', [SuperAdminController::class, 'storeTenant'])->name('tenants.store');
    Route::get('/tenants/{tenant}', [SuperAdminController::class, 'showTenant'])->name('tenants.show');
    Route::put('/tenants/{tenant}/status', [SuperAdminController::class, 'updateTenantStatus'])->name('tenants.update-status');
    Route::delete('/tenants/{tenant}', [SuperAdminController::class, 'deleteTenant'])->name('tenants.delete');

    // إدارة الخطط والأسعار
    Route::get('/plans', [SuperAdminController::class, 'plans'])->name('plans');
    Route::put('/plans/{plan}', [SuperAdminController::class, 'updatePlan'])->name('plans.update');

    // إدارة محتوى الصفحة الرئيسية
    Route::get('/landing-page', [LandingPageController::class, 'manage'])->name('landing-page');
    Route::post('/landing-page', [LandingPageController::class, 'update'])->name('landing-page.update');

    // الإعدادات العامة
    Route::get('/settings', [SuperAdminController::class, 'settings'])->name('settings');
    Route::post('/settings', [SuperAdminController::class, 'updateSettings'])->name('settings.update');

    // التقارير المالية
    Route::get('/financial-reports', [SuperAdminController::class, 'financialReports'])->name('financial-reports');
});

// صفحة اختبار
Route::get('/test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'النظام يعمل بشكل طبيعي',
        'user' => auth()->check() ? auth()->user()->name : 'غير مسجل دخول',
        'time' => now()->format('Y-m-d H:i:s')
    ]);
})->name('test');

// صفحة معلومات النظام
Route::get('/about', function () {
    return view('central.about');
})->name('central.about');

// صفحة الأسعار وخطط الاشتراك
Route::get('/pricing', function () {
    return view('central.pricing');
})->name('central.pricing');

// تسجيل شركة جديدة
Route::get('/register-company', function () {
    return view('central.register-company');
})->name('central.register-company');

Route::post('/register-company', function () {
    // سيتم تطوير هذا لاحقاً
    return redirect()->back()->with('success', 'تم تسجيل الشركة بنجاح!');
})->name('central.register-company.store');

// لوحة تحكم المشرف العام
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {

    Route::get('/dashboard', function () {
        $tenantsCount = Tenant::count();
        $activeTenantsCount = Tenant::where('status', 'active')->count();
        return view('admin.dashboard', compact('tenantsCount', 'activeTenantsCount'));
    })->name('dashboard');

    // إدارة الشركات
    Route::prefix('tenants')->name('tenants.')->group(function () {
        Route::get('/', function () {
            $tenants = Tenant::paginate(20);
            return view('admin.tenants.index', compact('tenants'));
        })->name('index');

        Route::get('/{tenant}', function (Tenant $tenant) {
            return view('admin.tenants.show', compact('tenant'));
        })->name('show');
    });
});

// تم نقل routes المصادقة إلى أعلى الملف

// require __DIR__.'/auth.php'; // تم تعطيله لتجنب التضارب
