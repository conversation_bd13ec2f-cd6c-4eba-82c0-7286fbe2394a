<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Models\Tenant;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\POSController;
use App\Http\Controllers\AccountingController;
use App\Http\Controllers\ReportsController;

/*
|--------------------------------------------------------------------------
| Central Routes (النطاق المركزي)
|--------------------------------------------------------------------------
|
| هذه الصفحات متاحة فقط من النطاق المركزي (localhost, ceramicpos.com)
| وتشمل: التسجيل، لوحة تحكم المشرف العام، صفحات التسويق
|
*/

// الصفحة الرئيسية للنظام (SaaS Landing Page)
Route::get('/', function () {
    return view('saas.landing');
})->name('home');

// صفحات SaaS
Route::get('/saas/register', function () {
    return view('saas.register');
})->name('saas.register');

Route::post('/saas/register', [\App\Http\Controllers\SaasController::class, 'register'])->name('saas.register.store');

// إدارة SaaS (للمدير فقط)
Route::get('/saas/admin', [\App\Http\Controllers\SaasController::class, 'admin'])->name('saas.admin');

// صفحة حالة الوحدات
Route::get('/modules-status', function () {
    return view('modules-status');
})->name('modules.status');

// routes المصادقة
Route::get('/login', [\App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [\App\Http\Controllers\Auth\LoginController::class, 'login']);
Route::post('/logout', [\App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');

Route::get('/register', [\App\Http\Controllers\Auth\RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [\App\Http\Controllers\Auth\RegisterController::class, 'register']);

// اختيار الشركة (Multi-Tenant)
Route::get('/select-tenant', function () {
    $tenants = \App\Models\SimpleTenant::where('status', '!=', 'cancelled')->get();
    return view('tenant-selector', compact('tenants'));
})->middleware('auth')->name('tenant.select');

// لوحة التحكم (تحتاج تسجيل دخول)
Route::get('/dashboard', function () {
    $tenantId = request('tenant');

    if (!$tenantId) {
        return redirect()->route('tenant.select');
    }

    $tenant = \App\Models\SimpleTenant::find($tenantId);

    if (!$tenant) {
        return redirect()->route('tenant.select')->with('error', 'الشركة المطلوبة غير موجودة.');
    }

    if (!$tenant->isActive()) {
        return redirect()->route('tenant.select')->with('error', 'هذه الشركة غير نشطة أو انتهت فترتها التجريبية.');
    }

    try {
        return view('dashboard', [
            'tenant' => $tenant,
            'tenant_id' => $tenant->id,
            'tenant_name' => $tenant->company_name
        ]);
    } catch (\Exception $e) {
        // في حالة وجود خطأ، اعرض صفحة بسيطة
        return response()->view('simple-dashboard', [
            'user' => auth()->user(),
            'error' => $e->getMessage(),
            'tenant' => $tenant,
            'tenant_id' => $tenant->id
        ]);
    }
})->middleware('auth')->name('dashboard');

// وحدات النظام (تحتاج تسجيل دخول)
Route::middleware('auth')->group(function () {

    // نقاط البيع
    Route::get('/pos', [POSController::class, 'index'])->name('pos.index');
    Route::get('/pos/search-products', [POSController::class, 'searchProducts'])->name('pos.search-products');
    Route::post('/pos/checkout', [POSController::class, 'checkout'])->name('pos.checkout');

    // إدارة المنتجات
    Route::prefix('inventory')->group(function () {
        Route::get('/products', [ProductController::class, 'index'])->name('inventory.products.index');
        Route::get('/products/create', [ProductController::class, 'create'])->name('inventory.products.create');
        Route::post('/products', [ProductController::class, 'store'])->name('inventory.products.store');
        Route::get('/products/{product}', [ProductController::class, 'show'])->name('inventory.products.show');
        Route::get('/products/{product}/edit', [ProductController::class, 'edit'])->name('inventory.products.edit');
        Route::put('/products/{product}', [ProductController::class, 'update'])->name('inventory.products.update');
        Route::delete('/products/{product}', [ProductController::class, 'destroy'])->name('inventory.products.destroy');
        Route::get('/products/search', [ProductController::class, 'search'])->name('inventory.products.search');
    });

    // إدارة العملاء
    Route::get('/customers', [CustomerController::class, 'index'])->name('customers.index');
    Route::get('/customers/create', [CustomerController::class, 'create'])->name('customers.create');
    Route::post('/customers', [CustomerController::class, 'store'])->name('customers.store');
    Route::get('/customers/{customer}', [CustomerController::class, 'show'])->name('customers.show');
    Route::get('/customers/{customer}/edit', [CustomerController::class, 'edit'])->name('customers.edit');
    Route::put('/customers/{customer}', [CustomerController::class, 'update'])->name('customers.update');
    Route::delete('/customers/{customer}', [CustomerController::class, 'destroy'])->name('customers.destroy');

    // الفواتير
    Route::get('/invoices', [InvoiceController::class, 'index'])->name('invoices.index');
    Route::get('/invoices/create', [InvoiceController::class, 'create'])->name('invoices.create');
    Route::post('/invoices', [InvoiceController::class, 'store'])->name('invoices.store');
    Route::get('/invoices/{invoice}', [InvoiceController::class, 'show'])->name('invoices.show');
    Route::get('/invoices/{invoice}/print', [InvoiceController::class, 'print'])->name('invoices.print');

    // المحاسبة
    Route::prefix('accounting')->group(function () {
        Route::get('/', [AccountingController::class, 'index'])->name('accounting.index');
        Route::get('/chart-of-accounts', [AccountingController::class, 'chartOfAccounts'])->name('accounting.chart-of-accounts');
        Route::get('/profit-loss', [AccountingController::class, 'profitLoss'])->name('accounting.profit-loss');
        Route::get('/balance-sheet', [AccountingController::class, 'balanceSheet'])->name('accounting.balance-sheet');
        Route::get('/cash-flow', [AccountingController::class, 'cashFlow'])->name('accounting.cash-flow');
    });

    // التقارير
    Route::prefix('reports')->group(function () {
        Route::get('/', [ReportsController::class, 'index'])->name('reports.index');
        Route::get('/sales', [ReportsController::class, 'sales'])->name('reports.sales');
        Route::get('/inventory', [ReportsController::class, 'inventory'])->name('reports.inventory');
        Route::get('/customers', [ReportsController::class, 'customers'])->name('reports.customers');
        Route::get('/payments', [ReportsController::class, 'payments'])->name('reports.payments');
    });

    // المدفوعات
    Route::get('/payments', function () {
        $tenantId = request('tenant');
        $tenant = \App\Models\SimpleTenant::findOrFail($tenantId);
        return view('payments.index', compact('tenant'));
    })->name('payments.index');

    // الإعدادات
    Route::get('/settings', function () {
        $tenantId = request('tenant', 'demo-company');
        return view('modules.settings', ['tenant_id' => $tenantId]);
    })->name('settings');
});

// صفحة اختبار
Route::get('/test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'النظام يعمل بشكل طبيعي',
        'user' => auth()->check() ? auth()->user()->name : 'غير مسجل دخول',
        'time' => now()->format('Y-m-d H:i:s')
    ]);
})->name('test');

// صفحة معلومات النظام
Route::get('/about', function () {
    return view('central.about');
})->name('central.about');

// صفحة الأسعار وخطط الاشتراك
Route::get('/pricing', function () {
    return view('central.pricing');
})->name('central.pricing');

// تسجيل شركة جديدة
Route::get('/register-company', function () {
    return view('central.register-company');
})->name('central.register-company');

Route::post('/register-company', function () {
    // سيتم تطوير هذا لاحقاً
    return redirect()->back()->with('success', 'تم تسجيل الشركة بنجاح!');
})->name('central.register-company.store');

// لوحة تحكم المشرف العام
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {

    Route::get('/dashboard', function () {
        $tenantsCount = Tenant::count();
        $activeTenantsCount = Tenant::where('status', 'active')->count();
        return view('admin.dashboard', compact('tenantsCount', 'activeTenantsCount'));
    })->name('dashboard');

    // إدارة الشركات
    Route::prefix('tenants')->name('tenants.')->group(function () {
        Route::get('/', function () {
            $tenants = Tenant::paginate(20);
            return view('admin.tenants.index', compact('tenants'));
        })->name('index');

        Route::get('/{tenant}', function (Tenant $tenant) {
            return view('admin.tenants.show', compact('tenant'));
        })->name('show');
    });
});

// تم نقل routes المصادقة إلى أعلى الملف

// require __DIR__.'/auth.php'; // تم تعطيله لتجنب التضارب
