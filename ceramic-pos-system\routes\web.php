<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Models\Tenant;

/*
|--------------------------------------------------------------------------
| Central Routes (النطاق المركزي)
|--------------------------------------------------------------------------
|
| هذه الصفحات متاحة فقط من النطاق المركزي (localhost, ceramicpos.com)
| وتشمل: التسجيل، لوحة تحكم المشرف العام، صفحات التسويق
|
*/

// الصفحة الرئيسية للنظام (بدون tenancy)
Route::get('/', function () {
    return view('simple-welcome');
})->name('home');

// routes المصادقة
Route::get('/login', [\App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [\App\Http\Controllers\Auth\LoginController::class, 'login']);
Route::post('/logout', [\App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');

Route::get('/register', [\App\Http\Controllers\Auth\RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [\App\Http\Controllers\Auth\RegisterController::class, 'register']);

// لوحة التحكم (تحتاج تسجيل دخول)
Route::get('/dashboard', function () {
    try {
        return view('dashboard');
    } catch (\Exception $e) {
        // في حالة وجود خطأ، اعرض صفحة بسيطة
        return response()->view('simple-dashboard', [
            'user' => auth()->user(),
            'error' => $e->getMessage()
        ]);
    }
})->middleware('auth')->name('dashboard');

// صفحة اختبار
Route::get('/test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'النظام يعمل بشكل طبيعي',
        'user' => auth()->check() ? auth()->user()->name : 'غير مسجل دخول',
        'time' => now()->format('Y-m-d H:i:s')
    ]);
})->name('test');

// صفحة معلومات النظام
Route::get('/about', function () {
    return view('central.about');
})->name('central.about');

// صفحة الأسعار وخطط الاشتراك
Route::get('/pricing', function () {
    return view('central.pricing');
})->name('central.pricing');

// تسجيل شركة جديدة
Route::get('/register-company', function () {
    return view('central.register-company');
})->name('central.register-company');

Route::post('/register-company', function () {
    // سيتم تطوير هذا لاحقاً
    return redirect()->back()->with('success', 'تم تسجيل الشركة بنجاح!');
})->name('central.register-company.store');

// لوحة تحكم المشرف العام
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {

    Route::get('/dashboard', function () {
        $tenantsCount = Tenant::count();
        $activeTenantsCount = Tenant::where('status', 'active')->count();
        return view('admin.dashboard', compact('tenantsCount', 'activeTenantsCount'));
    })->name('dashboard');

    // إدارة الشركات
    Route::prefix('tenants')->name('tenants.')->group(function () {
        Route::get('/', function () {
            $tenants = Tenant::paginate(20);
            return view('admin.tenants.index', compact('tenants'));
        })->name('index');

        Route::get('/{tenant}', function (Tenant $tenant) {
            return view('admin.tenants.show', compact('tenant'));
        })->name('show');
    });
});

// تم نقل routes المصادقة إلى أعلى الملف

// require __DIR__.'/auth.php'; // تم تعطيله لتجنب التضارب
