<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Models\Tenant;

/*
|--------------------------------------------------------------------------
| Central Routes (النطاق المركزي)
|--------------------------------------------------------------------------
|
| هذه الصفحات متاحة فقط من النطاق المركزي (localhost, ceramicpos.com)
| وتشمل: التسجيل، لوحة تحكم المشرف العام، صفحات التسويق
|
*/

// الصفحة الرئيسية للنظام
Route::get('/', function () {
    return view('central.welcome');
})->name('central.home');

// صفحة معلومات النظام
Route::get('/about', function () {
    return view('central.about');
})->name('central.about');

// صفحة الأسعار وخطط الاشتراك
Route::get('/pricing', function () {
    return view('central.pricing');
})->name('central.pricing');

// تسجيل شركة جديدة
Route::get('/register-company', function () {
    return view('central.register-company');
})->name('central.register-company');

Route::post('/register-company', function () {
    // سيتم تطوير هذا لاحقاً
    return redirect()->back()->with('success', 'تم تسجيل الشركة بنجاح!');
})->name('central.register-company.store');

// لوحة تحكم المشرف العام
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {

    Route::get('/dashboard', function () {
        $tenantsCount = Tenant::count();
        $activeTenantsCount = Tenant::where('status', 'active')->count();
        return view('admin.dashboard', compact('tenantsCount', 'activeTenantsCount'));
    })->name('dashboard');

    // إدارة الشركات
    Route::prefix('tenants')->name('tenants.')->group(function () {
        Route::get('/', function () {
            $tenants = Tenant::paginate(20);
            return view('admin.tenants.index', compact('tenants'));
        })->name('index');

        Route::get('/{tenant}', function (Tenant $tenant) {
            return view('admin.tenants.show', compact('tenant'));
        })->name('show');
    });
});

// الصفحات الافتراضية من Laravel Breeze
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
