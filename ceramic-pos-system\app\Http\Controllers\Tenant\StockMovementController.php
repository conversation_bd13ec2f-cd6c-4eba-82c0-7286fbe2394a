<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\StockMovement;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\ProductStock;
use Illuminate\Support\Facades\DB;

class StockMovementController extends Controller
{
    /**
     * عرض قائمة حركات المخزون
     */
    public function index(Request $request)
    {
        $query = StockMovement::with(['product', 'warehouse', 'createdBy']);

        // التصفية حسب المنتج
        if ($request->filled('product_id')) {
            $query->forProduct($request->product_id);
        }

        // التصفية حسب المخزن
        if ($request->filled('warehouse_id')) {
            $query->forWarehouse($request->warehouse_id);
        }

        // التصفية حسب نوع الحركة
        if ($request->filled('movement_type')) {
            $query->where('movement_type', $request->movement_type);
        }

        // التصفية حسب الفترة الزمنية
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        // الترتيب
        $query->orderBy('created_at', 'desc');

        $movements = $query->paginate(20);

        // البيانات المساعدة
        $products = Product::active()->orderBy('name')->get(['id', 'name', 'product_code']);
        $warehouses = Warehouse::active()->orderBy('name')->get(['id', 'name', 'warehouse_code']);

        // إحصائيات
        $stats = [
            'total_movements' => StockMovement::count(),
            'inbound_movements' => StockMovement::inbound()->count(),
            'outbound_movements' => StockMovement::outbound()->count(),
            'transfer_movements' => StockMovement::transfers()->count(),
            'adjustment_movements' => StockMovement::adjustments()->count(),
        ];

        return view('tenant.stock-movements.index', compact('movements', 'products', 'warehouses', 'stats'));
    }

    /**
     * عرض نموذج إنشاء حركة مخزون جديدة
     */
    public function create()
    {
        $products = Product::active()->orderBy('name')->get(['id', 'name', 'product_code']);
        $warehouses = Warehouse::active()->orderBy('name')->get(['id', 'name', 'warehouse_code']);

        return view('tenant.stock-movements.create', compact('products', 'warehouses'));
    }

    /**
     * حفظ حركة مخزون جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'movement_type' => 'required|in:' . implode(',', array_keys(StockMovement::MOVEMENT_TYPES)),
            'quantity' => 'required|integer|min:1',
            'unit_cost' => 'nullable|numeric|min:0',
            'reference_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string',
        ], [
            'product_id.required' => 'المنتج مطلوب',
            'warehouse_id.required' => 'المخزن مطلوب',
            'movement_type.required' => 'نوع الحركة مطلوب',
            'quantity.required' => 'الكمية مطلوبة',
            'quantity.min' => 'الكمية يجب أن تكون أكبر من صفر',
        ]);

        DB::beginTransaction();

        try {
            $product = Product::findOrFail($request->product_id);
            $warehouse = Warehouse::findOrFail($request->warehouse_id);

            // الحصول على المخزون الحالي
            $currentStock = ProductStock::where('product_id', $request->product_id)
                                       ->where('warehouse_id', $request->warehouse_id)
                                       ->first();

            $quantityBefore = $currentStock ? $currentStock->quantity : 0;
            $unitCost = $request->unit_cost ?? $product->purchase_price;

            // التحقق من الكمية المتاحة للحركات الصادرة
            if (in_array($request->movement_type, ['out', 'damage', 'expired'])) {
                if ($quantityBefore < $request->quantity) {
                    return redirect()->back()
                        ->withInput()
                        ->with('error', 'الكمية المطلوبة غير متوفرة في المخزن');
                }
            }

            // تحديد الكمية (موجبة للداخل، سالبة للخارج)
            $movementQuantity = $request->quantity;
            if (in_array($request->movement_type, ['out', 'damage', 'expired'])) {
                $movementQuantity = -$request->quantity;
            }

            $quantityAfter = $quantityBefore + $movementQuantity;

            // إنشاء حركة المخزون
            $movement = StockMovement::create([
                'product_id' => $request->product_id,
                'warehouse_id' => $request->warehouse_id,
                'movement_type' => $request->movement_type,
                'quantity' => $movementQuantity,
                'quantity_before' => $quantityBefore,
                'quantity_after' => $quantityAfter,
                'unit_cost' => $unitCost,
                'total_cost' => abs($movementQuantity) * $unitCost,
                'reference_number' => $request->reference_number,
                'notes' => $request->notes,
                'created_by' => auth()->id(),
            ]);

            // تحديث المخزون
            if ($request->movement_type === 'in') {
                $warehouse->updateProductStock($request->product_id, $request->quantity, 'add', $unitCost);
            } elseif (in_array($request->movement_type, ['out', 'damage', 'expired'])) {
                $warehouse->updateProductStock($request->product_id, $request->quantity, 'subtract');
            } elseif ($request->movement_type === 'adjustment') {
                // للتسوية، نحدد الكمية الجديدة مباشرة
                if ($currentStock) {
                    $currentStock->quantity = $quantityAfter;
                    $currentStock->available_quantity = $quantityAfter - $currentStock->reserved_quantity;
                    $currentStock->save();
                }
            }

            DB::commit();

            return redirect()->route('tenant.stock-movements.index')
                ->with('success', 'تم إنشاء حركة المخزون بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء حركة المخزون: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل حركة المخزون
     */
    public function show(StockMovement $stockMovement)
    {
        $stockMovement->load(['product', 'warehouse', 'fromWarehouse', 'toWarehouse', 'createdBy']);

        return view('tenant.stock-movements.show', compact('stockMovement'));
    }

    /**
     * تقرير حركات المخزون
     */
    public function report(Request $request)
    {
        $query = StockMovement::with(['product', 'warehouse', 'createdBy']);

        // التصفية حسب المنتج
        if ($request->filled('product_id')) {
            $query->forProduct($request->product_id);
        }

        // التصفية حسب المخزن
        if ($request->filled('warehouse_id')) {
            $query->forWarehouse($request->warehouse_id);
        }

        // التصفية حسب نوع الحركة
        if ($request->filled('movement_type')) {
            $query->where('movement_type', $request->movement_type);
        }

        // التصفية حسب الفترة الزمنية
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        $movements = $query->orderBy('created_at', 'desc')->get();

        // البيانات المساعدة
        $products = Product::active()->orderBy('name')->get(['id', 'name', 'product_code']);
        $warehouses = Warehouse::active()->orderBy('name')->get(['id', 'name', 'warehouse_code']);

        return view('tenant.stock-movements.report', compact('movements', 'products', 'warehouses'));
    }

    /**
     * تقرير حركة منتج محدد
     */
    public function productMovements(Product $product, Request $request)
    {
        $query = $product->stockMovements()->with(['warehouse', 'createdBy']);

        // التصفية حسب المخزن
        if ($request->filled('warehouse_id')) {
            $query->forWarehouse($request->warehouse_id);
        }

        // التصفية حسب نوع الحركة
        if ($request->filled('movement_type')) {
            $query->where('movement_type', $request->movement_type);
        }

        // التصفية حسب الفترة الزمنية
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        $movements = $query->orderBy('created_at', 'desc')->paginate(20);

        $warehouses = Warehouse::active()->orderBy('name')->get(['id', 'name', 'warehouse_code']);

        return view('tenant.stock-movements.product-movements', compact('product', 'movements', 'warehouses'));
    }

    /**
     * تقرير حركة مخزن محدد
     */
    public function warehouseMovements(Warehouse $warehouse, Request $request)
    {
        $query = $warehouse->stockMovements()->with(['product', 'createdBy']);

        // التصفية حسب المنتج
        if ($request->filled('product_id')) {
            $query->forProduct($request->product_id);
        }

        // التصفية حسب نوع الحركة
        if ($request->filled('movement_type')) {
            $query->where('movement_type', $request->movement_type);
        }

        // التصفية حسب الفترة الزمنية
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        $movements = $query->orderBy('created_at', 'desc')->paginate(20);

        $products = Product::active()->orderBy('name')->get(['id', 'name', 'product_code']);

        return view('tenant.stock-movements.warehouse-movements', compact('warehouse', 'movements', 'products'));
    }

    /**
     * تسوية المخزون
     */
    public function adjustment(Request $request)
    {
        $request->validate([
            'adjustments' => 'required|array|min:1',
            'adjustments.*.product_id' => 'required|exists:products,id',
            'adjustments.*.warehouse_id' => 'required|exists:warehouses,id',
            'adjustments.*.current_quantity' => 'required|integer|min:0',
            'adjustments.*.actual_quantity' => 'required|integer|min:0',
            'notes' => 'nullable|string',
        ], [
            'adjustments.required' => 'يجب إضافة تسوية واحدة على الأقل',
            'adjustments.*.product_id.required' => 'المنتج مطلوب',
            'adjustments.*.warehouse_id.required' => 'المخزن مطلوب',
            'adjustments.*.current_quantity.required' => 'الكمية الحالية مطلوبة',
            'adjustments.*.actual_quantity.required' => 'الكمية الفعلية مطلوبة',
        ]);

        DB::beginTransaction();

        try {
            foreach ($request->adjustments as $adjustment) {
                $currentQuantity = (int) $adjustment['current_quantity'];
                $actualQuantity = (int) $adjustment['actual_quantity'];

                // تخطي إذا لم يكن هناك تغيير
                if ($currentQuantity === $actualQuantity) {
                    continue;
                }

                $difference = $actualQuantity - $currentQuantity;

                // إنشاء حركة التسوية
                StockMovement::create([
                    'product_id' => $adjustment['product_id'],
                    'warehouse_id' => $adjustment['warehouse_id'],
                    'movement_type' => 'adjustment',
                    'quantity' => $difference,
                    'quantity_before' => $currentQuantity,
                    'quantity_after' => $actualQuantity,
                    'unit_cost' => 0,
                    'total_cost' => 0,
                    'reference_number' => 'ADJ-' . time(),
                    'notes' => $request->notes,
                    'created_by' => auth()->id(),
                ]);

                // تحديث المخزون
                $stock = ProductStock::where('product_id', $adjustment['product_id'])
                                    ->where('warehouse_id', $adjustment['warehouse_id'])
                                    ->first();

                if ($stock) {
                    $stock->quantity = $actualQuantity;
                    $stock->available_quantity = $actualQuantity - $stock->reserved_quantity;
                    $stock->save();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تنفيذ تسوية المخزون بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تسوية المخزون: ' . $e->getMessage()
            ], 500);
        }
    }
}
