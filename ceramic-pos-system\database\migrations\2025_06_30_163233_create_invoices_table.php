<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('invoice_number')->unique(); // رقم الفاتورة
            $table->string('invoice_type'); // sale, purchase
            $table->date('invoice_date'); // تاريخ الفاتورة
            $table->date('due_date')->nullable(); // تاريخ الاستحقاق

            // معلومات العميل/المورد
            $table->foreignId('customer_id')->nullable()->constrained('customers')->onDelete('set null');
            $table->foreignId('supplier_id')->nullable()->constrained('suppliers')->onDelete('set null');
            $table->string('customer_name')->nullable(); // نسخة احتياطية من الاسم
            $table->text('customer_address')->nullable(); // عنوان العميل
            $table->string('customer_phone')->nullable(); // هاتف العميل
            $table->string('customer_tax_number')->nullable(); // رقم ضريبي للعميل

            // معلومات الفاتورة
            $table->foreignId('warehouse_id')->nullable()->constrained('warehouses')->onDelete('set null');
            $table->string('status')->default('draft'); // draft, confirmed, paid, cancelled
            $table->string('payment_status')->default('unpaid'); // unpaid, partial, paid
            $table->string('payment_method')->nullable(); // cash, bank_transfer, check, credit

            // المبالغ
            $table->decimal('subtotal', 15, 2)->default(0); // المجموع الفرعي
            $table->decimal('discount_amount', 15, 2)->default(0); // مبلغ الخصم
            $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة الخصم
            $table->decimal('tax_amount', 15, 2)->default(0); // مبلغ الضريبة
            $table->decimal('total_amount', 15, 2)->default(0); // المبلغ الإجمالي
            $table->decimal('paid_amount', 15, 2)->default(0); // المبلغ المدفوع
            $table->decimal('remaining_amount', 15, 2)->default(0); // المبلغ المتبقي

            // معلومات إضافية
            $table->text('notes')->nullable(); // ملاحظات
            $table->text('terms_conditions')->nullable(); // الشروط والأحكام
            $table->json('metadata')->nullable(); // بيانات إضافية

            // معلومات النظام
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('confirmed_at')->nullable(); // تاريخ التأكيد
            $table->foreignId('confirmed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['tenant_id', 'invoice_type']);
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'payment_status']);
            $table->index(['tenant_id', 'invoice_date']);
            $table->index(['tenant_id', 'customer_id']);
            $table->index(['tenant_id', 'supplier_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
