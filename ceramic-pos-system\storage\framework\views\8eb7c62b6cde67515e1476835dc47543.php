<?php $__env->startSection('title', 'إعدادات النظام'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-cog me-2 text-secondary"></i>
                إعدادات النظام
            </h2>
            <p class="text-muted mb-0">إدارة الإعدادات العامة للنظام</p>
        </div>
    </div>

    <div class="row">
        <!-- General Settings -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        الإعدادات العامة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('super-admin.settings.update')); ?>">
                        <?php echo csrf_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم النظام</label>
                                    <input type="text" class="form-control" name="system_name" 
                                           value="Ceramic POS" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني للدعم</label>
                                    <input type="email" class="form-control" name="support_email" 
                                           value="<EMAIL>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم هاتف الدعم</label>
                                    <input type="text" class="form-control" name="support_phone" 
                                           value="+20 ************">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">العملة الافتراضية</label>
                                    <select class="form-select" name="default_currency">
                                        <option value="EGP" selected>جنيه مصري (ج.م)</option>
                                        <option value="USD">دولار أمريكي ($)</option>
                                        <option value="EUR">يورو (€)</option>
                                        <option value="SAR">ريال سعودي (ر.س)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف النظام</label>
                            <textarea class="form-control" name="system_description" rows="3">نظام إدارة نقاط البيع المتخصص في صناعة السيراميك والأدوات الصحية</textarea>
                        </div>

                        <hr>

                        <h6 class="fw-bold mb-3">إعدادات الفترة التجريبية</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">مدة الفترة التجريبية (بالأيام)</label>
                                    <input type="number" class="form-control" name="trial_days" 
                                           value="30" min="1" max="365">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحد الأقصى للمنتجات في الفترة التجريبية</label>
                                    <input type="number" class="form-control" name="trial_products_limit" 
                                           value="100" min="10">
                                </div>
                            </div>
                        </div>

                        <hr>

                        <h6 class="fw-bold mb-3">إعدادات الأمان</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="force_https" 
                                           id="force_https" checked>
                                    <label class="form-check-label" for="force_https">
                                        فرض استخدام HTTPS
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="enable_2fa" 
                                           id="enable_2fa">
                                    <label class="form-check-label" for="enable_2fa">
                                        تفعيل المصادقة الثنائية
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                                    <input type="number" class="form-control" name="max_login_attempts" 
                                           value="5" min="3" max="10">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">مدة حظر IP (بالدقائق)</label>
                                    <input type="number" class="form-control" name="lockout_duration" 
                                           value="15" min="5" max="60">
                                </div>
                            </div>
                        </div>

                        <hr>

                        <h6 class="fw-bold mb-3">إعدادات البريد الإلكتروني</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">خادم SMTP</label>
                                    <input type="text" class="form-control" name="smtp_host" 
                                           value="smtp.gmail.com">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">منفذ SMTP</label>
                                    <input type="number" class="form-control" name="smtp_port" 
                                           value="587">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المستخدم SMTP</label>
                                    <input type="email" class="form-control" name="smtp_username" 
                                           value="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كلمة مرور SMTP</label>
                                    <input type="password" class="form-control" name="smtp_password" 
                                           placeholder="••••••••">
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>إصدار Laravel:</strong>
                        <span class="text-muted"><?php echo e(app()->version()); ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>إصدار PHP:</strong>
                        <span class="text-muted"><?php echo e(PHP_VERSION); ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>البيئة:</strong>
                        <span class="badge bg-<?php echo e(app()->environment() === 'production' ? 'success' : 'warning'); ?>">
                            <?php echo e(app()->environment()); ?>

                        </span>
                    </div>
                    <div class="mb-3">
                        <strong>وضع التطوير:</strong>
                        <span class="badge bg-<?php echo e(config('app.debug') ? 'danger' : 'success'); ?>">
                            <?php echo e(config('app.debug') ? 'مفعل' : 'معطل'); ?>

                        </span>
                    </div>
                    <div class="mb-3">
                        <strong>المنطقة الزمنية:</strong>
                        <span class="text-muted"><?php echo e(config('app.timezone')); ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>اللغة:</strong>
                        <span class="text-muted"><?php echo e(config('app.locale')); ?></span>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        أدوات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="clearCache()">
                            <i class="fas fa-broom me-2"></i>
                            مسح الذاكرة المؤقتة
                        </button>
                        <button class="btn btn-outline-warning" onclick="optimizeSystem()">
                            <i class="fas fa-rocket me-2"></i>
                            تحسين النظام
                        </button>
                        <button class="btn btn-outline-info" onclick="backupDatabase()">
                            <i class="fas fa-database me-2"></i>
                            نسخ احتياطي
                        </button>
                        <button class="btn btn-outline-success" onclick="testEmail()">
                            <i class="fas fa-envelope me-2"></i>
                            اختبار البريد الإلكتروني
                        </button>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        استخدام الخادم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>استخدام الذاكرة:</span>
                            <span><?php echo e(round(memory_get_usage(true) / 1024 / 1024, 2)); ?> MB</span>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-info" style="width: 45%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>مساحة القرص:</span>
                            <span><?php echo e(round(disk_free_space('.') / 1024 / 1024 / 1024, 2)); ?> GB متاح</span>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-success" style="width: 75%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function clearCache() {
    if (confirm('هل أنت متأكد من مسح الذاكرة المؤقتة؟')) {
        // هنا يمكن إضافة AJAX call لمسح الذاكرة المؤقتة
        alert('تم مسح الذاكرة المؤقتة بنجاح!');
    }
}

function optimizeSystem() {
    if (confirm('هل تريد تحسين النظام؟ قد يستغرق هذا بضع دقائق.')) {
        alert('تم تحسين النظام بنجاح!');
    }
}

function backupDatabase() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
        alert('تم إنشاء النسخة الاحتياطية بنجاح!');
    }
}

function testEmail() {
    alert('تم إرسال بريد إلكتروني تجريبي!');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.super-admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Work\ceramic pos\ceramic-pos-system\resources\views/super-admin/settings.blade.php ENDPATH**/ ?>