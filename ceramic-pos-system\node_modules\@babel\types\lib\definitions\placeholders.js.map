{"version": 3, "names": ["_utils", "require", "PLACEHOLDERS", "exports", "PLACEHOLDERS_ALIAS", "Declaration", "Pattern", "type", "alias", "ALIAS_KEYS", "length", "PLACEHOLDERS_FLIPPED_ALIAS", "Object", "keys", "for<PERSON>ach", "hasOwnProperty", "call", "push"], "sources": ["../../src/definitions/placeholders.ts"], "sourcesContent": ["import { ALIAS_KEYS } from \"./utils.ts\";\n\nexport const PLACEHOLDERS = [\n  \"Identifier\",\n  \"StringLiteral\",\n  \"Expression\",\n  \"Statement\",\n  \"Declaration\",\n  \"BlockStatement\",\n  \"ClassBody\",\n  \"Pattern\",\n] as const;\n\nexport const PLACEHOLDERS_ALIAS: Record<string, string[]> = {\n  Declaration: [\"Statement\"],\n  Pattern: [\"PatternLike\", \"LVal\"],\n};\n\nfor (const type of PLACEHOLDERS) {\n  const alias = ALIAS_KEYS[type];\n  if (alias?.length) PLACEHOLDERS_ALIAS[type] = alias;\n}\n\nexport const PLACEHOLDERS_FLIPPED_ALIAS: Record<string, string[]> = {};\n\nObject.keys(PLACEHOLDERS_ALIAS).forEach(type => {\n  PLACEHOLDERS_ALIAS[type].forEach(alias => {\n    if (!Object.hasOwn(PLACEHOLDERS_FLIPPED_ALIAS, alias)) {\n      PLACEHOLDERS_FLIPPED_ALIAS[alias] = [];\n    }\n    PLACEHOLDERS_FLIPPED_ALIAS[alias].push(type);\n  });\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEO,MAAMC,YAAY,GAAAC,OAAA,CAAAD,YAAA,GAAG,CAC1B,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,WAAW,EACX,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,SAAS,CACD;AAEH,MAAME,kBAA4C,GAAAD,OAAA,CAAAC,kBAAA,GAAG;EAC1DC,WAAW,EAAE,CAAC,WAAW,CAAC;EAC1BC,OAAO,EAAE,CAAC,aAAa,EAAE,MAAM;AACjC,CAAC;AAED,KAAK,MAAMC,IAAI,IAAIL,YAAY,EAAE;EAC/B,MAAMM,KAAK,GAAGC,iBAAU,CAACF,IAAI,CAAC;EAC9B,IAAIC,KAAK,YAALA,KAAK,CAAEE,MAAM,EAAEN,kBAAkB,CAACG,IAAI,CAAC,GAAGC,KAAK;AACrD;AAEO,MAAMG,0BAAoD,GAAAR,OAAA,CAAAQ,0BAAA,GAAG,CAAC,CAAC;AAEtEC,MAAM,CAACC,IAAI,CAACT,kBAAkB,CAAC,CAACU,OAAO,CAACP,IAAI,IAAI;EAC9CH,kBAAkB,CAACG,IAAI,CAAC,CAACO,OAAO,CAACN,KAAK,IAAI;IACxC,IAAI,CAACO,cAAA,CAAAC,IAAA,CAAcL,0BAA0B,EAAEH,KAAK,CAAC,EAAE;MACrDG,0BAA0B,CAACH,KAAK,CAAC,GAAG,EAAE;IACxC;IACAG,0BAA0B,CAACH,KAAK,CAAC,CAACS,IAAI,CAACV,IAAI,CAAC;EAC9C,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}