<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Traits\BelongsToTenant;

class Customer extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'customer_code',
        'name',
        'company_name',
        'customer_type',
        'tax_number',
        'commercial_register',
        'email',
        'phone',
        'mobile',
        'fax',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'credit_limit',
        'payment_terms',
        'opening_balance',
        'current_balance',
        'balance_type',
        'is_active',
        'notes',
        'additional_info',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'payment_terms' => 'integer',
        'is_active' => 'boolean',
        'additional_info' => 'array',
    ];

    /**
     * أنواع العملاء
     */
    const CUSTOMER_TYPES = [
        'individual' => 'فرد',
        'company' => 'شركة',
    ];

    /**
     * أنواع الأرصدة
     */
    const BALANCE_TYPES = [
        'debit' => 'مدين',
        'credit' => 'دائن',
    ];

    /**
     * البلدان المدعومة
     */
    const COUNTRIES = [
        'EG' => 'مصر',
        'SA' => 'السعودية',
        'AE' => 'الإمارات',
        'KW' => 'الكويت',
        'QA' => 'قطر',
        'BH' => 'البحرين',
        'OM' => 'عمان',
        'JO' => 'الأردن',
        'LB' => 'لبنان',
        'SY' => 'سوريا',
    ];

    /**
     * العلاقة مع الفواتير
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * العلاقة مع أوامر البيع
     */
    public function salesOrders(): HasMany
    {
        return $this->hasMany(SalesOrder::class);
    }

    /**
     * Scope للعملاء النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للعملاء حسب النوع
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('customer_type', $type);
    }

    /**
     * Scope للبحث في العملاء
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('customer_code', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%")
              ->orWhere('mobile', 'like', "%{$search}%");
        });
    }

    /**
     * Scope للعملاء المتأخرين في السداد
     */
    public function scopeOverdue($query)
    {
        return $query->where('current_balance', '>', 0)
                    ->where('balance_type', 'debit');
    }

    /**
     * Scope للعملاء الذين تجاوزوا حد الائتمان
     */
    public function scopeOverCreditLimit($query)
    {
        return $query->where('current_balance', '>', 0)
                    ->where('balance_type', 'debit')
                    ->whereRaw('current_balance > credit_limit');
    }

    /**
     * الحصول على اسم نوع العميل
     */
    public function getCustomerTypeNameAttribute(): string
    {
        return self::CUSTOMER_TYPES[$this->customer_type] ?? $this->customer_type;
    }

    /**
     * الحصول على اسم نوع الرصيد
     */
    public function getBalanceTypeNameAttribute(): string
    {
        return self::BALANCE_TYPES[$this->balance_type] ?? $this->balance_type;
    }

    /**
     * الحصول على اسم البلد
     */
    public function getCountryNameAttribute(): string
    {
        return self::COUNTRIES[$this->country] ?? $this->country;
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        if ($this->customer_type === 'company' && $this->company_name) {
            return $this->company_name . ' (' . $this->name . ')';
        }

        return $this->name;
    }

    /**
     * التحقق من تجاوز حد الائتمان
     */
    public function isOverCreditLimit(): bool
    {
        return $this->balance_type === 'debit' &&
               $this->current_balance > $this->credit_limit;
    }

    /**
     * الحصول على الرصيد المتاح للائتمان
     */
    public function getAvailableCreditAttribute(): float
    {
        if ($this->balance_type === 'debit') {
            return max(0, $this->credit_limit - $this->current_balance);
        }

        return $this->credit_limit;
    }

    /**
     * تحديث الرصيد
     */
    public function updateBalance(float $amount, string $type = 'debit'): void
    {
        if ($type === 'debit') {
            if ($this->balance_type === 'debit') {
                $this->current_balance += $amount;
            } else {
                $this->current_balance -= $amount;
                if ($this->current_balance < 0) {
                    $this->current_balance = abs($this->current_balance);
                    $this->balance_type = 'debit';
                }
            }
        } else { // credit
            if ($this->balance_type === 'credit') {
                $this->current_balance += $amount;
            } else {
                $this->current_balance -= $amount;
                if ($this->current_balance < 0) {
                    $this->current_balance = abs($this->current_balance);
                    $this->balance_type = 'credit';
                }
            }
        }

        $this->save();
    }

    /**
     * الحصول على إجمالي المبيعات
     */
    public function getTotalSalesAttribute(): float
    {
        return $this->invoices()->sum('total_amount') ?? 0;
    }

    /**
     * الحصول على إجمالي المدفوعات
     */
    public function getTotalPaymentsAttribute(): float
    {
        return $this->payments()->sum('amount') ?? 0;
    }

    /**
     * الحصول على آخر فاتورة
     */
    public function getLastInvoiceAttribute()
    {
        return $this->invoices()->latest()->first();
    }

    /**
     * الحصول على آخر دفعة
     */
    public function getLastPaymentAttribute()
    {
        return $this->payments()->latest()->first();
    }
}
