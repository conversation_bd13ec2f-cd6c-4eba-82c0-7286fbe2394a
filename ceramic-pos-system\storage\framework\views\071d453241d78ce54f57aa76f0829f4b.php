<?php $__env->startSection('title', 'إدارة المشتركين'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-building me-2 text-primary"></i>
                إدارة المشتركين
            </h2>
            <p class="text-muted mb-0">إدارة جميع المشتركين في النظام</p>
        </div>
        <div>
            <a href="<?php echo e(route('super-admin.tenants.create')); ?>" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                إضافة مشترك جديد
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="اسم الشركة، البريد الإلكتروني، أو النطاق الفرعي"
                           value="<?php echo e(request('search')); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="trial" <?php echo e(request('status') == 'trial' ? 'selected' : ''); ?>>تجريبي</option>
                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>نشط</option>
                        <option value="suspended" <?php echo e(request('status') == 'suspended' ? 'selected' : ''); ?>>معلق</option>
                        <option value="expired" <?php echo e(request('status') == 'expired' ? 'selected' : ''); ?>>منتهي</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الخطة</label>
                    <select name="plan" class="form-select">
                        <option value="">جميع الخطط</option>
                        <option value="basic" <?php echo e(request('plan') == 'basic' ? 'selected' : ''); ?>>أساسية</option>
                        <option value="premium" <?php echo e(request('plan') == 'premium' ? 'selected' : ''); ?>>متقدمة</option>
                        <option value="enterprise" <?php echo e(request('plan') == 'enterprise' ? 'selected' : ''); ?>>مؤسسية</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Tenants Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة المشتركين (<?php echo e($tenants->total()); ?>)
            </h5>
        </div>
        <div class="card-body">
            <?php if($tenants->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الشركة</th>
                                <th>المالك</th>
                                <th>الخطة</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>انتهاء الاشتراك</th>
                                <th>الإيرادات الشهرية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo e($tenant->company_name); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-globe me-1"></i>
                                            <?php echo e($tenant->subdomain); ?>.ceramicpos.com
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <?php echo e($tenant->city); ?>

                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo e($tenant->owner_name); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>
                                            <?php echo e($tenant->email); ?>

                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-phone me-1"></i>
                                            <?php echo e($tenant->phone); ?>

                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary fs-6"><?php echo e($tenant->getPlanName()); ?></span>
                                </td>
                                <td>
                                    <?php switch($tenant->status):
                                        case ('active'): ?>
                                            <span class="badge bg-success fs-6">نشط</span>
                                            <?php break; ?>
                                        <?php case ('trial'): ?>
                                            <span class="badge bg-warning fs-6">تجريبي</span>
                                            <?php if($tenant->trial_ends_at): ?>
                                                <br>
                                                <small class="text-muted">
                                                    ينتهي <?php echo e($tenant->trial_ends_at->diffForHumans()); ?>

                                                </small>
                                            <?php endif; ?>
                                            <?php break; ?>
                                        <?php case ('suspended'): ?>
                                            <span class="badge bg-danger fs-6">معلق</span>
                                            <?php break; ?>
                                        <?php case ('expired'): ?>
                                            <span class="badge bg-dark fs-6">منتهي</span>
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </td>
                                <td><?php echo e($tenant->created_at->format('Y/m/d')); ?></td>
                                <td>
                                    <?php if($tenant->subscription_ends_at): ?>
                                        <?php echo e($tenant->subscription_ends_at->format('Y/m/d')); ?>

                                        <br>
                                        <small class="text-muted">
                                            <?php echo e($tenant->subscription_ends_at->diffForHumans()); ?>

                                        </small>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong class="text-success"><?php echo e(number_format($tenant->monthly_price)); ?> ج.م</strong>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?php echo e(route('super-admin.tenants.show', $tenant->id)); ?>" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('dashboard', ['tenant' => $tenant->id])); ?>" 
                                           class="btn btn-outline-primary" title="دخول للنظام" target="_blank">
                                            <i class="fas fa-sign-in-alt"></i>
                                        </a>
                                        <button class="btn btn-outline-warning" 
                                                onclick="showStatusModal(<?php echo e($tenant->id); ?>, '<?php echo e($tenant->status); ?>')"
                                                title="تغيير الحالة">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" 
                                                onclick="confirmDelete(<?php echo e($tenant->id); ?>, '<?php echo e($tenant->company_name); ?>')"
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($tenants->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مشتركين</h5>
                    <p class="text-muted">لم يتم العثور على أي مشتركين مطابقين للبحث</p>
                    <a href="<?php echo e(route('super-admin.tenants.create')); ?>" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول مشترك
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة المشترك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="statusForm" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" id="statusSelect" class="form-select" required>
                            <option value="trial">تجريبي</option>
                            <option value="active">نشط</option>
                            <option value="suspended">معلق</option>
                            <option value="expired">منتهي</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تاريخ انتهاء الاشتراك</label>
                        <input type="date" name="subscription_ends_at" class="form-control">
                        <small class="text-muted">اتركه فارغاً للاشتراكات التجريبية</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المشترك <strong id="deleteCompanyName"></strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> سيتم حذف جميع البيانات المرتبطة بهذا المشترك نهائياً ولا يمكن استرجاعها.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">حذف نهائياً</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function showStatusModal(tenantId, currentStatus) {
    document.getElementById('statusForm').action = `/super-admin/tenants/${tenantId}/status`;
    document.getElementById('statusSelect').value = currentStatus;
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

function confirmDelete(tenantId, companyName) {
    document.getElementById('deleteCompanyName').textContent = companyName;
    document.getElementById('deleteForm').action = `/super-admin/tenants/${tenantId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.super-admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Work\ceramic pos\ceramic-pos-system\resources\views/super-admin/tenants/index.blade.php ENDPATH**/ ?>