<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('warehouse_code', 20)->unique(); // رقم المخزن
            $table->string('name'); // اسم المخزن
            $table->text('description')->nullable(); // وصف المخزن
            $table->text('address')->nullable(); // عنوان المخزن
            $table->string('city')->nullable();
            $table->string('phone')->nullable();
            $table->string('manager_name')->nullable(); // اسم مدير المخزن
            $table->string('manager_phone')->nullable();
            $table->boolean('is_main')->default(false); // مخزن رئيسي
            $table->boolean('is_active')->default(true);
            $table->json('settings')->nullable(); // إعدادات المخزن
            $table->timestamps();

            $table->index(['tenant_id', 'warehouse_code']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['tenant_id', 'is_main']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouses');
    }
};
