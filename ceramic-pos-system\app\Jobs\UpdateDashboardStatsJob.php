<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use App\Models\Tenant;
use App\Services\DashboardService;
use App\Helpers\DashboardHelper;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class UpdateDashboardStatsJob implements ShouldQueue
{
    use Queueable;

    protected $tenant;
    protected $period;

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant, string $period = 'month')
    {
        $this->tenant = $tenant;
        $this->period = $period;
    }

    /**
     * Execute the job.
     */
    public function handle(DashboardService $dashboardService): void
    {
        try {
            // تعيين السياق للشركة
            tenancy()->initialize($this->tenant);

            Log::info("بدء تحديث إحصائيات لوحة التحكم للشركة: {$this->tenant->name}");

            // الحصول على الإحصائيات الشاملة
            $stats = $dashboardService->getComprehensiveStats($this->period);

            // حفظ الإحصائيات في الكاش
            $cacheKey = "dashboard_stats_{$this->tenant->id}_{$this->period}";
            Cache::put($cacheKey, $stats, now()->addHours(2));

            // حفظ إحصائيات سريعة منفصلة
            $quickStats = [
                'total_sales' => $stats['financial']['revenue'] ?? 0,
                'total_purchases' => $stats['financial']['expenses'] ?? 0,
                'net_profit' => $stats['financial']['gross_profit'] ?? 0,
                'cash_flow' => $stats['financial']['net_cash_flow'] ?? 0,
                'low_stock_count' => $stats['inventory']['low_stock_products'] ?? 0,
                'overdue_invoices_count' => $stats['sales']['overdue_invoices'] ?? 0,
                'pending_payments_count' => $stats['sales']['pending_payments'] ?? 0,
            ];

            $quickCacheKey = "dashboard_quick_stats_{$this->tenant->id}";
            Cache::put($quickCacheKey, $quickStats, now()->addMinutes(30));

            // تحديث كاش التنبيهات
            $alerts = DashboardHelper::getAlertsCount();
            $alertsCacheKey = "dashboard_alerts_count_{$this->tenant->id}";
            Cache::put($alertsCacheKey, $alerts, now()->addMinutes(15));

            // تحديث كاش أفضل العملاء والمنتجات
            $topCustomers = DashboardHelper::getTopCustomers(10);
            $topProducts = DashboardHelper::getTopProducts(10);

            Cache::put("dashboard_top_customers_{$this->tenant->id}_10", $topCustomers, now()->addHours(1));
            Cache::put("dashboard_top_products_{$this->tenant->id}_10", $topProducts, now()->addHours(1));

            Log::info("تم تحديث إحصائيات لوحة التحكم بنجاح للشركة: {$this->tenant->name}");

        } catch (\Exception $e) {
            Log::error("خطأ في تحديث إحصائيات لوحة التحكم للشركة {$this->tenant->name}: " . $e->getMessage());

            // إعادة المحاولة في حالة الفشل
            $this->fail($e);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("فشل في تحديث إحصائيات لوحة التحكم للشركة {$this->tenant->name}: " . $exception->getMessage());
    }
}
