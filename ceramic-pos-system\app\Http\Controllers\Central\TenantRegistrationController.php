<?php

namespace App\Http\Controllers\Central;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Tenant;
use App\Models\User;
use App\Models\Tax;
use App\Services\ChartOfAccountService;
use App\Services\CustomerSupplierService;
use App\Services\InventoryService;
use App\Services\InvoiceService;
use App\Services\PaymentService;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class TenantRegistrationController extends Controller
{
    /**
     * عرض نموذج تسجيل شركة جديدة
     */
    public function create()
    {
        return view('central.register-company');
    }

    /**
     * تسجيل شركة جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'company_name' => 'required|string|max:255',
            'subdomain' => 'required|string|max:50|unique:tenants,subdomain|alpha_dash',
            'admin_name' => 'required|string|max:255',
            'admin_email' => 'required|email|unique:users,email',
            'admin_password' => 'required|string|min:8|confirmed',
            'contact_phone' => 'nullable|string|max:20',
            'plan' => 'required|in:basic,standard,premium',
        ], [
            'company_name.required' => 'اسم الشركة مطلوب',
            'subdomain.required' => 'النطاق الفرعي مطلوب',
            'subdomain.unique' => 'النطاق الفرعي مستخدم من قبل',
            'subdomain.alpha_dash' => 'النطاق الفرعي يجب أن يحتوي على أحرف وأرقام فقط',
            'admin_email.unique' => 'البريد الإلكتروني مستخدم من قبل',
            'admin_password.min' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
            'admin_password.confirmed' => 'تأكيد كلمة المرور غير متطابق',
        ]);

        DB::beginTransaction();

        try {
            // إنشاء الـ tenant
            $tenant = Tenant::create([
                'name' => $request->company_name,
                'slug' => Str::slug($request->company_name),
                'subdomain' => $request->subdomain,
                'status' => 'active',
                'plan' => $request->plan,
                'subscription_start' => now(),
                'subscription_end' => now()->addYear(), // اشتراك لمدة سنة
                'max_users' => $this->getMaxUsers($request->plan),
                'contact_email' => $request->admin_email,
                'contact_phone' => $request->contact_phone,
                'country' => 'EG',
                'currency' => 'EGP',
                'timezone' => 'Africa/Cairo',
                'language' => 'ar',
                'features' => $this->getPlanFeatures($request->plan),
            ]);

            // إنشاء المستخدم الأول (المشرف)
            $user = User::create([
                'tenant_id' => $tenant->id,
                'name' => $request->admin_name,
                'email' => $request->admin_email,
                'password' => Hash::make($request->admin_password),
                'phone' => $request->contact_phone,
                'status' => 'active',
            ]);

            // إنشاء دليل الحسابات الأساسي
            $chartOfAccountService = new ChartOfAccountService();
            $chartOfAccountService->createDefaultChartOfAccounts($tenant);

            // إنشاء الضرائب الافتراضية
            $this->createDefaultTaxes($tenant);

            // إنشاء عملاء وموردين تجريبيين
            $customerSupplierService = new CustomerSupplierService();
            $customerSupplierService->createSampleCustomersAndSuppliers($tenant);

            // إنشاء مخازن ومنتجات تجريبية
            $inventoryService = new InventoryService();
            $inventoryService->createSampleInventory($tenant);

            // إنشاء فواتير تجريبية
            $invoiceService = new InvoiceService();
            $invoiceService->createSampleInvoices($tenant);

            // إنشاء مدفوعات تجريبية
            $paymentService = new PaymentService();
            $paymentService->createSamplePayments($tenant);

            DB::commit();

            return redirect()->route('central.home')
                ->with('success', 'تم تسجيل الشركة بنجاح! يمكنك الآن الوصول إليها من: ' . $request->subdomain . '.localhost:8000');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تسجيل الشركة: ' . $e->getMessage());
        }
    }

    /**
     * الحصول على الحد الأقصى للمستخدمين حسب الخطة
     */
    private function getMaxUsers($plan)
    {
        return match($plan) {
            'basic' => 5,
            'standard' => 20,
            'premium' => 100,
            default => 5,
        };
    }

    /**
     * الحصول على ميزات الخطة
     */
    private function getPlanFeatures($plan)
    {
        $features = [
            'basic' => [
                'customers' => true,
                'suppliers' => true,
                'products' => true,
                'inventory' => true,
                'invoices' => true,
                'basic_reports' => true,
                'advanced_reports' => false,
                'multi_warehouse' => false,
                'api_access' => false,
            ],
            'standard' => [
                'customers' => true,
                'suppliers' => true,
                'products' => true,
                'inventory' => true,
                'invoices' => true,
                'basic_reports' => true,
                'advanced_reports' => true,
                'multi_warehouse' => true,
                'api_access' => false,
            ],
            'premium' => [
                'customers' => true,
                'suppliers' => true,
                'products' => true,
                'inventory' => true,
                'invoices' => true,
                'basic_reports' => true,
                'advanced_reports' => true,
                'multi_warehouse' => true,
                'api_access' => true,
            ],
        ];

        return $features[$plan] ?? $features['basic'];
    }

    /**
     * إنشاء الضرائب الافتراضية للشركة
     */
    private function createDefaultTaxes(Tenant $tenant)
    {
        $taxes = [
            [
                'tenant_id' => $tenant->id,
                'tax_code' => 'VAT14',
                'name' => 'ضريبة القيمة المضافة',
                'name_en' => 'Value Added Tax',
                'type' => 'percentage',
                'rate' => 14.00,
                'amount' => null,
                'is_inclusive' => false,
                'is_default' => true,
                'is_active' => true,
                'description' => 'ضريبة القيمة المضافة 14% حسب النظام المصري',
            ],
            [
                'tenant_id' => $tenant->id,
                'tax_code' => 'SALES_TAX',
                'name' => 'ضريبة المبيعات',
                'name_en' => 'Sales Tax',
                'type' => 'percentage',
                'rate' => 10.00,
                'amount' => null,
                'is_inclusive' => false,
                'is_default' => false,
                'is_active' => true,
                'description' => 'ضريبة المبيعات 10% على بعض السلع',
            ],
            [
                'tenant_id' => $tenant->id,
                'tax_code' => 'EXEMPT',
                'name' => 'معفى من الضريبة',
                'name_en' => 'Tax Exempt',
                'type' => 'percentage',
                'rate' => 0.00,
                'amount' => null,
                'is_inclusive' => false,
                'is_default' => false,
                'is_active' => true,
                'description' => 'معفى من ضريبة القيمة المضافة',
            ],
        ];

        foreach ($taxes as $taxData) {
            Tax::create($taxData);
        }
    }
}
