<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\StockMovement;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardService
{
    /**
     * الحصول على إحصائيات شاملة للوحة التحكم
     */
    public function getComprehensiveStats(string $period = 'month'): array
    {
        $startDate = $this->getStartDate($period);
        $endDate = now();

        return [
            'financial' => $this->getFinancialStats($startDate, $endDate),
            'sales' => $this->getSalesAnalytics($startDate, $endDate),
            'purchases' => $this->getPurchaseAnalytics($startDate, $endDate),
            'inventory' => $this->getInventoryAnalytics(),
            'customers' => $this->getCustomerAnalytics(),
            'suppliers' => $this->getSupplierAnalytics(),
            'performance' => $this->getPerformanceMetrics($startDate, $endDate),
        ];
    }

    /**
     * الإحصائيات المالية
     */
    private function getFinancialStats($startDate, $endDate): array
    {
        $totalSales = Invoice::sales()
                            ->confirmed()
                            ->whereBetween('invoice_date', [$startDate, $endDate])
                            ->sum('total_amount');

        $totalPurchases = Invoice::purchases()
                                ->confirmed()
                                ->whereBetween('invoice_date', [$startDate, $endDate])
                                ->sum('total_amount');

        $totalReceived = Payment::received()
                               ->cleared()
                               ->whereBetween('payment_date', [$startDate, $endDate])
                               ->sum('amount');

        $totalPaid = Payment::paid()
                           ->cleared()
                           ->whereBetween('payment_date', [$startDate, $endDate])
                           ->sum('amount');

        return [
            'revenue' => $totalSales,
            'expenses' => $totalPurchases,
            'gross_profit' => $totalSales - $totalPurchases,
            'profit_margin' => $totalSales > 0 ? (($totalSales - $totalPurchases) / $totalSales) * 100 : 0,
            'cash_received' => $totalReceived,
            'cash_paid' => $totalPaid,
            'net_cash_flow' => $totalReceived - $totalPaid,
            'accounts_receivable' => Invoice::sales()->confirmed()->sum('remaining_amount'),
            'accounts_payable' => Invoice::purchases()->confirmed()->sum('remaining_amount'),
        ];
    }

    /**
     * تحليلات المبيعات
     */
    private function getSalesAnalytics($startDate, $endDate): array
    {
        $salesQuery = Invoice::sales()
                            ->confirmed()
                            ->whereBetween('invoice_date', [$startDate, $endDate]);

        $totalInvoices = $salesQuery->count();
        $totalAmount = $salesQuery->sum('total_amount');

        return [
            'total_invoices' => $totalInvoices,
            'total_amount' => $totalAmount,
            'average_invoice_value' => $totalInvoices > 0 ? $totalAmount / $totalInvoices : 0,
            'largest_invoice' => $salesQuery->max('total_amount') ?? 0,
            'smallest_invoice' => $salesQuery->min('total_amount') ?? 0,
            'paid_invoices' => Invoice::sales()
                                     ->confirmed()
                                     ->whereBetween('invoice_date', [$startDate, $endDate])
                                     ->where('payment_status', 'paid')
                                     ->count(),
            'overdue_invoices' => Invoice::sales()->overdue()->count(),
            'conversion_rate' => $this->calculateSalesConversionRate($startDate, $endDate),
        ];
    }

    /**
     * تحليلات المشتريات
     */
    private function getPurchaseAnalytics($startDate, $endDate): array
    {
        $purchaseQuery = Invoice::purchases()
                               ->confirmed()
                               ->whereBetween('invoice_date', [$startDate, $endDate]);

        $totalInvoices = $purchaseQuery->count();
        $totalAmount = $purchaseQuery->sum('total_amount');

        return [
            'total_invoices' => $totalInvoices,
            'total_amount' => $totalAmount,
            'average_invoice_value' => $totalInvoices > 0 ? $totalAmount / $totalInvoices : 0,
            'largest_invoice' => $purchaseQuery->max('total_amount') ?? 0,
            'smallest_invoice' => $purchaseQuery->min('total_amount') ?? 0,
            'paid_invoices' => Invoice::purchases()
                                     ->confirmed()
                                     ->whereBetween('invoice_date', [$startDate, $endDate])
                                     ->where('payment_status', 'paid')
                                     ->count(),
            'overdue_invoices' => Invoice::purchases()->overdue()->count(),
        ];
    }

    /**
     * تحليلات المخزون
     */
    private function getInventoryAnalytics(): array
    {
        $totalProducts = Product::count();
        $activeProducts = Product::active()->count();
        $lowStockProducts = Product::lowStock()->count();
        $outOfStockProducts = Product::outOfStock()->count();

        return [
            'total_products' => $totalProducts,
            'active_products' => $activeProducts,
            'inactive_products' => $totalProducts - $activeProducts,
            'low_stock_products' => $lowStockProducts,
            'out_of_stock_products' => $outOfStockProducts,
            'stock_health_percentage' => $totalProducts > 0 ? 
                (($totalProducts - $lowStockProducts - $outOfStockProducts) / $totalProducts) * 100 : 0,
            'total_stock_value' => ProductStock::get()->sum('stock_value'),
            'average_stock_value_per_product' => $totalProducts > 0 ? 
                ProductStock::get()->sum('stock_value') / $totalProducts : 0,
            'total_quantity' => ProductStock::sum('quantity'),
            'reserved_quantity' => ProductStock::sum('reserved_quantity'),
            'available_quantity' => ProductStock::sum('available_quantity'),
        ];
    }

    /**
     * تحليلات العملاء
     */
    private function getCustomerAnalytics(): array
    {
        $totalCustomers = Customer::count();
        $activeCustomers = Customer::active()->count();

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'inactive_customers' => $totalCustomers - $activeCustomers,
            'customers_with_balance' => Customer::where('current_balance', '>', 0)->count(),
            'new_customers_this_month' => Customer::whereMonth('created_at', now()->month)->count(),
            'average_customer_value' => $this->calculateAverageCustomerValue(),
            'top_customers' => $this->getTopCustomersByValue(5),
            'customer_retention_rate' => $this->calculateCustomerRetentionRate(),
        ];
    }

    /**
     * تحليلات الموردين
     */
    private function getSupplierAnalytics(): array
    {
        $totalSuppliers = Supplier::count();
        $activeSuppliers = Supplier::active()->count();

        return [
            'total_suppliers' => $totalSuppliers,
            'active_suppliers' => $activeSuppliers,
            'inactive_suppliers' => $totalSuppliers - $activeSuppliers,
            'suppliers_with_balance' => Supplier::where('current_balance', '>', 0)->count(),
            'new_suppliers_this_month' => Supplier::whereMonth('created_at', now()->month)->count(),
            'average_supplier_value' => $this->calculateAverageSupplierValue(),
            'top_suppliers' => $this->getTopSuppliersByValue(5),
        ];
    }

    /**
     * مؤشرات الأداء
     */
    private function getPerformanceMetrics($startDate, $endDate): array
    {
        return [
            'inventory_turnover' => $this->calculateInventoryTurnover($startDate, $endDate),
            'days_sales_outstanding' => $this->calculateDaysSalesOutstanding(),
            'days_payable_outstanding' => $this->calculateDaysPayableOutstanding(),
            'cash_conversion_cycle' => $this->calculateCashConversionCycle(),
            'gross_margin' => $this->calculateGrossMargin($startDate, $endDate),
            'return_on_assets' => $this->calculateReturnOnAssets($startDate, $endDate),
        ];
    }

    /**
     * حساب معدل دوران المخزون
     */
    private function calculateInventoryTurnover($startDate, $endDate): float
    {
        $costOfGoodsSold = Invoice::sales()
                                 ->confirmed()
                                 ->whereBetween('invoice_date', [$startDate, $endDate])
                                 ->sum('total_amount');

        $averageInventory = ProductStock::avg('stock_value') ?? 1;

        return $averageInventory > 0 ? $costOfGoodsSold / $averageInventory : 0;
    }

    /**
     * حساب أيام المبيعات المستحقة
     */
    private function calculateDaysSalesOutstanding(): float
    {
        $accountsReceivable = Invoice::sales()->confirmed()->sum('remaining_amount');
        $dailySales = Invoice::sales()
                            ->confirmed()
                            ->whereDate('invoice_date', '>=', now()->subDays(30))
                            ->sum('total_amount') / 30;

        return $dailySales > 0 ? $accountsReceivable / $dailySales : 0;
    }

    /**
     * حساب أيام المدفوعات المستحقة
     */
    private function calculateDaysPayableOutstanding(): float
    {
        $accountsPayable = Invoice::purchases()->confirmed()->sum('remaining_amount');
        $dailyPurchases = Invoice::purchases()
                                ->confirmed()
                                ->whereDate('invoice_date', '>=', now()->subDays(30))
                                ->sum('total_amount') / 30;

        return $dailyPurchases > 0 ? $accountsPayable / $dailyPurchases : 0;
    }

    /**
     * حساب دورة تحويل النقد
     */
    private function calculateCashConversionCycle(): float
    {
        $dso = $this->calculateDaysSalesOutstanding();
        $dpo = $this->calculateDaysPayableOutstanding();
        $inventoryDays = 30; // افتراضي

        return $dso + $inventoryDays - $dpo;
    }

    /**
     * حساب الهامش الإجمالي
     */
    private function calculateGrossMargin($startDate, $endDate): float
    {
        $sales = Invoice::sales()
                       ->confirmed()
                       ->whereBetween('invoice_date', [$startDate, $endDate])
                       ->sum('total_amount');

        $purchases = Invoice::purchases()
                           ->confirmed()
                           ->whereBetween('invoice_date', [$startDate, $endDate])
                           ->sum('total_amount');

        return $sales > 0 ? (($sales - $purchases) / $sales) * 100 : 0;
    }

    /**
     * حساب العائد على الأصول
     */
    private function calculateReturnOnAssets($startDate, $endDate): float
    {
        $netIncome = $this->calculateNetProfit($startDate, $endDate);
        $totalAssets = ProductStock::sum('stock_value') + 
                      Invoice::sales()->confirmed()->sum('remaining_amount');

        return $totalAssets > 0 ? ($netIncome / $totalAssets) * 100 : 0;
    }

    /**
     * حساب صافي الربح
     */
    private function calculateNetProfit($startDate, $endDate): float
    {
        $sales = Invoice::sales()
                       ->confirmed()
                       ->whereBetween('invoice_date', [$startDate, $endDate])
                       ->sum('total_amount');

        $purchases = Invoice::purchases()
                           ->confirmed()
                           ->whereBetween('invoice_date', [$startDate, $endDate])
                           ->sum('total_amount');

        return $sales - $purchases;
    }

    /**
     * حساب متوسط قيمة العميل
     */
    private function calculateAverageCustomerValue(): float
    {
        $totalCustomers = Customer::count();
        $totalSales = Invoice::sales()->confirmed()->sum('total_amount');

        return $totalCustomers > 0 ? $totalSales / $totalCustomers : 0;
    }

    /**
     * حساب متوسط قيمة المورد
     */
    private function calculateAverageSupplierValue(): float
    {
        $totalSuppliers = Supplier::count();
        $totalPurchases = Invoice::purchases()->confirmed()->sum('total_amount');

        return $totalSuppliers > 0 ? $totalPurchases / $totalSuppliers : 0;
    }

    /**
     * حساب معدل تحويل المبيعات
     */
    private function calculateSalesConversionRate($startDate, $endDate): float
    {
        $totalInvoices = Invoice::sales()
                               ->whereBetween('invoice_date', [$startDate, $endDate])
                               ->count();

        $confirmedInvoices = Invoice::sales()
                                   ->confirmed()
                                   ->whereBetween('invoice_date', [$startDate, $endDate])
                                   ->count();

        return $totalInvoices > 0 ? ($confirmedInvoices / $totalInvoices) * 100 : 0;
    }

    /**
     * حساب معدل الاحتفاظ بالعملاء
     */
    private function calculateCustomerRetentionRate(): float
    {
        $customersLastMonth = Customer::whereMonth('created_at', now()->subMonth()->month)->count();
        $returningCustomers = Customer::whereHas('salesInvoices', function($query) {
            $query->whereMonth('invoice_date', now()->month);
        })->whereMonth('created_at', '<', now()->month)->count();

        return $customersLastMonth > 0 ? ($returningCustomers / $customersLastMonth) * 100 : 0;
    }

    /**
     * الحصول على أفضل العملاء حسب القيمة
     */
    private function getTopCustomersByValue($limit = 5): array
    {
        return Customer::withSum(['salesInvoices' => function($query) {
                $query->where('status', 'confirmed');
            }], 'total_amount')
            ->orderBy('sales_invoices_sum_total_amount', 'desc')
            ->limit($limit)
            ->get(['id', 'name', 'customer_code'])
            ->toArray();
    }

    /**
     * الحصول على أفضل الموردين حسب القيمة
     */
    private function getTopSuppliersByValue($limit = 5): array
    {
        return Supplier::withSum(['purchaseInvoices' => function($query) {
                $query->where('status', 'confirmed');
            }], 'total_amount')
            ->orderBy('purchase_invoices_sum_total_amount', 'desc')
            ->limit($limit)
            ->get(['id', 'name', 'supplier_code'])
            ->toArray();
    }

    /**
     * الحصول على تاريخ البداية حسب الفترة
     */
    private function getStartDate($period): Carbon
    {
        return match($period) {
            'today' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }
}
