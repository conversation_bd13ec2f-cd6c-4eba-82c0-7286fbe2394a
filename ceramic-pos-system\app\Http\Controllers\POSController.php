<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\SimpleTenant;
use Illuminate\Support\Facades\DB;

class POSController extends Controller
{
    /**
     * عرض شاشة نقطة البيع
     */
    public function index(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        // جلب المنتجات النشطة للمشترك
        $products = Product::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->take(20)
            ->get();

        // جلب العملاء للمشترك
        $customers = Customer::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->get();

        return view('pos.index', compact('tenant', 'products', 'customers'));
    }

    /**
     * البحث في المنتجات
     */
    public function searchProducts(Request $request)
    {
        $search = $request->get('q');

        $products = Product::where('is_active', true)
            ->where('current_stock', '>', 0)
            ->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('barcode', 'like', "%{$search}%");
            })
            ->limit(10)
            ->get(['id', 'name', 'code', 'barcode', 'selling_price', 'current_stock', 'unit']);

        return response()->json($products);
    }

    /**
     * إتمام عملية البيع
     */
    public function checkout(Request $request)
    {
        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,card,credit',
            'paid_amount' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
        ]);

        DB::beginTransaction();

        try {
            // حساب الإجماليات
            $subtotal = 0;
            foreach ($request->items as $item) {
                $subtotal += $item['quantity'] * $item['price'];
            }

            $discount = $request->discount ?? 0;
            $taxRate = $request->tax_rate ?? 0;
            $taxAmount = (($subtotal - $discount) * $taxRate) / 100;
            $total = $subtotal - $discount + $taxAmount;

            // إنشاء الفاتورة
            $invoice = Invoice::create([
                'invoice_number' => $this->generateInvoiceNumber(),
                'customer_id' => $request->customer_id,
                'type' => 'sale',
                'status' => 'completed',
                'invoice_date' => now(),
                'due_date' => now(),
                'subtotal' => $subtotal,
                'discount_amount' => $discount,
                'tax_amount' => $taxAmount,
                'total_amount' => $total,
                'paid_amount' => $request->paid_amount,
                'remaining_amount' => max(0, $total - $request->paid_amount),
                'payment_method' => $request->payment_method,
                'payment_status' => $request->paid_amount >= $total ? 'paid' : 'partial',
                'notes' => $request->notes,
                'created_by' => auth()->id(),
                'confirmed_by' => auth()->id(),
                'confirmed_at' => now(),
            ]);

            // إضافة عناصر الفاتورة وتحديث المخزون
            foreach ($request->items as $item) {
                $product = Product::findOrFail($item['product_id']);

                // التحقق من توفر المخزون
                if ($product->current_stock < $item['quantity']) {
                    throw new \Exception("المخزون غير كافي للمنتج: {$product->name}");
                }

                // إضافة عنصر الفاتورة
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['price'],
                    'total_price' => $item['quantity'] * $item['price'],
                ]);

                // تحديث المخزون
                $product->decrement('current_stock', $item['quantity']);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إتمام عملية البيع بنجاح',
                'invoice_id' => $invoice->id,
                'change' => max(0, $request->paid_amount - $total)
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * توليد رقم فاتورة جديد
     */
    private function generateInvoiceNumber()
    {
        $lastInvoice = Invoice::where('type', 'sale')
            ->whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastNumber = intval(substr($lastInvoice->invoice_number, -4));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return 'INV-' . date('Ymd') . '-' . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
