<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Payment;
use App\Models\SimpleTenant;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportsController extends Controller
{
    /**
     * عرض لوحة تحكم التقارير
     */
    public function index(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        return view('reports.index', compact('tenant'));
    }

    /**
     * تقرير المبيعات
     */
    public function sales(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $dateFrom = $request->get('date_from', now()->startOfMonth());
        $dateTo = $request->get('date_to', now()->endOfMonth());

        // إحصائيات المبيعات
        $salesStats = [
            'total_invoices' => Invoice::where('type', 'sale')
                ->whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->count(),
            'total_amount' => Invoice::where('type', 'sale')
                ->where('status', 'completed')
                ->whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->sum('total_amount'),
            'average_invoice' => Invoice::where('type', 'sale')
                ->where('status', 'completed')
                ->whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->avg('total_amount'),
            'paid_amount' => Invoice::where('type', 'sale')
                ->whereBetween('invoice_date', [$dateFrom, $dateTo])
                ->sum('paid_amount'),
        ];

        // المبيعات اليومية
        $dailySales = Invoice::where('type', 'sale')
            ->where('status', 'completed')
            ->whereBetween('invoice_date', [$dateFrom, $dateTo])
            ->selectRaw('DATE(invoice_date) as date, SUM(total_amount) as total')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // أفضل العملاء
        $topCustomers = Invoice::where('type', 'sale')
            ->where('status', 'completed')
            ->whereBetween('invoice_date', [$dateFrom, $dateTo])
            ->with('customer')
            ->selectRaw('customer_id, SUM(total_amount) as total')
            ->groupBy('customer_id')
            ->orderBy('total', 'desc')
            ->take(10)
            ->get();

        return view('reports.sales', compact(
            'tenant', 'salesStats', 'dailySales', 'topCustomers', 'dateFrom', 'dateTo'
        ));
    }

    /**
     * تقرير المخزون
     */
    public function inventory(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        // إحصائيات المخزون
        $inventoryStats = [
            'total_products' => Product::where('is_active', true)->count(),
            'low_stock_products' => Product::whereRaw('COALESCE((SELECT SUM(quantity) FROM product_stock WHERE product_id = products.id), 0) <= minimum_stock')->count(),
            'out_of_stock' => Product::whereRaw('COALESCE((SELECT SUM(quantity) FROM product_stock WHERE product_id = products.id), 0) = 0')->count(),
            'total_value' => DB::table('products')
                ->selectRaw('SUM(purchase_price * COALESCE((SELECT SUM(quantity) FROM product_stock WHERE product_id = products.id), 0)) as total')
                ->value('total') ?? 0,
        ];

        // المنتجات منخفضة المخزون
        $lowStockProducts = Product::whereRaw('COALESCE((SELECT SUM(quantity) FROM product_stock WHERE product_id = products.id), 0) <= minimum_stock')
            ->with(['category'])
            ->get();

        // أكثر المنتجات مبيعاً
        $topSellingProducts = DB::table('invoice_items')
            ->join('invoices', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->join('products', 'invoice_items.product_id', '=', 'products.id')
            ->where('invoices.type', 'sale')
            ->where('invoices.status', 'completed')
            ->selectRaw('products.name, products.product_code, SUM(invoice_items.quantity) as total_sold, SUM(invoice_items.total_price) as total_revenue')
            ->groupBy('products.id', 'products.name', 'products.product_code')
            ->orderBy('total_sold', 'desc')
            ->take(10)
            ->get();

        return view('reports.inventory', compact(
            'tenant', 'inventoryStats', 'lowStockProducts', 'topSellingProducts'
        ));
    }

    /**
     * تقرير العملاء
     */
    public function customers(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $dateFrom = $request->get('date_from', now()->startOfMonth());
        $dateTo = $request->get('date_to', now()->endOfMonth());

        // إحصائيات العملاء
        $customerStats = [
            'total_customers' => Customer::where('is_active', true)->count(),
            'new_customers' => Customer::whereBetween('created_at', [$dateFrom, $dateTo])->count(),
            'active_customers' => Customer::whereHas('invoices', function($q) use ($dateFrom, $dateTo) {
                $q->whereBetween('invoice_date', [$dateFrom, $dateTo]);
            })->count(),
        ];

        // أفضل العملاء
        $topCustomers = Customer::withSum(['invoices as total_purchases' => function($q) use ($dateFrom, $dateTo) {
                $q->where('type', 'sale')
                  ->where('status', 'completed')
                  ->whereBetween('invoice_date', [$dateFrom, $dateTo]);
            }], 'total_amount')
            ->withCount(['invoices as total_invoices' => function($q) use ($dateFrom, $dateTo) {
                $q->where('type', 'sale')
                  ->whereBetween('invoice_date', [$dateFrom, $dateTo]);
            }])
            ->having('total_purchases', '>', 0)
            ->orderBy('total_purchases', 'desc')
            ->take(20)
            ->get();

        // العملاء الجدد
        $newCustomers = Customer::whereBetween('created_at', [$dateFrom, $dateTo])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        return view('reports.customers', compact(
            'tenant', 'customerStats', 'topCustomers', 'newCustomers', 'dateFrom', 'dateTo'
        ));
    }

    /**
     * تقرير المدفوعات
     */
    public function payments(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $dateFrom = $request->get('date_from', now()->startOfMonth());
        $dateTo = $request->get('date_to', now()->endOfMonth());

        // إحصائيات المدفوعات
        $paymentStats = [
            'total_payments' => Payment::whereBetween('payment_date', [$dateFrom, $dateTo])->sum('amount'),
            'cash_payments' => Payment::where('payment_method', 'cash')
                ->whereBetween('payment_date', [$dateFrom, $dateTo])
                ->sum('amount'),
            'card_payments' => Payment::where('payment_method', 'card')
                ->whereBetween('payment_date', [$dateFrom, $dateTo])
                ->sum('amount'),
            'pending_payments' => Invoice::where('payment_status', 'pending')->sum('remaining_amount'),
        ];

        // المدفوعات حسب الطريقة
        $paymentsByMethod = Payment::whereBetween('payment_date', [$dateFrom, $dateTo])
            ->selectRaw('payment_method, SUM(amount) as total')
            ->groupBy('payment_method')
            ->get();

        // المدفوعات اليومية
        $dailyPayments = Payment::whereBetween('payment_date', [$dateFrom, $dateTo])
            ->selectRaw('DATE(payment_date) as date, SUM(amount) as total')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('reports.payments', compact(
            'tenant', 'paymentStats', 'paymentsByMethod', 'dailyPayments', 'dateFrom', 'dateTo'
        ));
    }
}
