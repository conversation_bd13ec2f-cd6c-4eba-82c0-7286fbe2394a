@extends('layouts.super-admin')

@section('title', 'التقارير المالية')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-chart-bar me-2 text-warning"></i>
                التقارير المالية
            </h2>
            <p class="text-muted mb-0">تقارير مالية شاملة للنظام</p>
        </div>
        <div>
            <button class="btn btn-success" onclick="exportReport()">
                <i class="fas fa-download me-2"></i>
                تصدير التقرير
            </button>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ number_format(32968) }}</h3>
                            <p class="mb-0">الإيرادات الشهرية (ج.م)</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="opacity-75">
                            <i class="fas fa-arrow-up me-1"></i>
                            +12.5% من الشهر الماضي
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ number_format(395616) }}</h3>
                            <p class="mb-0">الإيرادات السنوية (ج.م)</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="opacity-75">
                            <i class="fas fa-arrow-up me-1"></i>
                            +8.3% من العام الماضي
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ number_format(599) }}</h3>
                            <p class="mb-0">متوسط الإيراد لكل مشترك</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-user-dollar"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="opacity-75">
                            <i class="fas fa-arrow-up me-1"></i>
                            +5.2% من الشهر الماضي
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ number_format(2.1) }}%</h3>
                            <p class="mb-0">معدل الإلغاء الشهري</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-percentage"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="opacity-75">
                            <i class="fas fa-arrow-down me-1"></i>
                            -0.3% من الشهر الماضي
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Revenue Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>
                        الإيرادات الشهرية
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Plan Distribution -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-pie-chart me-2"></i>
                        توزيع الإيرادات حسب الخطة
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="planRevenueChart" height="150"></canvas>
                    
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الخطة المتقدمة</span>
                            <strong>{{ number_format(19168) }} ج.م</strong>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الخطة الأساسية</span>
                            <strong>{{ number_format(4485) }} ج.م</strong>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>الخطة المؤسسية</span>
                            <strong>{{ number_format(7992) }} ج.م</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Financial Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        تفاصيل الإيرادات الشهرية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الشهر</th>
                                    <th>الخطة الأساسية</th>
                                    <th>الخطة المتقدمة</th>
                                    <th>الخطة المؤسسية</th>
                                    <th>الإجمالي</th>
                                    <th>النمو</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $months = [
                                        ['month' => 'يونيو 2025', 'basic' => 4485, 'premium' => 19168, 'enterprise' => 7992, 'growth' => 12.5],
                                        ['month' => 'مايو 2025', 'basic' => 4186, 'premium' => 17952, 'enterprise' => 7194, 'growth' => 8.7],
                                        ['month' => 'أبريل 2025', 'basic' => 3891, 'premium' => 16734, 'enterprise' => 6597, 'growth' => 5.2],
                                        ['month' => 'مارس 2025', 'basic' => 3597, 'premium' => 15516, 'enterprise' => 5999, 'growth' => 3.8],
                                        ['month' => 'فبراير 2025', 'basic' => 3298, 'premium' => 14298, 'enterprise' => 5399, 'growth' => 2.1],
                                        ['month' => 'يناير 2025', 'basic' => 2999, 'premium' => 13080, 'enterprise' => 4799, 'growth' => 0.0],
                                    ];
                                @endphp
                                
                                @foreach($months as $data)
                                @php
                                    $total = $data['basic'] + $data['premium'] + $data['enterprise'];
                                @endphp
                                <tr>
                                    <td><strong>{{ $data['month'] }}</strong></td>
                                    <td>{{ number_format($data['basic']) }} ج.م</td>
                                    <td>{{ number_format($data['premium']) }} ج.م</td>
                                    <td>{{ number_format($data['enterprise']) }} ج.م</td>
                                    <td><strong>{{ number_format($total) }} ج.م</strong></td>
                                    <td>
                                        @if($data['growth'] > 0)
                                            <span class="text-success">
                                                <i class="fas fa-arrow-up me-1"></i>
                                                +{{ $data['growth'] }}%
                                            </span>
                                        @elseif($data['growth'] < 0)
                                            <span class="text-danger">
                                                <i class="fas fa-arrow-down me-1"></i>
                                                {{ $data['growth'] }}%
                                            </span>
                                        @else
                                            <span class="text-muted">0%</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        المؤشرات الرئيسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <h4 class="text-success">97.9%</h4>
                            <p class="text-muted mb-0">معدل الاحتفاظ</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">4.2</h4>
                            <p class="text-muted mb-0">متوسط عمر المشترك (شهور)</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6 border-end">
                            <h4 class="text-warning">{{ number_format(2516) }}</h4>
                            <p class="text-muted mb-0">قيمة العميل مدى الحياة</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-primary">{{ number_format(125) }}</h4>
                            <p class="text-muted mb-0">تكلفة اكتساب العميل</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trending-up me-2"></i>
                        توقعات النمو
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>الإيرادات المتوقعة - يوليو 2025:</span>
                            <strong class="text-success">{{ number_format(37089) }} ج.م</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-success" style="width: 85%"></div>
                        </div>
                        <small class="text-muted">بناءً على النمو الحالي +12.5%</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>المشتركين الجدد المتوقعين:</span>
                            <strong class="text-info">+8 مشتركين</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-info" style="width: 70%"></div>
                        </div>
                        <small class="text-muted">بناءً على معدل النمو الشهري</small>
                    </div>
                    
                    <div>
                        <div class="d-flex justify-content-between">
                            <span>الإيرادات السنوية المتوقعة:</span>
                            <strong class="text-warning">{{ number_format(428427) }} ج.م</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-warning" style="width: 92%"></div>
                        </div>
                        <small class="text-muted">بناءً على الاتجاه الحالي</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'الإيرادات الشهرية (ج.م)',
            data: [20878, 22995, 25112, 27222, 29332, 31645],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ج.م';
                    }
                }
            }
        }
    }
});

// Plan Revenue Chart
const planCtx = document.getElementById('planRevenueChart').getContext('2d');
const planRevenueChart = new Chart(planCtx, {
    type: 'doughnut',
    data: {
        labels: ['الخطة المتقدمة', 'الخطة المؤسسية', 'الخطة الأساسية'],
        datasets: [{
            data: [19168, 7992, 4485],
            backgroundColor: [
                '#0d6efd',
                '#198754', 
                '#6c757d'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function exportReport() {
    alert('سيتم تصدير التقرير قريباً!');
}
</script>
@endpush
@endsection
