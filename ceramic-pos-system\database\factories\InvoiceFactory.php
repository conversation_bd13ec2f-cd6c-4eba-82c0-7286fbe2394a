<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Invoice>
 */
class InvoiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $invoiceTypes = ['sale', 'purchase'];
        $statuses = ['draft', 'confirmed', 'paid'];
        $paymentStatuses = ['unpaid', 'partial', 'paid'];
        $paymentMethods = ['cash', 'bank_transfer', 'check', 'credit'];

        $invoiceType = $this->faker->randomElement($invoiceTypes);
        $invoiceDate = $this->faker->dateTimeBetween('-6 months', 'now');
        $dueDate = $this->faker->dateTimeBetween($invoiceDate, '+30 days');

        $subtotal = $this->faker->randomFloat(2, 100, 5000);
        $discountAmount = $this->faker->randomFloat(2, 0, $subtotal * 0.1);
        $taxAmount = ($subtotal - $discountAmount) * 0.14; // 14% VAT
        $totalAmount = $subtotal - $discountAmount + $taxAmount;

        return [
            'tenant_id' => 1, // سيتم تحديثه عند الاستخدام
            'invoice_number' => $this->generateInvoiceNumber($invoiceType),
            'invoice_type' => $invoiceType,
            'invoice_date' => $invoiceDate,
            'due_date' => $dueDate,
            'customer_id' => $invoiceType === 'sale' ? 1 : null, // سيتم تحديثه
            'supplier_id' => $invoiceType === 'purchase' ? 1 : null, // سيتم تحديثه
            'customer_name' => $invoiceType === 'sale' ? $this->faker->name() : null,
            'customer_address' => $invoiceType === 'sale' ? $this->faker->address() : null,
            'customer_phone' => $invoiceType === 'sale' ? $this->faker->phoneNumber() : null,
            'warehouse_id' => 1, // سيتم تحديثه
            'status' => $this->faker->randomElement($statuses),
            'payment_status' => $this->faker->randomElement($paymentStatuses),
            'payment_method' => $this->faker->randomElement($paymentMethods),
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'discount_percentage' => $discountAmount > 0 ? ($discountAmount / $subtotal) * 100 : 0,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
            'paid_amount' => $this->faker->randomFloat(2, 0, $totalAmount),
            'remaining_amount' => $totalAmount, // سيتم حسابه
            'notes' => $this->faker->optional()->sentence(),
            'created_by' => 1, // سيتم تحديثه
            'created_at' => $invoiceDate,
            'updated_at' => $invoiceDate,
        ];
    }

    /**
     * إنشاء رقم فاتورة
     */
    private function generateInvoiceNumber(string $type): string
    {
        $prefix = $type === 'sale' ? 'SAL' : 'PUR';
        $year = date('Y');
        $month = date('m');
        $number = $this->faker->unique()->numberBetween(1, 9999);

        return sprintf('%s-%s%s%04d', $prefix, $year, $month, $number);
    }

    /**
     * فاتورة بيع
     */
    public function sale()
    {
        return $this->state(function (array $attributes) {
            return [
                'invoice_type' => 'sale',
                'invoice_number' => $this->generateInvoiceNumber('sale'),
                'supplier_id' => null,
                'customer_name' => $this->faker->name(),
                'customer_address' => $this->faker->address(),
                'customer_phone' => $this->faker->phoneNumber(),
            ];
        });
    }

    /**
     * فاتورة شراء
     */
    public function purchase()
    {
        return $this->state(function (array $attributes) {
            return [
                'invoice_type' => 'purchase',
                'invoice_number' => $this->generateInvoiceNumber('purchase'),
                'customer_id' => null,
                'customer_name' => null,
                'customer_address' => null,
                'customer_phone' => null,
            ];
        });
    }

    /**
     * فاتورة مؤكدة
     */
    public function confirmed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'confirmed_by' => 1,
            ];
        });
    }

    /**
     * فاتورة مدفوعة
     */
    public function paid()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'confirmed',
                'payment_status' => 'paid',
                'paid_amount' => $attributes['total_amount'],
                'remaining_amount' => 0,
                'confirmed_at' => now(),
                'confirmed_by' => 1,
            ];
        });
    }

    /**
     * فاتورة مسودة
     */
    public function draft()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'draft',
                'payment_status' => 'unpaid',
                'paid_amount' => 0,
                'remaining_amount' => $attributes['total_amount'],
                'confirmed_at' => null,
                'confirmed_by' => null,
            ];
        });
    }

    /**
     * فاتورة مستحقة
     */
    public function overdue()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'confirmed',
                'payment_status' => 'unpaid',
                'due_date' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
                'paid_amount' => 0,
                'remaining_amount' => $attributes['total_amount'],
                'confirmed_at' => now()->subDays(rand(1, 30)),
                'confirmed_by' => 1,
            ];
        });
    }
}
