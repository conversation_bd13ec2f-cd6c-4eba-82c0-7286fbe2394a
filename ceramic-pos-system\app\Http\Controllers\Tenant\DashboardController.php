<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\StockMovement;
use App\Models\Warehouse;
use App\Services\InvoiceService;
use App\Services\PaymentService;
use App\Services\InventoryService;
use App\Services\CustomerSupplierService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    protected $invoiceService;
    protected $paymentService;
    protected $inventoryService;
    protected $customerSupplierService;

    public function __construct(
        InvoiceService $invoiceService,
        PaymentService $paymentService,
        InventoryService $inventoryService,
        CustomerSupplierService $customerSupplierService
    ) {
        $this->invoiceService = $invoiceService;
        $this->paymentService = $paymentService;
        $this->inventoryService = $inventoryService;
        $this->customerSupplierService = $customerSupplierService;
    }

    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index(Request $request)
    {
        // الفترة الزمنية (افتراضي: الشهر الحالي)
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDate($period);
        $endDate = now();

        // الإحصائيات الأساسية
        $basicStats = $this->getBasicStats($startDate, $endDate);

        // إحصائيات المبيعات
        $salesStats = $this->getSalesStats($startDate, $endDate);

        // إحصائيات المشتريات
        $purchaseStats = $this->getPurchaseStats($startDate, $endDate);

        // إحصائيات المدفوعات
        $paymentStats = $this->getPaymentStats($startDate, $endDate);

        // إحصائيات المخزون
        $inventoryStats = $this->getInventoryStats();

        // إحصائيات العملاء والموردين
        $customerSupplierStats = $this->getCustomerSupplierStats();

        // الرسوم البيانية
        $charts = $this->getChartsData($period);

        // التنبيهات
        $alerts = $this->getAlerts();

        // الأنشطة الأخيرة
        $recentActivities = $this->getRecentActivities();

        return view('tenant.dashboard', compact(
            'basicStats',
            'salesStats',
            'purchaseStats',
            'paymentStats',
            'inventoryStats',
            'customerSupplierStats',
            'charts',
            'alerts',
            'recentActivities',
            'period'
        ));
    }

    /**
     * الحصول على الإحصائيات الأساسية
     */
    private function getBasicStats($startDate, $endDate): array
    {
        return [
            'total_sales' => Invoice::sales()
                                  ->confirmed()
                                  ->whereBetween('invoice_date', [$startDate, $endDate])
                                  ->sum('total_amount'),
            'total_purchases' => Invoice::purchases()
                                       ->confirmed()
                                       ->whereBetween('invoice_date', [$startDate, $endDate])
                                       ->sum('total_amount'),
            'total_received' => Payment::received()
                                      ->cleared()
                                      ->whereBetween('payment_date', [$startDate, $endDate])
                                      ->sum('amount'),
            'total_paid' => Payment::paid()
                                  ->cleared()
                                  ->whereBetween('payment_date', [$startDate, $endDate])
                                  ->sum('amount'),
            'net_profit' => $this->calculateNetProfit($startDate, $endDate),
            'total_customers' => Customer::count(),
            'total_suppliers' => Supplier::count(),
            'total_products' => Product::count(),
            'total_warehouses' => Warehouse::count(),
        ];
    }

    /**
     * الحصول على إحصائيات المبيعات
     */
    private function getSalesStats($startDate, $endDate): array
    {
        $salesInvoices = Invoice::sales()
                               ->confirmed()
                               ->whereBetween('invoice_date', [$startDate, $endDate]);

        return [
            'total_invoices' => $salesInvoices->count(),
            'total_amount' => $salesInvoices->sum('total_amount'),
            'average_invoice' => $salesInvoices->avg('total_amount') ?? 0,
            'paid_invoices' => $salesInvoices->where('payment_status', 'paid')->count(),
            'unpaid_invoices' => $salesInvoices->where('payment_status', 'unpaid')->count(),
            'overdue_invoices' => Invoice::sales()->overdue()->count(),
            'total_receivables' => Invoice::sales()
                                         ->confirmed()
                                         ->sum('remaining_amount'),
            'top_customers' => $this->getTopCustomers(5),
        ];
    }

    /**
     * الحصول على إحصائيات المشتريات
     */
    private function getPurchaseStats($startDate, $endDate): array
    {
        $purchaseInvoices = Invoice::purchases()
                                  ->confirmed()
                                  ->whereBetween('invoice_date', [$startDate, $endDate]);

        return [
            'total_invoices' => $purchaseInvoices->count(),
            'total_amount' => $purchaseInvoices->sum('total_amount'),
            'average_invoice' => $purchaseInvoices->avg('total_amount') ?? 0,
            'paid_invoices' => $purchaseInvoices->where('payment_status', 'paid')->count(),
            'unpaid_invoices' => $purchaseInvoices->where('payment_status', 'unpaid')->count(),
            'overdue_invoices' => Invoice::purchases()->overdue()->count(),
            'total_payables' => Invoice::purchases()
                                      ->confirmed()
                                      ->sum('remaining_amount'),
            'top_suppliers' => $this->getTopSuppliers(5),
        ];
    }

    /**
     * الحصول على إحصائيات المدفوعات
     */
    private function getPaymentStats($startDate, $endDate): array
    {
        return [
            'received_payments' => Payment::received()
                                         ->cleared()
                                         ->whereBetween('payment_date', [$startDate, $endDate])
                                         ->count(),
            'paid_payments' => Payment::paid()
                                     ->cleared()
                                     ->whereBetween('payment_date', [$startDate, $endDate])
                                     ->count(),
            'pending_received' => Payment::received()->pending()->count(),
            'pending_paid' => Payment::paid()->pending()->count(),
            'bounced_checks' => Payment::bounced()->count(),
            'net_cash_flow' => $this->calculateNetCashFlow($startDate, $endDate),
            'cash_payments' => Payment::where('payment_method', 'cash')
                                     ->cleared()
                                     ->whereBetween('payment_date', [$startDate, $endDate])
                                     ->sum('amount'),
            'bank_payments' => Payment::where('payment_method', 'bank_transfer')
                                     ->cleared()
                                     ->whereBetween('payment_date', [$startDate, $endDate])
                                     ->sum('amount'),
        ];
    }

    /**
     * الحصول على إحصائيات المخزون
     */
    private function getInventoryStats(): array
    {
        return [
            'total_products' => Product::count(),
            'active_products' => Product::active()->count(),
            'low_stock_products' => Product::lowStock()->count(),
            'out_of_stock_products' => Product::outOfStock()->count(),
            'total_stock_value' => ProductStock::get()->sum('stock_value'),
            'total_quantity' => ProductStock::sum('quantity'),
            'recent_movements' => StockMovement::orderBy('created_at', 'desc')->limit(5)->get(),
            'top_selling_products' => $this->getTopSellingProducts(5),
        ];
    }

    /**
     * الحصول على إحصائيات العملاء والموردين
     */
    private function getCustomerSupplierStats(): array
    {
        return [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::active()->count(),
            'customers_with_balance' => Customer::where('current_balance', '>', 0)->count(),
            'total_suppliers' => Supplier::count(),
            'active_suppliers' => Supplier::active()->count(),
            'suppliers_with_balance' => Supplier::where('current_balance', '>', 0)->count(),
            'new_customers_this_month' => Customer::whereMonth('created_at', now()->month)->count(),
            'new_suppliers_this_month' => Supplier::whereMonth('created_at', now()->month)->count(),
        ];
    }

    /**
     * الحصول على بيانات الرسوم البيانية
     */
    private function getChartsData($period): array
    {
        return [
            'sales_chart' => $this->getSalesChartData($period),
            'purchases_chart' => $this->getPurchasesChartData($period),
            'cash_flow_chart' => $this->getCashFlowChartData($period),
            'products_chart' => $this->getProductsChartData(),
            'customers_chart' => $this->getCustomersChartData(),
        ];
    }

    /**
     * الحصول على التنبيهات
     */
    private function getAlerts(): array
    {
        return [
            'low_stock_products' => Product::lowStock()->limit(5)->get(),
            'out_of_stock_products' => Product::outOfStock()->limit(5)->get(),
            'overdue_sales' => Invoice::sales()->overdue()->limit(5)->get(),
            'overdue_purchases' => Invoice::purchases()->overdue()->limit(5)->get(),
            'pending_payments' => Payment::pending()->limit(5)->get(),
            'due_checks' => Payment::where('payment_method', 'check')
                                  ->where('status', 'pending')
                                  ->where('check_date', '<=', now())
                                  ->limit(5)
                                  ->get(),
        ];
    }

    /**
     * الحصول على الأنشطة الأخيرة
     */
    private function getRecentActivities(): array
    {
        $recentInvoices = Invoice::with(['customer', 'supplier'])
                                ->orderBy('created_at', 'desc')
                                ->limit(5)
                                ->get();

        $recentPayments = Payment::with(['customer', 'supplier'])
                                ->orderBy('created_at', 'desc')
                                ->limit(5)
                                ->get();

        $recentStockMovements = StockMovement::with(['product', 'warehouse'])
                                            ->orderBy('created_at', 'desc')
                                            ->limit(5)
                                            ->get();

        return [
            'invoices' => $recentInvoices,
            'payments' => $recentPayments,
            'stock_movements' => $recentStockMovements,
        ];
    }

    /**
     * حساب صافي الربح
     */
    private function calculateNetProfit($startDate, $endDate): float
    {
        $totalSales = Invoice::sales()
                            ->confirmed()
                            ->whereBetween('invoice_date', [$startDate, $endDate])
                            ->sum('total_amount');

        $totalPurchases = Invoice::purchases()
                                ->confirmed()
                                ->whereBetween('invoice_date', [$startDate, $endDate])
                                ->sum('total_amount');

        return $totalSales - $totalPurchases;
    }

    /**
     * حساب صافي التدفق النقدي
     */
    private function calculateNetCashFlow($startDate, $endDate): float
    {
        $totalReceived = Payment::received()
                               ->cleared()
                               ->whereBetween('payment_date', [$startDate, $endDate])
                               ->sum('amount');

        $totalPaid = Payment::paid()
                           ->cleared()
                           ->whereBetween('payment_date', [$startDate, $endDate])
                           ->sum('amount');

        return $totalReceived - $totalPaid;
    }

    /**
     * الحصول على أفضل العملاء
     */
    private function getTopCustomers($limit = 5): array
    {
        return Customer::withSum(['salesInvoices' => function($query) {
                $query->where('status', 'confirmed');
            }], 'total_amount')
            ->orderBy('sales_invoices_sum_total_amount', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * الحصول على أفضل الموردين
     */
    private function getTopSuppliers($limit = 5): array
    {
        return Supplier::withSum(['purchaseInvoices' => function($query) {
                $query->where('status', 'confirmed');
            }], 'total_amount')
            ->orderBy('purchase_invoices_sum_total_amount', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * الحصول على أفضل المنتجات مبيعاً
     */
    private function getTopSellingProducts($limit = 5): array
    {
        return Product::withSum(['invoiceItems' => function($query) {
                $query->whereHas('invoice', function($invoiceQuery) {
                    $invoiceQuery->where('invoice_type', 'sale')
                                ->where('status', 'confirmed');
                });
            }], 'quantity')
            ->orderBy('invoice_items_sum_quantity', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * الحصول على تاريخ البداية حسب الفترة
     */
    private function getStartDate($period): Carbon
    {
        return match($period) {
            'today' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    /**
     * بيانات رسم المبيعات البياني
     */
    private function getSalesChartData($period): array
    {
        $startDate = $this->getStartDate($period);
        $groupBy = $this->getGroupByFormat($period);

        $salesData = Invoice::sales()
                           ->confirmed()
                           ->where('invoice_date', '>=', $startDate)
                           ->selectRaw("DATE_FORMAT(invoice_date, '{$groupBy}') as period, SUM(total_amount) as total")
                           ->groupBy('period')
                           ->orderBy('period')
                           ->get();

        return [
            'labels' => $salesData->pluck('period')->toArray(),
            'data' => $salesData->pluck('total')->toArray(),
        ];
    }

    /**
     * بيانات رسم المشتريات البياني
     */
    private function getPurchasesChartData($period): array
    {
        $startDate = $this->getStartDate($period);
        $groupBy = $this->getGroupByFormat($period);

        $purchasesData = Invoice::purchases()
                               ->confirmed()
                               ->where('invoice_date', '>=', $startDate)
                               ->selectRaw("DATE_FORMAT(invoice_date, '{$groupBy}') as period, SUM(total_amount) as total")
                               ->groupBy('period')
                               ->orderBy('period')
                               ->get();

        return [
            'labels' => $purchasesData->pluck('period')->toArray(),
            'data' => $purchasesData->pluck('total')->toArray(),
        ];
    }

    /**
     * بيانات رسم التدفق النقدي البياني
     */
    private function getCashFlowChartData($period): array
    {
        $startDate = $this->getStartDate($period);
        $groupBy = $this->getGroupByFormat($period);

        $receivedData = Payment::received()
                              ->cleared()
                              ->where('payment_date', '>=', $startDate)
                              ->selectRaw("DATE_FORMAT(payment_date, '{$groupBy}') as period, SUM(amount) as total")
                              ->groupBy('period')
                              ->orderBy('period')
                              ->get();

        $paidData = Payment::paid()
                          ->cleared()
                          ->where('payment_date', '>=', $startDate)
                          ->selectRaw("DATE_FORMAT(payment_date, '{$groupBy}') as period, SUM(amount) as total")
                          ->groupBy('period')
                          ->orderBy('period')
                          ->get();

        return [
            'labels' => $receivedData->pluck('period')->toArray(),
            'received' => $receivedData->pluck('total')->toArray(),
            'paid' => $paidData->pluck('total')->toArray(),
        ];
    }

    /**
     * بيانات رسم المنتجات البياني
     */
    private function getProductsChartData(): array
    {
        $productTypes = Product::selectRaw('product_type, COUNT(*) as count')
                              ->groupBy('product_type')
                              ->get();

        return [
            'labels' => $productTypes->pluck('product_type')->toArray(),
            'data' => $productTypes->pluck('count')->toArray(),
        ];
    }

    /**
     * بيانات رسم العملاء البياني
     */
    private function getCustomersChartData(): array
    {
        $customerTypes = Customer::selectRaw('customer_type, COUNT(*) as count')
                                ->groupBy('customer_type')
                                ->get();

        return [
            'labels' => $customerTypes->pluck('customer_type')->toArray(),
            'data' => $customerTypes->pluck('count')->toArray(),
        ];
    }

    /**
     * الحصول على تنسيق التجميع حسب الفترة
     */
    private function getGroupByFormat($period): string
    {
        return match($period) {
            'today' => '%H:00',
            'week' => '%Y-%m-%d',
            'month' => '%Y-%m-%d',
            'quarter' => '%Y-%m',
            'year' => '%Y-%m',
            default => '%Y-%m-%d',
        };
    }

    /**
     * API للحصول على إحصائيات سريعة
     */
    public function quickStats(Request $request)
    {
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDate($period);
        $endDate = now();

        $stats = [
            'sales' => Invoice::sales()
                             ->confirmed()
                             ->whereBetween('invoice_date', [$startDate, $endDate])
                             ->sum('total_amount'),
            'purchases' => Invoice::purchases()
                                 ->confirmed()
                                 ->whereBetween('invoice_date', [$startDate, $endDate])
                                 ->sum('total_amount'),
            'received' => Payment::received()
                                ->cleared()
                                ->whereBetween('payment_date', [$startDate, $endDate])
                                ->sum('amount'),
            'paid' => Payment::paid()
                            ->cleared()
                            ->whereBetween('payment_date', [$startDate, $endDate])
                            ->sum('amount'),
        ];

        return response()->json($stats);
    }

    /**
     * API للحصول على بيانات الرسم البياني
     */
    public function chartData(Request $request)
    {
        $type = $request->get('type', 'sales');
        $period = $request->get('period', 'month');

        $data = match($type) {
            'sales' => $this->getSalesChartData($period),
            'purchases' => $this->getPurchasesChartData($period),
            'cash_flow' => $this->getCashFlowChartData($period),
            'products' => $this->getProductsChartData(),
            'customers' => $this->getCustomersChartData(),
            default => [],
        };

        return response()->json($data);
    }

    /**
     * عرض معلومات الشركة
     */
    public function companyInfo()
    {
        $tenant = tenant();
        return view('tenant.company-info', compact('tenant'));
    }
}
