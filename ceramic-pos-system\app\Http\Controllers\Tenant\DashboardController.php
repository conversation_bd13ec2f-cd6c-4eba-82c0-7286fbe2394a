<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\ProductStock;

class DashboardController extends Controller
{
    /**
     * عرض لوحة التحكم الرئيسية للشركة
     */
    public function index()
    {
        $tenant = tenant();

        // إحصائيات سريعة
        $stats = [
            'customers_count' => Customer::count(),
            'suppliers_count' => Supplier::count(),
            'products_count' => Product::count(),
            'low_stock_products' => Product::whereHas('stock', function($query) {
                $query->whereRaw('quantity <= minimum_stock');
            })->count(),
        ];

        // المنتجات منخفضة المخزون
        $lowStockProducts = Product::with('stock')
            ->whereHas('stock', function($query) {
                $query->whereRaw('quantity <= minimum_stock');
            })
            ->limit(5)
            ->get();

        return view('tenant.dashboard', compact('tenant', 'stats', 'lowStockProducts'));
    }

    /**
     * عرض معلومات الشركة
     */
    public function companyInfo()
    {
        $tenant = tenant();
        return view('tenant.company-info', compact('tenant'));
    }
}
