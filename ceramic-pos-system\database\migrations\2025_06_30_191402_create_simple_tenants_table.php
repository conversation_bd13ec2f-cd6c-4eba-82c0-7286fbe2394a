<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('simple_tenants', function (Blueprint $table) {
            $table->id();
            $table->string('company_name');
            $table->string('subdomain')->unique();
            $table->string('owner_name');
            $table->string('email')->unique();
            $table->string('phone');
            $table->string('city');
            $table->enum('plan', ['basic', 'premium', 'enterprise'])->default('premium');
            $table->enum('status', ['trial', 'active', 'suspended', 'cancelled'])->default('trial');
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('subscription_ends_at')->nullable();
            $table->decimal('monthly_price', 8, 2)->default(0);
            $table->json('settings')->nullable();
            $table->timestamps();

            $table->index(['subdomain', 'status']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('simple_tenants');
    }
};
