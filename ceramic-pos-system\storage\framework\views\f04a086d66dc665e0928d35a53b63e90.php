<?php $__env->startSection('title', 'إدارة محتوى الصفحة الرئيسية'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-home me-2 text-primary"></i>
                إدارة محتوى الصفحة الرئيسية
            </h2>
            <p class="text-muted mb-0">تحكم في محتوى وإعدادات الصفحة الرئيسية</p>
        </div>
        <div>
            <a href="<?php echo e(route('home')); ?>" target="_blank" class="btn btn-outline-success">
                <i class="fas fa-external-link-alt me-2"></i>
                معاينة الصفحة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="<?php echo e(route('super-admin.landing-page.update')); ?>">
                <?php echo csrf_field(); ?>
                
                <!-- Hero Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>
                            القسم الرئيسي (Hero Section)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">العنوان الرئيسي</label>
                            <input type="text" name="hero_title" class="form-control" 
                                   value="<?php echo e($settings['hero_title']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">العنوان الفرعي</label>
                            <input type="text" name="hero_subtitle" class="form-control" 
                                   value="<?php echo e($settings['hero_subtitle']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea name="hero_description" class="form-control" rows="4" required><?php echo e($settings['hero_description']); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Features Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list-ul me-2"></i>
                            قسم المميزات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">عنوان قسم المميزات</label>
                            <input type="text" name="features_title" class="form-control" 
                                   value="<?php echo e($settings['features_title']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">وصف قسم المميزات</label>
                            <input type="text" name="features_subtitle" class="form-control" 
                                   value="<?php echo e($settings['features_subtitle']); ?>" required>
                        </div>
                    </div>
                </div>

                <!-- Pricing Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>
                            قسم الأسعار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">عنوان قسم الأسعار</label>
                            <input type="text" name="pricing_title" class="form-control" 
                                   value="<?php echo e($settings['pricing_title']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">وصف قسم الأسعار</label>
                            <input type="text" name="pricing_subtitle" class="form-control" 
                                   value="<?php echo e($settings['pricing_subtitle']); ?>" required>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-address-book me-2"></i>
                            معلومات التواصل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" name="contact_email" class="form-control" 
                                           value="<?php echo e($settings['contact_email']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" name="contact_phone" class="form-control" 
                                           value="<?php echo e($settings['contact_phone']); ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <input type="text" name="contact_address" class="form-control" 
                                   value="<?php echo e($settings['contact_address']); ?>" required>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>
                            وسائل التواصل الاجتماعي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">فيسبوك</label>
                                    <input type="url" name="social_facebook" class="form-control" 
                                           value="<?php echo e($settings['social_facebook']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تويتر</label>
                                    <input type="url" name="social_twitter" class="form-control" 
                                           value="<?php echo e($settings['social_twitter']); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">لينكد إن</label>
                                    <input type="url" name="social_linkedin" class="form-control" 
                                           value="<?php echo e($settings['social_linkedin']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إنستغرام</label>
                                    <input type="url" name="social_instagram" class="form-control" 
                                           value="<?php echo e($settings['social_instagram']); ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-end">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card sticky-top">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        معاينة سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="preview-section mb-4">
                        <h6 class="fw-bold text-primary">القسم الرئيسي:</h6>
                        <div class="bg-light p-3 rounded">
                            <h5 class="preview-title"><?php echo e($settings['hero_title']); ?></h5>
                            <p class="preview-subtitle text-muted"><?php echo e($settings['hero_subtitle']); ?></p>
                            <small class="preview-description"><?php echo e(Str::limit($settings['hero_description'], 100)); ?></small>
                        </div>
                    </div>

                    <div class="preview-section mb-4">
                        <h6 class="fw-bold text-primary">معلومات التواصل:</h6>
                        <div class="bg-light p-3 rounded">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <small><?php echo e($settings['contact_email']); ?></small>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone text-primary me-2"></i>
                                <small><?php echo e($settings['contact_phone']); ?></small>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <small><?php echo e($settings['contact_address']); ?></small>
                            </div>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h6 class="fw-bold text-primary">وسائل التواصل:</h6>
                        <div class="bg-light p-3 rounded">
                            <div class="d-flex gap-2">
                                <?php if($settings['social_facebook']): ?>
                                    <i class="fab fa-facebook text-primary"></i>
                                <?php endif; ?>
                                <?php if($settings['social_twitter']): ?>
                                    <i class="fab fa-twitter text-info"></i>
                                <?php endif; ?>
                                <?php if($settings['social_linkedin']): ?>
                                    <i class="fab fa-linkedin text-primary"></i>
                                <?php endif; ?>
                                <?php if($settings['social_instagram']): ?>
                                    <i class="fab fa-instagram text-danger"></i>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <div class="d-grid">
                        <a href="<?php echo e(route('home')); ?>" target="_blank" class="btn btn-outline-success">
                            <i class="fas fa-external-link-alt me-2"></i>
                            معاينة كاملة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.preview-section {
    border-left: 3px solid #0d6efd;
    padding-left: 10px;
}

.preview-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.preview-subtitle {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.preview-description {
    font-size: 0.8rem;
    color: #666;
}

.sticky-top {
    top: 20px;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.super-admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Work\ceramic pos\ceramic-pos-system\resources\views/super-admin/landing-page.blade.php ENDPATH**/ ?>