<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\Warehouse;
use App\Models\ProductStock;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * عرض قائمة المنتجات
     */
    public function index(Request $request)
    {
        $query = Product::with(['mainSupplier', 'stock.warehouse']);

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // التصفية حسب النوع
        if ($request->filled('product_type')) {
            $query->ofType($request->product_type);
        }

        // التصفية حسب الفئة
        if ($request->filled('category')) {
            $query->ofCategory($request->category);
        }

        // التصفية حسب العلامة التجارية
        if ($request->filled('brand')) {
            $query->ofBrand($request->brand);
        }

        // التصفية حسب الحالة
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // التصفية حسب حالة المخزون
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low_stock':
                    $query->lowStock();
                    break;
                case 'out_of_stock':
                    $query->outOfStock();
                    break;
            }
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'product_code');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $products = $query->paginate(20);

        // إحصائيات
        $stats = [
            'total_products' => Product::count(),
            'active_products' => Product::active()->count(),
            'ceramic_products' => Product::ofType('ceramic')->count(),
            'sanitary_products' => Product::ofType('sanitary')->count(),
            'low_stock_products' => Product::lowStock()->count(),
            'out_of_stock_products' => Product::outOfStock()->count(),
            'total_stock_value' => ProductStock::get()->sum('stock_value'),
        ];

        return view('tenant.products.index', compact('products', 'stats'));
    }

    /**
     * عرض نموذج إنشاء منتج جديد
     */
    public function create()
    {
        $suppliers = Supplier::active()->orderBy('name')->get();
        $warehouses = Warehouse::active()->orderBy('name')->get();

        return view('tenant.products.create', compact('suppliers', 'warehouses'));
    }

    /**
     * حفظ منتج جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'product_code' => 'required|string|max:50|unique:products,product_code',
            'barcode' => 'nullable|string|max:50|unique:products,barcode',
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'product_type' => 'required|in:' . implode(',', array_keys(Product::PRODUCT_TYPES)),
            'category' => 'nullable|string|max:100',
            'brand' => 'nullable|string|max:100',
            'model' => 'nullable|string|max:100',
            'size' => 'nullable|string|max:50',
            'color' => 'nullable|string|max:50',
            'material' => 'nullable|string|max:100',
            'origin_country' => 'nullable|string|max:50',
            'unit' => 'required|in:' . implode(',', array_keys(Product::UNITS)),
            'purchase_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'minimum_price' => 'nullable|numeric|min:0',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:100',
            'minimum_stock' => 'required|integer|min:0',
            'maximum_stock' => 'nullable|integer|min:0',
            'reorder_level' => 'nullable|integer|min:0',
            'track_quantity' => 'boolean',
            'main_supplier_id' => 'nullable|exists:suppliers,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'notes' => 'nullable|string',
        ], [
            'product_code.required' => 'رقم المنتج مطلوب',
            'product_code.unique' => 'رقم المنتج مستخدم من قبل',
            'barcode.unique' => 'الباركود مستخدم من قبل',
            'name.required' => 'اسم المنتج مطلوب',
            'product_type.required' => 'نوع المنتج مطلوب',
            'unit.required' => 'وحدة القياس مطلوبة',
            'purchase_price.required' => 'سعر الشراء مطلوب',
            'selling_price.required' => 'سعر البيع مطلوب',
            'minimum_stock.required' => 'الحد الأدنى للمخزون مطلوب',
        ]);

        DB::beginTransaction();

        try {
            // رفع الصورة إن وجدت
            $imagePath = null;
            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('products', 'public');
            }

            $product = Product::create([
                'product_code' => $request->product_code,
                'barcode' => $request->barcode,
                'name' => $request->name,
                'name_en' => $request->name_en,
                'description' => $request->description,
                'product_type' => $request->product_type,
                'category' => $request->category,
                'brand' => $request->brand,
                'model' => $request->model,
                'size' => $request->size,
                'color' => $request->color,
                'material' => $request->material,
                'origin_country' => $request->origin_country,
                'unit' => $request->unit,
                'purchase_price' => $request->purchase_price,
                'selling_price' => $request->selling_price,
                'wholesale_price' => $request->wholesale_price,
                'minimum_price' => $request->minimum_price,
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
                'minimum_stock' => $request->minimum_stock,
                'maximum_stock' => $request->maximum_stock,
                'reorder_level' => $request->reorder_level,
                'track_quantity' => $request->boolean('track_quantity', true),
                'main_supplier_id' => $request->main_supplier_id,
                'image' => $imagePath,
                'notes' => $request->notes,
                'is_active' => true,
            ]);

            // إنشاء مخزون أولي في جميع المخازن النشطة
            $warehouses = Warehouse::active()->get();
            foreach ($warehouses as $warehouse) {
                ProductStock::create([
                    'product_id' => $product->id,
                    'warehouse_id' => $warehouse->id,
                    'quantity' => 0,
                    'reserved_quantity' => 0,
                    'available_quantity' => 0,
                    'average_cost' => $request->purchase_price,
                ]);
            }

            DB::commit();

            return redirect()->route('tenant.products.index')
                ->with('success', 'تم إنشاء المنتج بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            // حذف الصورة في حالة الخطأ
            if ($imagePath) {
                Storage::disk('public')->delete($imagePath);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المنتج: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل المنتج
     */
    public function show(Product $product)
    {
        $product->load(['mainSupplier', 'stock.warehouse']);

        // إحصائيات المنتج
        $stats = [
            'total_quantity' => $product->total_quantity,
            'available_quantity' => $product->available_quantity,
            'reserved_quantity' => $product->reserved_quantity,
            'average_cost' => $product->average_cost,
            'stock_value' => $product->total_quantity * $product->average_cost,
            'profit_margin' => $product->selling_price > 0 ?
                (($product->selling_price - $product->average_cost) / $product->selling_price) * 100 : 0,
        ];

        return view('tenant.products.show', compact('product', 'stats'));
    }

    /**
     * عرض نموذج تعديل المنتج
     */
    public function edit(Product $product)
    {
        $suppliers = Supplier::active()->orderBy('name')->get();
        $warehouses = Warehouse::active()->orderBy('name')->get();

        return view('tenant.products.edit', compact('product', 'suppliers', 'warehouses'));
    }

    /**
     * تحديث المنتج
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'product_code' => 'required|string|max:50|unique:products,product_code,' . $product->id,
            'barcode' => 'nullable|string|max:50|unique:products,barcode,' . $product->id,
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'product_type' => 'required|in:' . implode(',', array_keys(Product::PRODUCT_TYPES)),
            'category' => 'nullable|string|max:100',
            'brand' => 'nullable|string|max:100',
            'model' => 'nullable|string|max:100',
            'size' => 'nullable|string|max:50',
            'color' => 'nullable|string|max:50',
            'material' => 'nullable|string|max:100',
            'origin_country' => 'nullable|string|max:50',
            'unit' => 'required|in:' . implode(',', array_keys(Product::UNITS)),
            'purchase_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'minimum_price' => 'nullable|numeric|min:0',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:100',
            'minimum_stock' => 'required|integer|min:0',
            'maximum_stock' => 'nullable|integer|min:0',
            'reorder_level' => 'nullable|integer|min:0',
            'track_quantity' => 'boolean',
            'main_supplier_id' => 'nullable|exists:suppliers,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        try {
            // رفع الصورة الجديدة إن وجدت
            $imagePath = $product->image;
            if ($request->hasFile('image')) {
                // حذف الصورة القديمة
                if ($product->image) {
                    Storage::disk('public')->delete($product->image);
                }
                $imagePath = $request->file('image')->store('products', 'public');
            }

            $product->update([
                'product_code' => $request->product_code,
                'barcode' => $request->barcode,
                'name' => $request->name,
                'name_en' => $request->name_en,
                'description' => $request->description,
                'product_type' => $request->product_type,
                'category' => $request->category,
                'brand' => $request->brand,
                'model' => $request->model,
                'size' => $request->size,
                'color' => $request->color,
                'material' => $request->material,
                'origin_country' => $request->origin_country,
                'unit' => $request->unit,
                'purchase_price' => $request->purchase_price,
                'selling_price' => $request->selling_price,
                'wholesale_price' => $request->wholesale_price,
                'minimum_price' => $request->minimum_price,
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
                'minimum_stock' => $request->minimum_stock,
                'maximum_stock' => $request->maximum_stock,
                'reorder_level' => $request->reorder_level,
                'track_quantity' => $request->boolean('track_quantity', true),
                'main_supplier_id' => $request->main_supplier_id,
                'image' => $imagePath,
                'notes' => $request->notes,
                'is_active' => $request->boolean('is_active', true),
            ]);

            return redirect()->route('tenant.products.index')
                ->with('success', 'تم تحديث المنتج بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث المنتج: ' . $e->getMessage());
        }
    }

    /**
     * حذف المنتج
     */
    public function destroy(Product $product)
    {
        // التحقق من عدم وجود مخزون أو حركات مالية
        if ($product->total_quantity > 0) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف منتج له مخزون متاح');
        }

        // TODO: التحقق من عدم وجود فواتير أو حركات مالية

        try {
            // حذف الصورة
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }

            // حذف مخزون المنتج
            $product->stock()->delete();

            // حذف المنتج
            $product->delete();

            return redirect()->route('tenant.products.index')
                ->with('success', 'تم حذف المنتج بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف المنتج: ' . $e->getMessage());
        }
    }

    /**
     * تفعيل/إلغاء تفعيل المنتج
     */
    public function toggleStatus(Product $product)
    {
        $product->update(['is_active' => !$product->is_active]);

        return response()->json([
            'success' => true,
            'message' => $product->is_active ? 'تم تفعيل المنتج' : 'تم إلغاء تفعيل المنتج',
            'is_active' => $product->is_active
        ]);
    }

    /**
     * البحث في المنتجات (للـ API)
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $products = Product::search($query)
            ->active()
            ->with('stock')
            ->limit(10)
            ->get(['id', 'product_code', 'name', 'selling_price', 'unit']);

        return response()->json($products->map(function ($product) {
            return [
                'id' => $product->id,
                'code' => $product->product_code,
                'name' => $product->name,
                'price' => $product->selling_price,
                'unit' => $product->unit_name,
                'total_quantity' => $product->total_quantity,
                'available_quantity' => $product->available_quantity,
                'stock_status' => $product->stock_status_name,
            ];
        }));
    }

    /**
     * تقرير المنتجات
     */
    public function report(Request $request)
    {
        $query = Product::with(['mainSupplier', 'stock']);

        // التصفية حسب النوع
        if ($request->filled('product_type')) {
            $query->ofType($request->product_type);
        }

        // التصفية حسب الفئة
        if ($request->filled('category')) {
            $query->ofCategory($request->category);
        }

        // التصفية حسب حالة المخزون
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low_stock':
                    $query->lowStock();
                    break;
                case 'out_of_stock':
                    $query->outOfStock();
                    break;
            }
        }

        $products = $query->orderBy('product_code')->get();

        return view('tenant.products.report', compact('products'));
    }
}
