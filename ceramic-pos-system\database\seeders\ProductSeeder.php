<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء فئات المنتجات
        $categories = [
            ['name' => 'سيراميك أرضيات', 'description' => 'سيراميك للأرضيات بأحجام مختلفة'],
            ['name' => 'سيراميك حوائط', 'description' => 'سيراميك للحوائط والجدران'],
            ['name' => 'أدوات صحية', 'description' => 'مراحيض وأحواض وخلاطات'],
            ['name' => 'إكسسوارات', 'description' => 'إكسسوارات الحمامات والمطابخ'],
            ['name' => 'بورسلين', 'description' => 'بورسلين فاخر للأرضيات والحوائط'],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(['name' => $categoryData['name']], $categoryData);
        }

        // إنشاء منتجات بسيطة
        $products = [
            [
                'name' => 'سيراميك أرضيات 60×60 رمادي',
                'product_code' => 'FLR001',
                'barcode' => '1234567890001',
                'purchase_price' => 45,
                'selling_price' => 65,
                'unit' => 'متر مربع',
                'category' => 'سيراميك أرضيات',
                'description' => 'سيراميك عالي الجودة مقاوم للخدش والبقع',
                'minimum_stock' => 20,
                'is_active' => true,
                'track_quantity' => true,
            ],
            [
                'name' => 'سيراميك حوائط 25×40 أزرق',
                'product_code' => 'WAL001',
                'barcode' => '1234567890011',
                'purchase_price' => 20,
                'selling_price' => 35,
                'unit' => 'متر مربع',
                'category' => 'سيراميك حوائط',
                'description' => 'سيراميك حوائط مقاوم للرطوبة',
                'minimum_stock' => 30,
                'is_active' => true,
                'track_quantity' => true,
            ],
            [
                'name' => 'مرحاض إفرنجي أبيض',
                'product_code' => 'SAN001',
                'barcode' => '1234567890021',
                'purchase_price' => 800,
                'selling_price' => 1200,
                'unit' => 'قطعة',
                'category' => 'أدوات صحية',
                'description' => 'مرحاض إفرنجي عالي الجودة',
                'minimum_stock' => 5,
                'is_active' => true,
                'track_quantity' => true,
            ],
            [
                'name' => 'خلاط مياه للحوض',
                'product_code' => 'SAN003',
                'barcode' => '1234567890023',
                'purchase_price' => 150,
                'selling_price' => 250,
                'unit' => 'قطعة',
                'category' => 'أدوات صحية',
                'description' => 'خلاط مياه نحاس مطلي كروم',
                'minimum_stock' => 10,
                'is_active' => true,
                'track_quantity' => true,
            ],
            [
                'name' => 'بورسلين 60×120 رخام كرارا',
                'product_code' => 'POR001',
                'barcode' => '1234567890041',
                'purchase_price' => 120,
                'selling_price' => 180,
                'unit' => 'متر مربع',
                'category' => 'بورسلين',
                'description' => 'بورسلين فاخر بتصميم رخام كرارا',
                'minimum_stock' => 15,
                'is_active' => true,
                'track_quantity' => true,
            ],
        ];

        foreach ($products as $productData) {
            Product::create(array_merge($productData, ['tenant_id' => 1]));
        }

        $this->command->info('تم إنشاء ' . Product::count() . ' منتج بنجاح!');
        $this->command->info('تم إنشاء ' . Category::count() . ' فئة بنجاح!');
    }
}
