[2025-06-30 11:43:04] local.ERROR: There are no commands defined in the "breeze" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"breeze\" namespace. at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('breeze')
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('breeze:install')
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-06-30 11:59:40] local.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR, expecting T_VARIABLE on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR, expecting T_VARIABLE on line 1 at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo '\\xD8\\xA7\\xD9...', false)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...', true)
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...', true)
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...')
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-06-30 12:00:29] local.ERROR: The "intl" PHP extension is required to use the [format] method. {"exception":"[object] (RuntimeException(code: 0): The \"intl\" PHP extension is required to use the [format] method. at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Number.php:427)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Number.php(38): Illuminate\\Support\\Number::ensureIntlExtensionIsInstalled()
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(194): Illuminate\\Support\\Number::format(0)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(271): Illuminate\\Database\\Console\\ShowCommand->Illuminate\\Database\\Console\\{closure}(Array, 0)
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(189): Illuminate\\Support\\Collection->each(Object(Closure))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(136): Illuminate\\Database\\Console\\ShowCommand->displayForCli(Array)
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(65): Illuminate\\Database\\Console\\ShowCommand->display(Array)
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\ShowCommand->handle(Object(Illuminate\\Database\\DatabaseManager))
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
