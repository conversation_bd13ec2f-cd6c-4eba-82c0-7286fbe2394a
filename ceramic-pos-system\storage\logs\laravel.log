[2025-06-30 11:43:04] local.ERROR: There are no commands defined in the "breeze" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"breeze\" namespace. at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php:659)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(708): Symfony\\Component\\Console\\Application->findNamespace('breeze')
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('breeze:install')
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-06-30 11:59:40] local.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR, expecting T_VARIABLE on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR, expecting T_VARIABLE on line 1 at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo '\\xD8\\xA7\\xD9...', false)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...', true)
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...', true)
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...')
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-06-30 12:00:29] local.ERROR: The "intl" PHP extension is required to use the [format] method. {"exception":"[object] (RuntimeException(code: 0): The \"intl\" PHP extension is required to use the [format] method. at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Number.php:427)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Number.php(38): Illuminate\\Support\\Number::ensureIntlExtensionIsInstalled()
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(194): Illuminate\\Support\\Number::format(0)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(271): Illuminate\\Database\\Console\\ShowCommand->Illuminate\\Database\\Console\\{closure}(Array, 0)
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(189): Illuminate\\Support\\Collection->each(Object(Closure))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(136): Illuminate\\Database\\Console\\ShowCommand->displayForCli(Array)
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(65): Illuminate\\Database\\Console\\ShowCommand->display(Array)
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\ShowCommand->handle(Object(Illuminate\\Database\\DatabaseManager))
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-06-30 15:04:45] local.ERROR: SQLSTATE[HY000] [1049] Unknown database 'ceramic_pos' (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'ceramic_pos' (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(67): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\FreshCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'ceramic_pos' at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(67): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\FreshCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}
"} 
[2025-06-30 15:05:52] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'performance_schema.session_status' doesn't exist (Connection: mysql, SQL: select variable_value as `Value` from performance_schema.session_status where variable_name = 'threads_connected') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'performance_schema.session_status' doesn't exist (Connection: mysql, SQL: select variable_value as `Value` from performance_schema.session_status where variable_name = 'threads_connected') at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select variable...', Array, Object(Closure))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select variable...', Array, Object(Closure))
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select variable...', Array, true)
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select variable...', Array, true)
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(633): Illuminate\\Database\\Connection->scalar('select variable...')
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(52): Illuminate\\Database\\Connection->threadCount()
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\ShowCommand->handle(Object(Illuminate\\Database\\DatabaseManager))
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'performance_schema.session_status' doesn't exist at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select variable...')
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select variable...', Array)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select variable...', Array, Object(Closure))
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select variable...', Array, Object(Closure))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select variable...', Array, true)
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select variable...', Array, true)
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(633): Illuminate\\Database\\Connection->scalar('select variable...')
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(52): Illuminate\\Database\\Connection->threadCount()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\ShowCommand->handle(Object(Illuminate\\Database\\DatabaseManager))
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-06-30 15:06:05] local.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo '\\xD8\\xA7\\xD9...', false)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...', true)
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...', true)
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo '\\xD8\\xA7\\xD9\\x84\\xD8\\xB9\\xD9\\x85\\xD9...')
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-06-30 15:14:46] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'tenants' already exists (Connection: mysql, SQL: create table `tenants` (`id` varchar(255) not null, `created_at` timestamp null, `updated_at` timestamp null, `data` json null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'tenants' already exists (Connection: mysql, SQL: create table `tenants` (`id` varchar(255) not null, `created_at` timestamp null, `updated_at` timestamp null, `data` json null, primary key (`id`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `t...', Array, Object(Closure))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `t...', Array, Object(Closure))
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `t...')
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('tenants', Object(Closure))
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2019_09_15_000010_create_tenants_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateTenantsTable->up()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateTenantsTable), 'up')
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateTenantsTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2019_09_15_0000...', Object(Closure))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2019_09_15_0000...', Object(Closure))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 2, false)
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'tenants' already exists at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `t...', Array)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `t...', Array, Object(Closure))
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `t...', Array, Object(Closure))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `t...')
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('tenants', Object(Closure))
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2019_09_15_000010_create_tenants_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateTenantsTable->up()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateTenantsTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateTenantsTable), 'up')
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2019_09_15_0000...', Object(Closure))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2019_09_15_0000...', Object(Closure))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 2, false)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-30 15:15:21] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `ceramic_pos`.`domains` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `domains` add constraint `domains_tenant_id_foreign` foreign key (`tenant_id`) references `tenants` (`id`) on delete cascade on update cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `ceramic_pos`.`domains` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `domains` add constraint `domains_tenant_id_foreign` foreign key (`tenant_id`) references `tenants` (`id`) on delete cascade on update cascade) at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `do...', Array, Object(Closure))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `do...', Array, Object(Closure))
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `do...')
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('domains', Object(Closure))
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2019_09_15_000020_create_domains_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateDomainsTable->up()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateDomainsTable), 'up')
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateDomainsTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2019_09_15_0000...', Object(Closure))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2019_09_15_0000...', Object(Closure))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 2, false)
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `ceramic_pos`.`domains` (errno: 150 \"Foreign key constraint is incorrectly formed\") at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `do...', Array)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `do...', Array, Object(Closure))
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `do...', Array, Object(Closure))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `do...')
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('domains', Object(Closure))
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2019_09_15_000020_create_domains_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateDomainsTable->up()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateDomainsTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateDomainsTable), 'up')
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2019_09_15_0000...', Object(Closure))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2019_09_15_0000...', Object(Closure))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 2, false)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-30 15:15:58] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'domains' already exists (Connection: mysql, SQL: create table `domains` (`id` int unsigned not null auto_increment primary key, `domain` varchar(255) not null, `tenant_id` bigint unsigned not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'domains' already exists (Connection: mysql, SQL: create table `domains` (`id` int unsigned not null auto_increment primary key, `domain` varchar(255) not null, `tenant_id` bigint unsigned not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `d...', Array, Object(Closure))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `d...', Array, Object(Closure))
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `d...')
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('domains', Object(Closure))
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2019_09_15_000020_create_domains_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateDomainsTable->up()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateDomainsTable), 'up')
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateDomainsTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2019_09_15_0000...', Object(Closure))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2019_09_15_0000...', Object(Closure))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 2, false)
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'domains' already exists at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `d...', Array)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `d...', Array, Object(Closure))
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `d...', Array, Object(Closure))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `d...')
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('domains', Object(Closure))
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2019_09_15_000020_create_domains_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateDomainsTable->up()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateDomainsTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateDomainsTable), 'up')
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2019_09_15_0000...', Object(Closure))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2019_09_15_0000...', Object(Closure))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 2, false)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-30 15:16:16] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `ceramic_pos`.`tenant_user_impersonation_tokens` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `tenant_user_impersonation_tokens` add constraint `tenant_user_impersonation_tokens_tenant_id_foreign` foreign key (`tenant_id`) references `tenants` (`id`) on delete cascade on update cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `ceramic_pos`.`tenant_user_impersonation_tokens` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `tenant_user_impersonation_tokens` add constraint `tenant_user_impersonation_tokens_tenant_id_foreign` foreign key (`tenant_id`) references `tenants` (`id`) on delete cascade on update cascade) at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `te...', Array, Object(Closure))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `te...', Array, Object(Closure))
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `te...')
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('tenant_user_imp...', Object(Closure))
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2020_05_15_000010_create_tenant_user_impersonation_tokens_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateTenantUserImpersonationTokensTable->up()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateTenantUserImpersonationTokensTable), 'up')
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateTenantUserImpersonationTokensTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2020_05_15_0000...', Object(Closure))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2020_05_15_0000...', Object(Closure))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 2, false)
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `ceramic_pos`.`tenant_user_impersonation_tokens` (errno: 150 \"Foreign key constraint is incorrectly formed\") at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `te...', Array)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `te...', Array, Object(Closure))
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `te...', Array, Object(Closure))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `te...')
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('tenant_user_imp...', Object(Closure))
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2020_05_15_000010_create_tenant_user_impersonation_tokens_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateTenantUserImpersonationTokensTable->up()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateTenantUserImpersonationTokensTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateTenantUserImpersonationTokensTable), 'up')
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2020_05_15_0000...', Object(Closure))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2020_05_15_0000...', Object(Closure))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 2, false)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-30 15:16:54] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'tenant_user_impersonation_tokens' already exists (Connection: mysql, SQL: create table `tenant_user_impersonation_tokens` (`token` varchar(128) not null, `tenant_id` bigint unsigned not null, `user_id` varchar(255) not null, `auth_guard` varchar(255) not null, `redirect_url` varchar(255) not null, `created_at` timestamp not null, primary key (`token`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'tenant_user_impersonation_tokens' already exists (Connection: mysql, SQL: create table `tenant_user_impersonation_tokens` (`token` varchar(128) not null, `tenant_id` bigint unsigned not null, `user_id` varchar(255) not null, `auth_guard` varchar(255) not null, `redirect_url` varchar(255) not null, `created_at` timestamp not null, primary key (`token`)) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `t...', Array, Object(Closure))
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `t...', Array, Object(Closure))
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `t...')
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('tenant_user_imp...', Object(Closure))
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2020_05_15_000010_create_tenant_user_impersonation_tokens_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateTenantUserImpersonationTokensTable->up()
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateTenantUserImpersonationTokensTable), 'up')
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateTenantUserImpersonationTokensTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2020_05_15_0000...', Object(Closure))
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2020_05_15_0000...', Object(Closure))
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 3, false)
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'tenant_user_impersonation_tokens' already exists at D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `t...', Array)
#2 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `t...', Array, Object(Closure))
#3 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `t...', Array, Object(Closure))
#4 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `t...')
#5 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('tenant_user_imp...', Object(Closure))
#8 D:\\Work\\ceramic pos\\ceramic-pos-system\\database\\migrations\\2020_05_15_000010_create_tenant_user_impersonation_tokens_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): CreateTenantUserImpersonationTokensTable->up()
#10 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(CreateTenantUserImpersonationTokensTable), 'up')
#11 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(CreateTenantUserImpersonationTokensTable), 'up')
#13 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2020_05_15_0000...', Object(Closure))
#15 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2020_05_15_0000...', Object(Closure))
#16 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Work\\\\ceramic...', 3, false)
#17 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\Work\\ceramic pos\\ceramic-pos-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\Work\\ceramic pos\\ceramic-pos-system\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
