<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\BelongsToTenant;
use Illuminate\Support\Facades\Crypt;

class Setting extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_public',
        'is_encrypted',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
    ];

    /**
     * الإعدادات الافتراضية للنظام
     */
    const DEFAULT_SETTINGS = [
        // إعدادات الشركة
        'company' => [
            'company_name' => ['value' => '', 'type' => 'string', 'description' => 'اسم الشركة'],
            'company_logo' => ['value' => '', 'type' => 'file', 'description' => 'شعار الشركة'],
            'company_address' => ['value' => '', 'type' => 'text', 'description' => 'عنوان الشركة'],
            'company_phone' => ['value' => '', 'type' => 'string', 'description' => 'هاتف الشركة'],
            'company_email' => ['value' => '', 'type' => 'email', 'description' => 'بريد الشركة الإلكتروني'],
            'company_website' => ['value' => '', 'type' => 'url', 'description' => 'موقع الشركة الإلكتروني'],
            'tax_number' => ['value' => '', 'type' => 'string', 'description' => 'الرقم الضريبي'],
            'commercial_register' => ['value' => '', 'type' => 'string', 'description' => 'السجل التجاري'],
        ],

        // إعدادات النظام
        'system' => [
            'default_currency' => ['value' => 'EGP', 'type' => 'string', 'description' => 'العملة الافتراضية'],
            'date_format' => ['value' => 'Y-m-d', 'type' => 'string', 'description' => 'تنسيق التاريخ'],
            'time_format' => ['value' => 'H:i', 'type' => 'string', 'description' => 'تنسيق الوقت'],
            'timezone' => ['value' => 'Africa/Cairo', 'type' => 'string', 'description' => 'المنطقة الزمنية'],
            'language' => ['value' => 'ar', 'type' => 'string', 'description' => 'اللغة الافتراضية'],
            'decimal_places' => ['value' => '2', 'type' => 'integer', 'description' => 'عدد الخانات العشرية'],
            'thousands_separator' => ['value' => ',', 'type' => 'string', 'description' => 'فاصل الآلاف'],
            'decimal_separator' => ['value' => '.', 'type' => 'string', 'description' => 'فاصل العشرات'],
        ],

        // إعدادات الفوترة
        'invoicing' => [
            'invoice_prefix_sales' => ['value' => 'INV-S-', 'type' => 'string', 'description' => 'بادئة فواتير البيع'],
            'invoice_prefix_purchase' => ['value' => 'INV-P-', 'type' => 'string', 'description' => 'بادئة فواتير الشراء'],
            'auto_invoice_numbering' => ['value' => 'true', 'type' => 'boolean', 'description' => 'ترقيم الفواتير تلقائياً'],
            'default_payment_terms' => ['value' => '30', 'type' => 'integer', 'description' => 'شروط الدفع الافتراضية (بالأيام)'],
            'tax_rate' => ['value' => '14', 'type' => 'decimal', 'description' => 'معدل الضريبة (%)'],
            'include_tax_in_price' => ['value' => 'false', 'type' => 'boolean', 'description' => 'تضمين الضريبة في السعر'],
        ],

        // إعدادات المخزون
        'inventory' => [
            'low_stock_threshold' => ['value' => '10', 'type' => 'integer', 'description' => 'حد التنبيه للمخزون المنخفض'],
            'auto_update_stock' => ['value' => 'true', 'type' => 'boolean', 'description' => 'تحديث المخزون تلقائياً'],
            'allow_negative_stock' => ['value' => 'false', 'type' => 'boolean', 'description' => 'السماح بالمخزون السالب'],
            'stock_valuation_method' => ['value' => 'fifo', 'type' => 'string', 'description' => 'طريقة تقييم المخزون'],
        ],

        // إعدادات الواجهة
        'ui' => [
            'theme' => ['value' => 'light', 'type' => 'string', 'description' => 'المظهر'],
            'sidebar_collapsed' => ['value' => 'false', 'type' => 'boolean', 'description' => 'طي الشريط الجانبي'],
            'items_per_page' => ['value' => '20', 'type' => 'integer', 'description' => 'عدد العناصر في الصفحة'],
            'show_dashboard_widgets' => ['value' => 'true', 'type' => 'boolean', 'description' => 'عرض عناصر لوحة التحكم'],
        ],

        // إعدادات الطباعة
        'printing' => [
            'print_logo' => ['value' => 'true', 'type' => 'boolean', 'description' => 'طباعة الشعار'],
            'print_company_info' => ['value' => 'true', 'type' => 'boolean', 'description' => 'طباعة معلومات الشركة'],
            'paper_size' => ['value' => 'A4', 'type' => 'string', 'description' => 'حجم الورق'],
            'print_orientation' => ['value' => 'portrait', 'type' => 'string', 'description' => 'اتجاه الطباعة'],
        ],

        // إعدادات الأمان
        'security' => [
            'session_timeout' => ['value' => '120', 'type' => 'integer', 'description' => 'انتهاء الجلسة (بالدقائق)'],
            'password_min_length' => ['value' => '8', 'type' => 'integer', 'description' => 'الحد الأدنى لطول كلمة المرور'],
            'require_strong_password' => ['value' => 'true', 'type' => 'boolean', 'description' => 'طلب كلمة مرور قوية'],
            'two_factor_auth' => ['value' => 'false', 'type' => 'boolean', 'description' => 'المصادقة الثنائية'],
        ],
    ];

    /**
     * الحصول على قيمة الإعداد
     */
    public function getValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            $value = Crypt::decrypt($value);
        }

        return match($this->type) {
            'boolean' => filter_var($value, FILTER_VALIDATE_BOOLEAN),
            'integer' => (int) $value,
            'decimal', 'float' => (float) $value,
            'json' => json_decode($value, true),
            default => $value,
        };
    }

    /**
     * تعيين قيمة الإعداد
     */
    public function setValueAttribute($value)
    {
        if ($this->type === 'json') {
            $value = json_encode($value);
        }

        if ($this->is_encrypted) {
            $value = Crypt::encrypt($value);
        }

        $this->attributes['value'] = $value;
    }

    /**
     * Scope للإعدادات العامة
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope حسب المجموعة
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * الحصول على إعداد
     */
    public static function get(string $key, $default = null)
    {
        $setting = self::where('key', $key)->first();

        return $setting ? $setting->value : $default;
    }

    /**
     * تعيين إعداد
     */
    public static function set(string $key, $value, array $options = []): self
    {
        $setting = self::updateOrCreate(
            [
                'tenant_id' => tenant('id'),
                'key' => $key,
            ],
            array_merge([
                'value' => $value,
                'type' => 'string',
                'group' => 'general',
            ], $options)
        );

        return $setting;
    }

    /**
     * الحصول على إعدادات مجموعة
     */
    public static function getGroup(string $group): array
    {
        return self::byGroup($group)
                  ->pluck('value', 'key')
                  ->toArray();
    }

    /**
     * تعيين إعدادات مجموعة
     */
    public static function setGroup(string $group, array $settings): void
    {
        foreach ($settings as $key => $value) {
            self::set($key, $value, ['group' => $group]);
        }
    }

    /**
     * إنشاء الإعدادات الافتراضية
     */
    public static function createDefaultSettings(string $tenantId): void
    {
        foreach (self::DEFAULT_SETTINGS as $group => $settings) {
            foreach ($settings as $key => $config) {
                self::updateOrCreate(
                    [
                        'tenant_id' => $tenantId,
                        'key' => $key,
                    ],
                    [
                        'value' => $config['value'],
                        'type' => $config['type'],
                        'group' => $group,
                        'description' => $config['description'],
                        'is_public' => false,
                        'is_encrypted' => false,
                    ]
                );
            }
        }
    }

    /**
     * الحصول على جميع الإعدادات مجمعة
     */
    public static function getAllGrouped(): array
    {
        return self::all()
                  ->groupBy('group')
                  ->map(function($settings) {
                      return $settings->pluck('value', 'key');
                  })
                  ->toArray();
    }

    /**
     * مسح كاش الإعدادات
     */
    public static function clearCache(): void
    {
        cache()->forget('settings_' . tenant('id'));
    }

    /**
     * الحصول على الإعدادات من الكاش
     */
    public static function getCached(): array
    {
        return cache()->remember('settings_' . tenant('id'), 3600, function() {
            return self::getAllGrouped();
        });
    }
}
