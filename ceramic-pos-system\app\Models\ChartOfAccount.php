<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Traits\BelongsToTenant;

class ChartOfAccount extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'account_code',
        'account_name',
        'account_name_en',
        'account_type',
        'account_category',
        'parent_id',
        'level',
        'path',
        'nature',
        'opening_balance',
        'current_balance',
        'is_active',
        'is_system',
        'description',
        'settings',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'settings' => 'array',
        'level' => 'integer',
    ];

    /**
     * أنواع الحسابات
     */
    const ACCOUNT_TYPES = [
        'assets' => 'الأصول',
        'liabilities' => 'الخصوم',
        'equity' => 'حقوق الملكية',
        'revenue' => 'الإيرادات',
        'expenses' => 'المصروفات',
        'cost_of_goods_sold' => 'تكلفة البضاعة المباعة',
    ];

    /**
     * فئات الحسابات
     */
    const ACCOUNT_CATEGORIES = [
        'current_assets' => 'الأصول المتداولة',
        'fixed_assets' => 'الأصول الثابتة',
        'current_liabilities' => 'الخصوم المتداولة',
        'long_term_liabilities' => 'الخصوم طويلة الأجل',
        'capital' => 'رأس المال',
        'retained_earnings' => 'الأرباح المحتجزة',
        'sales_revenue' => 'إيرادات المبيعات',
        'other_revenue' => 'إيرادات أخرى',
        'operating_expenses' => 'مصروفات تشغيلية',
        'administrative_expenses' => 'مصروفات إدارية',
        'financial_expenses' => 'مصروفات مالية',
        'cogs' => 'تكلفة البضاعة المباعة',
    ];

    /**
     * طبيعة الحسابات
     */
    const NATURES = [
        'debit' => 'مدين',
        'credit' => 'دائن',
    ];

    /**
     * العلاقة مع الحساب الأب
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(ChartOfAccount::class, 'parent_id');
    }

    /**
     * العلاقة مع الحسابات الفرعية
     */
    public function children(): HasMany
    {
        return $this->hasMany(ChartOfAccount::class, 'parent_id');
    }

    /**
     * الحصول على جميع الحسابات الفرعية (متداخلة)
     */
    public function allChildren(): HasMany
    {
        return $this->children()->with('allChildren');
    }

    /**
     * الحصول على مسار الحساب الكامل
     */
    public function getFullPathAttribute(): string
    {
        $path = collect();
        $current = $this;

        while ($current) {
            $path->prepend($current->account_name);
            $current = $current->parent;
        }

        return $path->implode(' > ');
    }

    /**
     * التحقق من كون الحساب حساب أب
     */
    public function getIsParentAttribute(): bool
    {
        return $this->children()->count() > 0;
    }

    /**
     * Scope للحسابات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للحسابات الرئيسية (بدون أب)
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope للحسابات حسب النوع
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('account_type', $type);
    }

    /**
     * Scope للحسابات حسب الفئة
     */
    public function scopeOfCategory($query, $category)
    {
        return $query->where('account_category', $category);
    }

    /**
     * Scope للحسابات حسب الطبيعة
     */
    public function scopeOfNature($query, $nature)
    {
        return $query->where('nature', $nature);
    }

    /**
     * Scope للبحث في الحسابات
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('account_name', 'like', "%{$search}%")
              ->orWhere('account_code', 'like', "%{$search}%")
              ->orWhere('account_name_en', 'like', "%{$search}%");
        });
    }

    /**
     * تحديث مسار الحساب
     */
    public function updatePath()
    {
        $path = collect([$this->id]);
        $current = $this->parent;

        while ($current) {
            $path->prepend($current->id);
            $current = $current->parent;
        }

        $this->update(['path' => $path->implode('/')]);
    }

    /**
     * تحديث مستوى الحساب
     */
    public function updateLevel()
    {
        $level = 1;
        $current = $this->parent;

        while ($current) {
            $level++;
            $current = $current->parent;
        }

        $this->update(['level' => $level]);
    }

    /**
     * الحصول على اسم نوع الحساب
     */
    public function getAccountTypeNameAttribute(): string
    {
        return self::ACCOUNT_TYPES[$this->account_type] ?? $this->account_type;
    }

    /**
     * الحصول على اسم فئة الحساب
     */
    public function getAccountCategoryNameAttribute(): string
    {
        return self::ACCOUNT_CATEGORIES[$this->account_category] ?? $this->account_category;
    }

    /**
     * الحصول على اسم طبيعة الحساب
     */
    public function getNatureNameAttribute(): string
    {
        return self::NATURES[$this->nature] ?? $this->nature;
    }
}
