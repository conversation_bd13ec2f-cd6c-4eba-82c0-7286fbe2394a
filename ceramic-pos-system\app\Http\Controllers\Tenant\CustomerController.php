<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;

class CustomerController extends Controller
{
    /**
     * عرض قائمة العملاء
     */
    public function index(Request $request)
    {
        $query = Customer::query();

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // التصفية حسب النوع
        if ($request->filled('customer_type')) {
            $query->ofType($request->customer_type);
        }

        // التصفية حسب الحالة
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // التصفية حسب نوع الرصيد
        if ($request->filled('balance_type')) {
            $query->where('balance_type', $request->balance_type);
        }

        // التصفية للعملاء المتأخرين
        if ($request->boolean('overdue')) {
            $query->overdue();
        }

        // التصفية للعملاء الذين تجاوزوا حد الائتمان
        if ($request->boolean('over_credit_limit')) {
            $query->overCreditLimit();
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'customer_code');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $customers = $query->paginate(20);

        // إحصائيات
        $stats = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::active()->count(),
            'individual_customers' => Customer::ofType('individual')->count(),
            'company_customers' => Customer::ofType('company')->count(),
            'overdue_customers' => Customer::overdue()->count(),
            'over_credit_limit' => Customer::overCreditLimit()->count(),
            'total_receivables' => Customer::where('balance_type', 'debit')->sum('current_balance'),
        ];

        return view('tenant.customers.index', compact('customers', 'stats'));
    }

    /**
     * عرض نموذج إنشاء عميل جديد
     */
    public function create()
    {
        return view('tenant.customers.create');
    }

    /**
     * حفظ عميل جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_code' => 'required|string|max:20|unique:customers,customer_code',
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'customer_type' => 'required|in:individual,company',
            'tax_number' => 'nullable|string|max:50',
            'commercial_register' => 'nullable|string|max:50',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:2',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'opening_balance' => 'nullable|numeric|min:0',
            'balance_type' => 'required|in:debit,credit',
            'notes' => 'nullable|string',
        ], [
            'customer_code.required' => 'رقم العميل مطلوب',
            'customer_code.unique' => 'رقم العميل مستخدم من قبل',
            'name.required' => 'اسم العميل مطلوب',
            'customer_type.required' => 'نوع العميل مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'country.required' => 'البلد مطلوب',
            'balance_type.required' => 'نوع الرصيد مطلوب',
        ]);

        try {
            $customer = Customer::create([
                'customer_code' => $request->customer_code,
                'name' => $request->name,
                'company_name' => $request->company_name,
                'customer_type' => $request->customer_type,
                'tax_number' => $request->tax_number,
                'commercial_register' => $request->commercial_register,
                'email' => $request->email,
                'phone' => $request->phone,
                'mobile' => $request->mobile,
                'fax' => $request->fax,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code,
                'country' => $request->country,
                'credit_limit' => $request->credit_limit ?? 0,
                'payment_terms' => $request->payment_terms ?? 30,
                'opening_balance' => $request->opening_balance ?? 0,
                'current_balance' => $request->opening_balance ?? 0,
                'balance_type' => $request->balance_type,
                'notes' => $request->notes,
                'is_active' => true,
            ]);

            return redirect()->route('tenant.customers.index')
                ->with('success', 'تم إنشاء العميل بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء العميل: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل العميل
     */
    public function show(Customer $customer)
    {
        // $customer->load('invoices', 'payments', 'salesOrders');

        // إحصائيات العميل
        $stats = [
            'total_sales' => 0, // $customer->total_sales,
            'total_payments' => 0, // $customer->total_payments,
            'invoices_count' => 0, // $customer->invoices()->count(),
            'last_invoice_date' => null, // $customer->last_invoice?->created_at,
            'last_payment_date' => null, // $customer->last_payment?->created_at,
        ];

        return view('tenant.customers.show', compact('customer', 'stats'));
    }

    /**
     * عرض نموذج تعديل العميل
     */
    public function edit(Customer $customer)
    {
        return view('tenant.customers.edit', compact('customer'));
    }

    /**
     * تحديث العميل
     */
    public function update(Request $request, Customer $customer)
    {
        $request->validate([
            'customer_code' => 'required|string|max:20|unique:customers,customer_code,' . $customer->id,
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'customer_type' => 'required|in:individual,company',
            'tax_number' => 'nullable|string|max:50',
            'commercial_register' => 'nullable|string|max:50',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:2',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        try {
            $customer->update([
                'customer_code' => $request->customer_code,
                'name' => $request->name,
                'company_name' => $request->company_name,
                'customer_type' => $request->customer_type,
                'tax_number' => $request->tax_number,
                'commercial_register' => $request->commercial_register,
                'email' => $request->email,
                'phone' => $request->phone,
                'mobile' => $request->mobile,
                'fax' => $request->fax,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code,
                'country' => $request->country,
                'credit_limit' => $request->credit_limit ?? 0,
                'payment_terms' => $request->payment_terms ?? 30,
                'notes' => $request->notes,
                'is_active' => $request->boolean('is_active', true),
            ]);

            return redirect()->route('tenant.customers.index')
                ->with('success', 'تم تحديث العميل بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث العميل: ' . $e->getMessage());
        }
    }

    /**
     * حذف العميل
     */
    public function destroy(Customer $customer)
    {
        // التحقق من عدم وجود فواتير أو حركات مالية
        // if ($customer->invoices()->count() > 0 || $customer->payments()->count() > 0) {
        //     return redirect()->back()
        //         ->with('error', 'لا يمكن حذف عميل له فواتير أو حركات مالية');
        // }

        try {
            $customer->delete();

            return redirect()->route('tenant.customers.index')
                ->with('success', 'تم حذف العميل بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف العميل: ' . $e->getMessage());
        }
    }

    /**
     * تفعيل/إلغاء تفعيل العميل
     */
    public function toggleStatus(Customer $customer)
    {
        $customer->update(['is_active' => !$customer->is_active]);

        return response()->json([
            'success' => true,
            'message' => $customer->is_active ? 'تم تفعيل العميل' : 'تم إلغاء تفعيل العميل',
            'is_active' => $customer->is_active
        ]);
    }

    /**
     * البحث في العملاء (للـ API)
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $customers = Customer::search($query)
            ->active()
            ->limit(10)
            ->get(['id', 'customer_code', 'name', 'company_name', 'current_balance', 'balance_type']);

        return response()->json($customers->map(function ($customer) {
            return [
                'id' => $customer->id,
                'code' => $customer->customer_code,
                'name' => $customer->full_name,
                'balance' => $customer->current_balance,
                'balance_type' => $customer->balance_type_name,
            ];
        }));
    }

    /**
     * تقرير العملاء
     */
    public function report(Request $request)
    {
        $query = Customer::query();

        // التصفية حسب التاريخ
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        // التصفية حسب النوع
        if ($request->filled('customer_type')) {
            $query->ofType($request->customer_type);
        }

        // التصفية حسب الحالة
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $customers = $query->orderBy('customer_code')->get();

        return view('tenant.customers.report', compact('customers'));
    }
}
