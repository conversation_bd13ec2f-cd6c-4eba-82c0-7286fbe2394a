<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Traits\BelongsToTenant;

class Warehouse extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'warehouse_code',
        'name',
        'description',
        'address',
        'city',
        'phone',
        'manager_name',
        'manager_phone',
        'is_main',
        'is_active',
        'settings',
    ];

    protected $casts = [
        'is_main' => 'boolean',
        'is_active' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * العلاقة مع مخزون المنتجات
     */
    public function productStock(): HasMany
    {
        return $this->hasMany(ProductStock::class);
    }

    /**
     * العلاقة مع حركات المخزون
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * Scope للمخازن النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للمخزن الرئيسي
     */
    public function scopeMain($query)
    {
        return $query->where('is_main', true);
    }

    /**
     * Scope للبحث في المخازن
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('warehouse_code', 'like', "%{$search}%")
              ->orWhere('city', 'like', "%{$search}%");
        });
    }

    /**
     * الحصول على إجمالي قيمة المخزون
     */
    public function getTotalStockValueAttribute(): float
    {
        return $this->productStock()->get()->sum('stock_value');
    }

    /**
     * الحصول على عدد المنتجات
     */
    public function getProductsCountAttribute(): int
    {
        return $this->productStock()->count();
    }

    /**
     * الحصول على عدد المنتجات المنخفضة المخزون
     */
    public function getLowStockProductsCountAttribute(): int
    {
        return $this->productStock()->lowStock()->count();
    }

    /**
     * الحصول على عدد المنتجات النافدة
     */
    public function getOutOfStockProductsCountAttribute(): int
    {
        return $this->productStock()->outOfStock()->count();
    }

    /**
     * الحصول على المنتجات في هذا المخزن
     */
    public function getProducts()
    {
        return Product::whereHas('stock', function($query) {
            $query->where('warehouse_id', $this->id)
                  ->where('quantity', '>', 0);
        })->with(['stock' => function($query) {
            $query->where('warehouse_id', $this->id);
        }]);
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    public function getLowStockProducts()
    {
        return $this->getProducts()->get()->filter(function($product) {
            $stock = $product->stock->first();
            return $stock && $stock->quantity <= $product->minimum_stock;
        });
    }

    /**
     * الحصول على المنتجات النافدة
     */
    public function getOutOfStockProducts()
    {
        return Product::whereHas('stock', function($query) {
            $query->where('warehouse_id', $this->id)
                  ->where('quantity', '<=', 0);
        })->with(['stock' => function($query) {
            $query->where('warehouse_id', $this->id);
        }]);
    }

    /**
     * التحقق من توفر كمية من منتج
     */
    public function hasProductQuantity(int $productId, int $quantity): bool
    {
        $stock = $this->productStock()
                     ->where('product_id', $productId)
                     ->first();

        return $stock && $stock->available_quantity >= $quantity;
    }

    /**
     * الحصول على كمية منتج متاحة
     */
    public function getProductAvailableQuantity(int $productId): int
    {
        $stock = $this->productStock()
                     ->where('product_id', $productId)
                     ->first();

        return $stock ? $stock->available_quantity : 0;
    }

    /**
     * تحديث مخزون منتج
     */
    public function updateProductStock(int $productId, int $quantity, string $type = 'add', float $cost = null): bool
    {
        $stock = $this->productStock()->firstOrCreate(
            ['product_id' => $productId],
            [
                'quantity' => 0,
                'reserved_quantity' => 0,
                'available_quantity' => 0,
                'average_cost' => 0
            ]
        );

        if ($type === 'add') {
            $stock->addQuantity($quantity, $cost);
        } elseif ($type === 'subtract') {
            return $stock->subtractQuantity($quantity);
        }

        return true;
    }

    /**
     * نقل مخزون بين المخازن
     */
    public function transferStockTo(Warehouse $targetWarehouse, int $productId, int $quantity): bool
    {
        // التحقق من توفر الكمية
        if (!$this->hasProductQuantity($productId, $quantity)) {
            return false;
        }

        // خصم من المخزن المصدر
        $sourceStock = $this->productStock()->where('product_id', $productId)->first();
        if (!$sourceStock->subtractQuantity($quantity)) {
            return false;
        }

        // إضافة للمخزن المستهدف
        $targetWarehouse->updateProductStock($productId, $quantity, 'add', $sourceStock->average_cost);

        return true;
    }
}
