<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    'tenant',
    'prevent.central',
])->group(function () {

    // الصفحة الرئيسية للشركة
    Route::get('/', function () {
        $tenant = tenant();
        return view('tenant.dashboard', compact('tenant'));
    })->name('tenant.dashboard');

    // صفحات التوثيق للشركة
    Route::get('/login', function () {
        return view('tenant.auth.login');
    })->name('tenant.login');

    Route::get('/register', function () {
        return view('tenant.auth.register');
    })->name('tenant.register');

    // مجموعة الصفحات المحمية (تتطلب تسجيل دخول)
    Route::middleware(['auth'])->group(function () {

        // لوحة التحكم الرئيسية
        Route::get('/dashboard', function () {
            $tenant = tenant();
            return view('tenant.dashboard', compact('tenant'));
        })->name('tenant.dashboard');

        // دليل الحسابات
        Route::prefix('chart-of-accounts')->name('tenant.chart-of-accounts.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'index'])->name('index');
            Route::get('/tree', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'tree'])->name('tree');
            Route::get('/create', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'store'])->name('store');
            Route::get('/{chartOfAccount}', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'show'])->name('show');
            Route::get('/{chartOfAccount}/edit', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'edit'])->name('edit');
            Route::put('/{chartOfAccount}', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'update'])->name('update');
            Route::delete('/{chartOfAccount}', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'destroy'])->name('destroy');
            Route::post('/{chartOfAccount}/toggle-status', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'toggleStatus'])->name('toggle-status');
        });

        // إدارة العملاء
        Route::prefix('customers')->name('tenant.customers.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\CustomerController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\CustomerController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\CustomerController::class, 'store'])->name('store');
            Route::get('/search', [\App\Http\Controllers\Tenant\CustomerController::class, 'search'])->name('search');
            Route::get('/report', [\App\Http\Controllers\Tenant\CustomerController::class, 'report'])->name('report');
            Route::get('/{customer}', [\App\Http\Controllers\Tenant\CustomerController::class, 'show'])->name('show');
            Route::get('/{customer}/edit', [\App\Http\Controllers\Tenant\CustomerController::class, 'edit'])->name('edit');
            Route::put('/{customer}', [\App\Http\Controllers\Tenant\CustomerController::class, 'update'])->name('update');
            Route::delete('/{customer}', [\App\Http\Controllers\Tenant\CustomerController::class, 'destroy'])->name('destroy');
            Route::post('/{customer}/toggle-status', [\App\Http\Controllers\Tenant\CustomerController::class, 'toggleStatus'])->name('toggle-status');
        });

        // إدارة الموردين
        Route::prefix('suppliers')->name('tenant.suppliers.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\SupplierController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\SupplierController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\SupplierController::class, 'store'])->name('store');
            Route::get('/search', [\App\Http\Controllers\Tenant\SupplierController::class, 'search'])->name('search');
            Route::get('/report', [\App\Http\Controllers\Tenant\SupplierController::class, 'report'])->name('report');
            Route::get('/{supplier}', [\App\Http\Controllers\Tenant\SupplierController::class, 'show'])->name('show');
            Route::get('/{supplier}/edit', [\App\Http\Controllers\Tenant\SupplierController::class, 'edit'])->name('edit');
            Route::put('/{supplier}', [\App\Http\Controllers\Tenant\SupplierController::class, 'update'])->name('update');
            Route::delete('/{supplier}', [\App\Http\Controllers\Tenant\SupplierController::class, 'destroy'])->name('destroy');
            Route::post('/{supplier}/toggle-status', [\App\Http\Controllers\Tenant\SupplierController::class, 'toggleStatus'])->name('toggle-status');
        });

        // إدارة المنتجات
        Route::prefix('products')->name('tenant.products.')->group(function () {
            Route::get('/', function () {
                return view('tenant.products.index');
            })->name('index');
        });

        // إدارة المخزون
        Route::prefix('inventory')->name('tenant.inventory.')->group(function () {
            Route::get('/', function () {
                return view('tenant.inventory.index');
            })->name('index');
        });

        // إدارة الفواتير
        Route::prefix('invoices')->name('tenant.invoices.')->group(function () {
            Route::get('/', function () {
                return view('tenant.invoices.index');
            })->name('index');
        });

        // التقارير المالية
        Route::prefix('reports')->name('tenant.reports.')->group(function () {
            Route::get('/', function () {
                return view('tenant.reports.index');
            })->name('index');
        });

        // إعدادات الشركة
        Route::prefix('settings')->name('tenant.settings.')->group(function () {
            Route::get('/', function () {
                $tenant = tenant();
                return view('tenant.settings.index', compact('tenant'));
            })->name('index');
        });
    });
});
