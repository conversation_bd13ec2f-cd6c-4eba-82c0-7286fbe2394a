<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    'tenant',
    'prevent.central',
])->group(function () {

    // الصفحة الرئيسية للشركة
    Route::get('/', function () {
        $tenant = tenant();
        return view('tenant.dashboard', compact('tenant'));
    })->name('tenant.dashboard');

    // صفحات التوثيق للشركة
    Route::get('/login', function () {
        return view('tenant.auth.login');
    })->name('tenant.login');

    Route::get('/register', function () {
        return view('tenant.auth.register');
    })->name('tenant.register');

    // مجموعة الصفحات المحمية (تتطلب تسجيل دخول)
    Route::middleware(['auth'])->group(function () {

        // لوحة التحكم الرئيسية
        Route::get('/dashboard', function () {
            $tenant = tenant();
            return view('tenant.dashboard', compact('tenant'));
        })->name('tenant.dashboard');

        // إدارة العملاء
        Route::prefix('customers')->name('tenant.customers.')->group(function () {
            Route::get('/', function () {
                return view('tenant.customers.index');
            })->name('index');
        });

        // إدارة الموردين
        Route::prefix('suppliers')->name('tenant.suppliers.')->group(function () {
            Route::get('/', function () {
                return view('tenant.suppliers.index');
            })->name('index');
        });

        // إدارة المنتجات
        Route::prefix('products')->name('tenant.products.')->group(function () {
            Route::get('/', function () {
                return view('tenant.products.index');
            })->name('index');
        });

        // إدارة المخزون
        Route::prefix('inventory')->name('tenant.inventory.')->group(function () {
            Route::get('/', function () {
                return view('tenant.inventory.index');
            })->name('index');
        });

        // إدارة الفواتير
        Route::prefix('invoices')->name('tenant.invoices.')->group(function () {
            Route::get('/', function () {
                return view('tenant.invoices.index');
            })->name('index');
        });

        // التقارير المالية
        Route::prefix('reports')->name('tenant.reports.')->group(function () {
            Route::get('/', function () {
                return view('tenant.reports.index');
            })->name('index');
        });

        // إعدادات الشركة
        Route::prefix('settings')->name('tenant.settings.')->group(function () {
            Route::get('/', function () {
                $tenant = tenant();
                return view('tenant.settings.index', compact('tenant'));
            })->name('index');
        });
    });
});
