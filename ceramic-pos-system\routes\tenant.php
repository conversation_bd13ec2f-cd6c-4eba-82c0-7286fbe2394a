<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    'tenant',
    'prevent.central',
])->group(function () {

    // لوحة التحكم للشركة
    Route::get('/dashboard', [\App\Http\Controllers\Tenant\DashboardController::class, 'index'])->name('tenant.dashboard');

    // صفحات التوثيق للشركة
    Route::get('/login', function () {
        return view('tenant.auth.login');
    })->name('tenant.login');

    Route::get('/register', function () {
        return view('tenant.auth.register');
    })->name('tenant.register');

    // مجموعة الصفحات المحمية (تتطلب تسجيل دخول)
    Route::middleware(['auth'])->group(function () {

        // لوحة التحكم الرئيسية
        Route::get('/dashboard', [\App\Http\Controllers\Tenant\DashboardController::class, 'index'])->name('tenant.dashboard.main');
        Route::get('/company-info', [\App\Http\Controllers\Tenant\DashboardController::class, 'companyInfo'])->name('tenant.company-info');
        Route::get('/quick-stats', [\App\Http\Controllers\Tenant\DashboardController::class, 'quickStats'])->name('tenant.quick-stats');
        Route::get('/chart-data', [\App\Http\Controllers\Tenant\DashboardController::class, 'chartData'])->name('tenant.chart-data');

        // العناصر المصغرة (Widgets)
        Route::prefix('widgets')->name('tenant.widgets.')->group(function () {
            Route::get('/sales', [\App\Http\Controllers\Tenant\WidgetController::class, 'salesWidget'])->name('sales');
            Route::get('/payments', [\App\Http\Controllers\Tenant\WidgetController::class, 'paymentsWidget'])->name('payments');
            Route::get('/inventory', [\App\Http\Controllers\Tenant\WidgetController::class, 'inventoryWidget'])->name('inventory');
            Route::get('/top-products', [\App\Http\Controllers\Tenant\WidgetController::class, 'topProductsWidget'])->name('top-products');
            Route::get('/top-customers', [\App\Http\Controllers\Tenant\WidgetController::class, 'topCustomersWidget'])->name('top-customers');
            Route::get('/overdue-invoices', [\App\Http\Controllers\Tenant\WidgetController::class, 'overdueInvoicesWidget'])->name('overdue-invoices');
            Route::get('/alerts', [\App\Http\Controllers\Tenant\WidgetController::class, 'alertsWidget'])->name('alerts');
            Route::get('/recent-activities', [\App\Http\Controllers\Tenant\WidgetController::class, 'recentActivitiesWidget'])->name('recent-activities');
            Route::get('/sales-chart', [\App\Http\Controllers\Tenant\WidgetController::class, 'salesChartWidget'])->name('sales-chart');
            Route::get('/cash-flow-chart', [\App\Http\Controllers\Tenant\WidgetController::class, 'cashFlowChartWidget'])->name('cash-flow-chart');
        });

        // دليل الحسابات
        Route::prefix('chart-of-accounts')->name('tenant.chart-of-accounts.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'index'])->name('index');
            Route::get('/tree', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'tree'])->name('tree');
            Route::get('/create', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'store'])->name('store');
            Route::get('/{chartOfAccount}', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'show'])->name('show');
            Route::get('/{chartOfAccount}/edit', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'edit'])->name('edit');
            Route::put('/{chartOfAccount}', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'update'])->name('update');
            Route::delete('/{chartOfAccount}', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'destroy'])->name('destroy');
            Route::post('/{chartOfAccount}/toggle-status', [\App\Http\Controllers\Tenant\ChartOfAccountController::class, 'toggleStatus'])->name('toggle-status');
        });

        // إدارة العملاء
        Route::prefix('customers')->name('tenant.customers.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\CustomerController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\CustomerController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\CustomerController::class, 'store'])->name('store');
            Route::get('/search', [\App\Http\Controllers\Tenant\CustomerController::class, 'search'])->name('search');
            Route::get('/report', [\App\Http\Controllers\Tenant\CustomerController::class, 'report'])->name('report');
            Route::get('/{customer}', [\App\Http\Controllers\Tenant\CustomerController::class, 'show'])->name('show');
            Route::get('/{customer}/edit', [\App\Http\Controllers\Tenant\CustomerController::class, 'edit'])->name('edit');
            Route::put('/{customer}', [\App\Http\Controllers\Tenant\CustomerController::class, 'update'])->name('update');
            Route::delete('/{customer}', [\App\Http\Controllers\Tenant\CustomerController::class, 'destroy'])->name('destroy');
            Route::post('/{customer}/toggle-status', [\App\Http\Controllers\Tenant\CustomerController::class, 'toggleStatus'])->name('toggle-status');
        });

        // إدارة الموردين
        Route::prefix('suppliers')->name('tenant.suppliers.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\SupplierController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\SupplierController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\SupplierController::class, 'store'])->name('store');
            Route::get('/search', [\App\Http\Controllers\Tenant\SupplierController::class, 'search'])->name('search');
            Route::get('/report', [\App\Http\Controllers\Tenant\SupplierController::class, 'report'])->name('report');
            Route::get('/{supplier}', [\App\Http\Controllers\Tenant\SupplierController::class, 'show'])->name('show');
            Route::get('/{supplier}/edit', [\App\Http\Controllers\Tenant\SupplierController::class, 'edit'])->name('edit');
            Route::put('/{supplier}', [\App\Http\Controllers\Tenant\SupplierController::class, 'update'])->name('update');
            Route::delete('/{supplier}', [\App\Http\Controllers\Tenant\SupplierController::class, 'destroy'])->name('destroy');
            Route::post('/{supplier}/toggle-status', [\App\Http\Controllers\Tenant\SupplierController::class, 'toggleStatus'])->name('toggle-status');
        });

        // إدارة المنتجات
        Route::prefix('products')->name('tenant.products.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\ProductController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\ProductController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\ProductController::class, 'store'])->name('store');
            Route::get('/search', [\App\Http\Controllers\Tenant\ProductController::class, 'search'])->name('search');
            Route::get('/report', [\App\Http\Controllers\Tenant\ProductController::class, 'report'])->name('report');
            Route::get('/{product}', [\App\Http\Controllers\Tenant\ProductController::class, 'show'])->name('show');
            Route::get('/{product}/edit', [\App\Http\Controllers\Tenant\ProductController::class, 'edit'])->name('edit');
            Route::put('/{product}', [\App\Http\Controllers\Tenant\ProductController::class, 'update'])->name('update');
            Route::delete('/{product}', [\App\Http\Controllers\Tenant\ProductController::class, 'destroy'])->name('destroy');
            Route::post('/{product}/toggle-status', [\App\Http\Controllers\Tenant\ProductController::class, 'toggleStatus'])->name('toggle-status');
        });

        // إدارة المخازن
        Route::prefix('warehouses')->name('tenant.warehouses.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\WarehouseController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\WarehouseController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\WarehouseController::class, 'store'])->name('store');
            Route::get('/{warehouse}', [\App\Http\Controllers\Tenant\WarehouseController::class, 'show'])->name('show');
            Route::get('/{warehouse}/edit', [\App\Http\Controllers\Tenant\WarehouseController::class, 'edit'])->name('edit');
            Route::put('/{warehouse}', [\App\Http\Controllers\Tenant\WarehouseController::class, 'update'])->name('update');
            Route::delete('/{warehouse}', [\App\Http\Controllers\Tenant\WarehouseController::class, 'destroy'])->name('destroy');
            Route::post('/{warehouse}/toggle-status', [\App\Http\Controllers\Tenant\WarehouseController::class, 'toggleStatus'])->name('toggle-status');
            Route::get('/{warehouse}/report', [\App\Http\Controllers\Tenant\WarehouseController::class, 'report'])->name('report');
            Route::post('/transfer', [\App\Http\Controllers\Tenant\WarehouseController::class, 'transfer'])->name('transfer');
        });

        // إدارة حركات المخزون
        Route::prefix('stock-movements')->name('tenant.stock-movements.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\StockMovementController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\StockMovementController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\StockMovementController::class, 'store'])->name('store');
            Route::get('/report', [\App\Http\Controllers\Tenant\StockMovementController::class, 'report'])->name('report');
            Route::get('/{stockMovement}', [\App\Http\Controllers\Tenant\StockMovementController::class, 'show'])->name('show');
            Route::get('/product/{product}', [\App\Http\Controllers\Tenant\StockMovementController::class, 'productMovements'])->name('product-movements');
            Route::get('/warehouse/{warehouse}', [\App\Http\Controllers\Tenant\StockMovementController::class, 'warehouseMovements'])->name('warehouse-movements');
            Route::post('/adjustment', [\App\Http\Controllers\Tenant\StockMovementController::class, 'adjustment'])->name('adjustment');
        });

        // إدارة الفواتير
        Route::prefix('invoices')->name('tenant.invoices.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\InvoiceController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\InvoiceController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\InvoiceController::class, 'store'])->name('store');
            Route::get('/report', [\App\Http\Controllers\Tenant\InvoiceController::class, 'report'])->name('report');
            Route::get('/{invoice}', [\App\Http\Controllers\Tenant\InvoiceController::class, 'show'])->name('show');
            Route::get('/{invoice}/edit', [\App\Http\Controllers\Tenant\InvoiceController::class, 'edit'])->name('edit');
            Route::put('/{invoice}', [\App\Http\Controllers\Tenant\InvoiceController::class, 'update'])->name('update');
            Route::delete('/{invoice}', [\App\Http\Controllers\Tenant\InvoiceController::class, 'destroy'])->name('destroy');
            Route::post('/{invoice}/confirm', [\App\Http\Controllers\Tenant\InvoiceController::class, 'confirm'])->name('confirm');
            Route::post('/{invoice}/cancel', [\App\Http\Controllers\Tenant\InvoiceController::class, 'cancel'])->name('cancel');
            Route::get('/{invoice}/print', [\App\Http\Controllers\Tenant\InvoiceController::class, 'print'])->name('print');
        });

        // إدارة المدفوعات
        Route::prefix('payments')->name('tenant.payments.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\PaymentController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\PaymentController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\PaymentController::class, 'store'])->name('store');
            Route::get('/report', [\App\Http\Controllers\Tenant\PaymentController::class, 'report'])->name('report');
            Route::get('/unpaid-invoices', [\App\Http\Controllers\Tenant\PaymentController::class, 'getUnpaidInvoices'])->name('unpaid-invoices');
            Route::get('/{payment}', [\App\Http\Controllers\Tenant\PaymentController::class, 'show'])->name('show');
            Route::get('/{payment}/edit', [\App\Http\Controllers\Tenant\PaymentController::class, 'edit'])->name('edit');
            Route::put('/{payment}', [\App\Http\Controllers\Tenant\PaymentController::class, 'update'])->name('update');
            Route::delete('/{payment}', [\App\Http\Controllers\Tenant\PaymentController::class, 'destroy'])->name('destroy');
            Route::post('/{payment}/clear', [\App\Http\Controllers\Tenant\PaymentController::class, 'clear'])->name('clear');
            Route::post('/{payment}/cancel', [\App\Http\Controllers\Tenant\PaymentController::class, 'cancel'])->name('cancel');
            Route::get('/{payment}/print', [\App\Http\Controllers\Tenant\PaymentController::class, 'print'])->name('print');
        });

        // التقارير المالية
        Route::prefix('reports')->name('tenant.reports.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\ReportController::class, 'index'])->name('index');
            Route::get('/profit-loss', [\App\Http\Controllers\Tenant\ReportController::class, 'profitLoss'])->name('profit-loss');
            Route::get('/balance-sheet', [\App\Http\Controllers\Tenant\ReportController::class, 'balanceSheet'])->name('balance-sheet');
            Route::get('/cash-flow', [\App\Http\Controllers\Tenant\ReportController::class, 'cashFlow'])->name('cash-flow');
            Route::get('/trial-balance', [\App\Http\Controllers\Tenant\ReportController::class, 'trialBalance'])->name('trial-balance');
            Route::get('/sales-summary', [\App\Http\Controllers\Tenant\ReportController::class, 'salesSummary'])->name('sales-summary');
            Route::get('/sales-detailed', [\App\Http\Controllers\Tenant\ReportController::class, 'salesDetailed'])->name('sales-detailed');
            Route::get('/customer-statement', [\App\Http\Controllers\Tenant\ReportController::class, 'customerStatement'])->name('customer-statement');
            Route::get('/top-customers', [\App\Http\Controllers\Tenant\ReportController::class, 'topCustomers'])->name('top-customers');
            Route::get('/purchases-summary', [\App\Http\Controllers\Tenant\ReportController::class, 'purchasesSummary'])->name('purchases-summary');
        });

        // إدارة المستخدمين والأدوار
        Route::prefix('users')->name('tenant.users.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\UserController::class, 'index'])->name('index');
            Route::get('/create', [\App\Http\Controllers\Tenant\UserController::class, 'create'])->name('create');
            Route::post('/', [\App\Http\Controllers\Tenant\UserController::class, 'store'])->name('store');
            Route::get('/{user}', [\App\Http\Controllers\Tenant\UserController::class, 'show'])->name('show');
            Route::get('/{user}/edit', [\App\Http\Controllers\Tenant\UserController::class, 'edit'])->name('edit');
            Route::put('/{user}', [\App\Http\Controllers\Tenant\UserController::class, 'update'])->name('update');
            Route::delete('/{user}', [\App\Http\Controllers\Tenant\UserController::class, 'destroy'])->name('destroy');
            Route::post('/{user}/assign-roles', [\App\Http\Controllers\Tenant\UserController::class, 'assignRoles'])->name('assign-roles');
        });

        // الإعدادات
        Route::prefix('settings')->name('tenant.settings.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Tenant\SettingsController::class, 'index'])->name('index');
            Route::get('/company', [\App\Http\Controllers\Tenant\SettingsController::class, 'company'])->name('company');
            Route::get('/system', [\App\Http\Controllers\Tenant\SettingsController::class, 'system'])->name('system');
            Route::get('/security', [\App\Http\Controllers\Tenant\SettingsController::class, 'security'])->name('security');
            Route::post('/update', [\App\Http\Controllers\Tenant\SettingsController::class, 'update'])->name('update');
        });

        // إعدادات الشركة
        Route::prefix('settings')->name('tenant.settings.')->group(function () {
            Route::get('/', function () {
                $tenant = tenant();
                return view('tenant.settings.index', compact('tenant'));
            })->name('index');
        });
    });
});
