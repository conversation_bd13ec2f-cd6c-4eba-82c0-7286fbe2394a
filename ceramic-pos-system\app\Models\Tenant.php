<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains;

    protected $fillable = [
        'id',
    ];

    protected $casts = [
        'settings' => 'array',
        'features' => 'array',
        'subscription_start' => 'date',
        'subscription_end' => 'date',
        'max_users' => 'integer',
    ];

    public static function getCustomColumns(): array
    {
        return [
            'id',
            'name',
            'slug',
            'domain',
            'subdomain',
            'database_name',
            'logo',
            'description',
            'settings',
            'status',
            'plan',
            'subscription_start',
            'subscription_end',
            'max_users',
            'features',
            'contact_email',
            'contact_phone',
            'address',
            'city',
            'country',
            'currency',
            'timezone',
            'language',
        ];
    }

    public function getIncrementing()
    {
        return true;
    }

    public function getKeyType()
    {
        return 'int';
    }
}
