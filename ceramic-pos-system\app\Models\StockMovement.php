<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Traits\BelongsToTenant;

class StockMovement extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'product_id',
        'warehouse_id',
        'movement_type',
        'quantity',
        'quantity_before',
        'quantity_after',
        'unit_cost',
        'total_cost',
        'reference_type',
        'reference_id',
        'reference_number',
        'from_warehouse_id',
        'to_warehouse_id',
        'notes',
        'metadata',
        'created_by',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'quantity_before' => 'integer',
        'quantity_after' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * أنواع حركات المخزون
     */
    const MOVEMENT_TYPES = [
        'in' => 'إدخال',
        'out' => 'إخراج',
        'transfer' => 'تحويل',
        'adjustment' => 'تسوية',
        'return' => 'مرتجع',
        'damage' => 'تالف',
        'expired' => 'منتهي الصلاحية',
    ];

    /**
     * العلاقة مع المنتج
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * العلاقة مع المخزن
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * العلاقة مع المخزن المصدر (للتحويلات)
     */
    public function fromWarehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'from_warehouse_id');
    }

    /**
     * العلاقة مع المخزن المستهدف (للتحويلات)
     */
    public function toWarehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'to_warehouse_id');
    }

    /**
     * العلاقة مع المستخدم الذي أنشأ الحركة
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope لحركات الإدخال
     */
    public function scopeInbound($query)
    {
        return $query->whereIn('movement_type', ['in', 'return']);
    }

    /**
     * Scope لحركات الإخراج
     */
    public function scopeOutbound($query)
    {
        return $query->whereIn('movement_type', ['out', 'damage', 'expired']);
    }

    /**
     * Scope لحركات التحويل
     */
    public function scopeTransfers($query)
    {
        return $query->where('movement_type', 'transfer');
    }

    /**
     * Scope لحركات التسوية
     */
    public function scopeAdjustments($query)
    {
        return $query->where('movement_type', 'adjustment');
    }

    /**
     * Scope للحركات في فترة زمنية
     */
    public function scopeInPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope للحركات حسب المنتج
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope للحركات حسب المخزن
     */
    public function scopeForWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    /**
     * الحصول على اسم نوع الحركة
     */
    public function getMovementTypeNameAttribute(): string
    {
        return self::MOVEMENT_TYPES[$this->movement_type] ?? $this->movement_type;
    }

    /**
     * التحقق من كون الحركة إدخال
     */
    public function isInbound(): bool
    {
        return in_array($this->movement_type, ['in', 'return']);
    }

    /**
     * التحقق من كون الحركة إخراج
     */
    public function isOutbound(): bool
    {
        return in_array($this->movement_type, ['out', 'damage', 'expired']);
    }

    /**
     * التحقق من كون الحركة تحويل
     */
    public function isTransfer(): bool
    {
        return $this->movement_type === 'transfer';
    }

    /**
     * التحقق من كون الحركة تسوية
     */
    public function isAdjustment(): bool
    {
        return $this->movement_type === 'adjustment';
    }

    /**
     * الحصول على لون نوع الحركة
     */
    public function getMovementTypeColorAttribute(): string
    {
        $colors = [
            'in' => 'success',
            'out' => 'danger',
            'transfer' => 'info',
            'adjustment' => 'warning',
            'return' => 'primary',
            'damage' => 'dark',
            'expired' => 'secondary',
        ];

        return $colors[$this->movement_type] ?? 'secondary';
    }

    /**
     * الحصول على أيقونة نوع الحركة
     */
    public function getMovementTypeIconAttribute(): string
    {
        $icons = [
            'in' => 'fas fa-arrow-down',
            'out' => 'fas fa-arrow-up',
            'transfer' => 'fas fa-exchange-alt',
            'adjustment' => 'fas fa-balance-scale',
            'return' => 'fas fa-undo',
            'damage' => 'fas fa-exclamation-triangle',
            'expired' => 'fas fa-clock',
        ];

        return $icons[$this->movement_type] ?? 'fas fa-question';
    }

    /**
     * إنشاء حركة مخزون جديدة
     */
    public static function createMovement(array $data): self
    {
        // حساب إجمالي التكلفة
        $data['total_cost'] = $data['quantity'] * ($data['unit_cost'] ?? 0);

        // إضافة معرف المستخدم الحالي
        if (!isset($data['created_by']) && auth()->check()) {
            $data['created_by'] = auth()->id();
        }

        return self::create($data);
    }

    /**
     * إنشاء حركة إدخال
     */
    public static function createInboundMovement(
        int $productId,
        int $warehouseId,
        int $quantity,
        float $unitCost = 0,
        string $referenceType = null,
        int $referenceId = null,
        string $referenceNumber = null,
        string $notes = null
    ): self {
        // الحصول على الكمية الحالية
        $currentStock = ProductStock::where('product_id', $productId)
                                   ->where('warehouse_id', $warehouseId)
                                   ->first();

        $quantityBefore = $currentStock ? $currentStock->quantity : 0;
        $quantityAfter = $quantityBefore + $quantity;

        return self::createMovement([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'movement_type' => 'in',
            'quantity' => $quantity,
            'quantity_before' => $quantityBefore,
            'quantity_after' => $quantityAfter,
            'unit_cost' => $unitCost,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'reference_number' => $referenceNumber,
            'notes' => $notes,
        ]);
    }

    /**
     * إنشاء حركة إخراج
     */
    public static function createOutboundMovement(
        int $productId,
        int $warehouseId,
        int $quantity,
        float $unitCost = 0,
        string $referenceType = null,
        int $referenceId = null,
        string $referenceNumber = null,
        string $notes = null
    ): self {
        // الحصول على الكمية الحالية
        $currentStock = ProductStock::where('product_id', $productId)
                                   ->where('warehouse_id', $warehouseId)
                                   ->first();

        $quantityBefore = $currentStock ? $currentStock->quantity : 0;
        $quantityAfter = $quantityBefore - $quantity;

        return self::createMovement([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'movement_type' => 'out',
            'quantity' => -$quantity, // سالب للإخراج
            'quantity_before' => $quantityBefore,
            'quantity_after' => $quantityAfter,
            'unit_cost' => $unitCost,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'reference_number' => $referenceNumber,
            'notes' => $notes,
        ]);
    }

    /**
     * إنشاء حركة تحويل
     */
    public static function createTransferMovement(
        int $productId,
        int $fromWarehouseId,
        int $toWarehouseId,
        int $quantity,
        float $unitCost = 0,
        string $referenceNumber = null,
        string $notes = null
    ): array {
        $movements = [];

        // حركة إخراج من المخزن المصدر
        $movements[] = self::createMovement([
            'product_id' => $productId,
            'warehouse_id' => $fromWarehouseId,
            'movement_type' => 'transfer',
            'quantity' => -$quantity,
            'quantity_before' => 0, // سيتم تحديثه
            'quantity_after' => 0, // سيتم تحديثه
            'unit_cost' => $unitCost,
            'from_warehouse_id' => $fromWarehouseId,
            'to_warehouse_id' => $toWarehouseId,
            'reference_number' => $referenceNumber,
            'notes' => $notes,
        ]);

        // حركة إدخال للمخزن المستهدف
        $movements[] = self::createMovement([
            'product_id' => $productId,
            'warehouse_id' => $toWarehouseId,
            'movement_type' => 'transfer',
            'quantity' => $quantity,
            'quantity_before' => 0, // سيتم تحديثه
            'quantity_after' => 0, // سيتم تحديثه
            'unit_cost' => $unitCost,
            'from_warehouse_id' => $fromWarehouseId,
            'to_warehouse_id' => $toWarehouseId,
            'reference_number' => $referenceNumber,
            'notes' => $notes,
        ]);

        return $movements;
    }
}
