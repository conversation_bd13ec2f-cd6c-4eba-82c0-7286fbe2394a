<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SimpleTenant extends Model
{
    protected $fillable = [
        'company_name',
        'subdomain',
        'owner_name',
        'email',
        'phone',
        'city',
        'plan',
        'status',
        'trial_ends_at',
        'subscription_ends_at',
        'monthly_price',
        'settings',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'subscription_ends_at' => 'datetime',
        'settings' => 'array',
    ];

    /**
     * علاقة مع المستخدمين
     */
    public function users()
    {
        return $this->hasMany(User::class, 'tenant_id', 'id');
    }

    /**
     * المستخدم المالك
     */
    public function owner()
    {
        return $this->hasOne(User::class, 'tenant_id', 'id')->where('is_owner', true);
    }

    /**
     * التحقق من انتهاء الفترة التجريبية
     */
    public function isTrialExpired()
    {
        return $this->status === 'trial' && $this->trial_ends_at && $this->trial_ends_at->isPast();
    }

    /**
     * التحقق من نشاط الاشتراك
     */
    public function isActive()
    {
        return in_array($this->status, ['trial', 'active']) && !$this->isTrialExpired();
    }

    /**
     * الحصول على سعر الخطة
     */
    public function getPlanPrice()
    {
        $prices = [
            'basic' => 299,
            'premium' => 599,
            'enterprise' => 999,
        ];

        return $prices[$this->plan] ?? 599;
    }

    /**
     * الحصول على اسم الخطة
     */
    public function getPlanName()
    {
        $names = [
            'basic' => 'الخطة الأساسية',
            'premium' => 'الخطة المتقدمة',
            'enterprise' => 'الخطة المؤسسية',
        ];

        return $names[$this->plan] ?? 'الخطة المتقدمة';
    }
}
