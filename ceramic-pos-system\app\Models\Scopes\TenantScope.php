<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class TenantScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        // التحقق من وجود tenant في السياق الحالي
        if (tenancy()->initialized) {
            $builder->where($model->getTable() . '.tenant_id', tenant('id'));
        }
    }
}
