<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Product;
use App\Models\Customer;
use App\Models\SimpleTenant;
use Illuminate\Support\Facades\DB;

class InvoiceController extends Controller
{
    /**
     * عرض قائمة الفواتير
     */
    public function index(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $query = Invoice::with(['customer', 'items.product']);

        // فلترة حسب النوع
        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('invoice_date', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('invoice_date', '<=', $request->get('date_to'));
        }

        // البحث
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $invoices = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('invoices.index', compact('invoices', 'tenant'));
    }

    /**
     * عرض تفاصيل الفاتورة
     */
    public function show(Request $request, Invoice $invoice)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $invoice->load(['customer', 'items.product', 'createdBy', 'confirmedBy']);

        return view('invoices.show', compact('invoice', 'tenant'));
    }

    /**
     * طباعة الفاتورة
     */
    public function print(Request $request, Invoice $invoice)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $invoice->load(['customer', 'items.product']);

        return view('invoices.print', compact('invoice', 'tenant'));
    }

    /**
     * إنشاء فاتورة جديدة
     */
    public function create(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $customers = Customer::where('is_active', true)->get();
        $products = Product::where('is_active', true)->where('current_stock', '>', 0)->get();

        return view('invoices.create', compact('tenant', 'customers', 'products'));
    }

    /**
     * حفظ فاتورة جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'type' => 'required|in:sale,purchase,return_sale,return_purchase',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'payment_method' => 'required|in:cash,card,credit,bank_transfer',
            'notes' => 'nullable|string|max:1000',
        ]);

        DB::beginTransaction();

        try {
            // حساب الإجماليات
            $subtotal = 0;
            foreach ($request->items as $item) {
                $subtotal += $item['quantity'] * $item['unit_price'];
            }

            $discountAmount = $request->discount_amount ?? 0;
            $taxRate = $request->tax_rate ?? 0;
            $taxAmount = (($subtotal - $discountAmount) * $taxRate) / 100;
            $totalAmount = $subtotal - $discountAmount + $taxAmount;

            // إنشاء الفاتورة
            $invoice = Invoice::create([
                'invoice_number' => $this->generateInvoiceNumber($request->type),
                'customer_id' => $request->customer_id,
                'type' => $request->type,
                'status' => 'draft',
                'invoice_date' => $request->invoice_date ?? now(),
                'due_date' => $request->due_date ?? now()->addDays(30),
                'subtotal' => $subtotal,
                'discount_amount' => $discountAmount,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'payment_status' => 'pending',
                'notes' => $request->notes,
                'created_by' => auth()->id(),
            ]);

            // إضافة عناصر الفاتورة
            foreach ($request->items as $item) {
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['quantity'] * $item['unit_price'],
                ]);
            }

            DB::commit();

            return redirect()->route('invoices.show', ['invoice' => $invoice->id, 'tenant' => $request->get('tenant')])
                ->with('success', 'تم إنشاء الفاتورة بنجاح');

        } catch (\Exception $e) {
            DB::rollback();

            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * توليد رقم فاتورة جديد
     */
    private function generateInvoiceNumber($type)
    {
        $prefix = [
            'sale' => 'SAL',
            'purchase' => 'PUR',
            'return_sale' => 'RSL',
            'return_purchase' => 'RPR',
        ][$type] ?? 'INV';

        $lastInvoice = Invoice::where('type', $type)
            ->whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastNumber = intval(substr($lastInvoice->invoice_number, -4));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . '-' . date('Ymd') . '-' . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
