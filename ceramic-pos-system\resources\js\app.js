import './bootstrap';
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Alpine from 'alpinejs';

// Import Vue components
import DashboardWidget from './components/DashboardWidget.vue'
import ChartComponent from './components/ChartComponent.vue'
import DataTable from './components/DataTable.vue'
import SearchComponent from './components/SearchComponent.vue'
import NotificationComponent from './components/NotificationComponent.vue'
import ModalComponent from './components/ModalComponent.vue'

window.Alpine = Alpine;
Alpine.start();

// Create Vue app if element exists
if (document.getElementById('vue-app')) {
    const app = createApp({})
    const pinia = createPinia()

    // Register global components
    app.component('DashboardWidget', DashboardWidget)
    app.component('ChartComponent', ChartComponent)
    app.component('DataTable', DataTable)
    app.component('SearchComponent', SearchComponent)
    app.component('NotificationComponent', NotificationComponent)
    app.component('ModalComponent', ModalComponent)

    // Use Pinia for state management
    app.use(pinia)

    // Global properties
    app.config.globalProperties.$http = window.axios
    app.config.globalProperties.$formatCurrency = (amount, currency = 'EGP') => {
        const symbols = {
            'EGP': 'ج.م',
            'USD': '$',
            'EUR': '€',
            'SAR': 'ر.س',
            'AED': 'د.إ'
        }

        const symbol = symbols[currency] || currency
        return new Intl.NumberFormat('ar-EG').format(amount) + ' ' + symbol
    }

    app.config.globalProperties.$formatNumber = (number, decimals = 2) => {
        return new Intl.NumberFormat('ar-EG', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number)
    }

    app.config.globalProperties.$formatDate = (date, format = 'short') => {
        const options = {
            short: { year: 'numeric', month: '2-digit', day: '2-digit' },
            long: { year: 'numeric', month: 'long', day: 'numeric' },
            datetime: {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            }
        }

        return new Intl.DateTimeFormat('ar-EG', options[format] || options.short).format(new Date(date))
    }

    // Mount the app
    app.mount('#vue-app')

    // Export for use in other files
    window.Vue = app
}
