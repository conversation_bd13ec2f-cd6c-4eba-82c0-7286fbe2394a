<template>
  <teleport to="body">
    <div class="notification-container">
      <transition-group name="notification" tag="div">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="getNotificationClass(notification)"
          class="notification"
        >
          <div class="notification-content">
            <div class="notification-icon">
              <i :class="getNotificationIcon(notification.type)"></i>
            </div>
            <div class="notification-body">
              <div class="notification-title" v-if="notification.title">
                {{ notification.title }}
              </div>
              <div class="notification-message">
                {{ notification.message }}
              </div>
            </div>
            <button
              class="notification-close"
              @click="removeNotification(notification.id)"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div
            v-if="notification.duration > 0"
            class="notification-progress"
            :style="{ animationDuration: notification.duration + 'ms' }"
          ></div>
        </div>
      </transition-group>
    </div>
  </teleport>
</template>

<script>
export default {
  name: 'NotificationComponent',
  data() {
    return {
      notifications: []
    }
  },
  methods: {
    show(options) {
      const notification = {
        id: Date.now() + Math.random(),
        type: options.type || 'info',
        title: options.title || '',
        message: options.message || '',
        duration: options.duration !== undefined ? options.duration : 5000,
        persistent: options.persistent || false
      }

      this.notifications.push(notification)

      if (notification.duration > 0 && !notification.persistent) {
        setTimeout(() => {
          this.removeNotification(notification.id)
        }, notification.duration)
      }

      return notification.id
    },

    success(message, title = '', options = {}) {
      return this.show({
        type: 'success',
        title,
        message,
        ...options
      })
    },

    error(message, title = '', options = {}) {
      return this.show({
        type: 'error',
        title,
        message,
        duration: 0, // Don't auto-hide errors
        ...options
      })
    },

    warning(message, title = '', options = {}) {
      return this.show({
        type: 'warning',
        title,
        message,
        ...options
      })
    },

    info(message, title = '', options = {}) {
      return this.show({
        type: 'info',
        title,
        message,
        ...options
      })
    },

    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },

    clear() {
      this.notifications = []
    },

    getNotificationClass(notification) {
      return [
        'alert',
        `alert-${this.getBootstrapType(notification.type)}`,
        'alert-dismissible'
      ]
    },

    getNotificationIcon(type) {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[type] || icons.info
    },

    getBootstrapType(type) {
      const types = {
        success: 'success',
        error: 'danger',
        warning: 'warning',
        info: 'info'
      }
      return types[type] || 'info'
    }
  },
  mounted() {
    // Make notification methods globally available
    window.$notify = {
      show: this.show,
      success: this.success,
      error: this.error,
      warning: this.warning,
      info: this.info,
      clear: this.clear
    }

    // Listen for Laravel flash messages
    const flashSuccess = document.querySelector('meta[name="flash-success"]')
    const flashError = document.querySelector('meta[name="flash-error"]')
    const flashWarning = document.querySelector('meta[name="flash-warning"]')
    const flashInfo = document.querySelector('meta[name="flash-info"]')

    if (flashSuccess) {
      this.success(flashSuccess.getAttribute('content'))
    }
    if (flashError) {
      this.error(flashError.getAttribute('content'))
    }
    if (flashWarning) {
      this.warning(flashWarning.getAttribute('content'))
    }
    if (flashInfo) {
      this.info(flashInfo.getAttribute('content'))
    }
  }
}
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  width: 100%;
}

.notification {
  margin-bottom: 10px;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
}

.notification-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.notification-body {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.95rem;
}

.notification-message {
  font-size: 0.9rem;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1rem;
  opacity: 0.7;
  cursor: pointer;
  padding: 0;
  margin-left: 0.5rem;
  margin-top: 0.125rem;
}

.notification-close:hover {
  opacity: 1;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  animation: progress linear;
  transform-origin: left;
}

@keyframes progress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* Transition animations */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.notification-move {
  transition: transform 0.3s ease;
}

/* Alert variants */
.alert-success {
  background-color: #d1e7dd;
  border-color: #badbcc;
  color: #0f5132;
}

.alert-success .notification-icon {
  color: #198754;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c2c7;
  color: #842029;
}

.alert-danger .notification-icon {
  color: #dc3545;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: #ffecb5;
  color: #664d03;
}

.alert-warning .notification-icon {
  color: #ffc107;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #b6effb;
  color: #055160;
}

.alert-info .notification-icon {
  color: #0dcaf0;
}

/* Responsive */
@media (max-width: 576px) {
  .notification-container {
    right: 10px;
    left: 10px;
    max-width: none;
  }
}
</style>
