<template>
  <div class="card" :class="widgetClass">
    <div class="card-body">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h6 class="card-title text-muted mb-1">{{ title }}</h6>
          <h3 class="mb-0" :class="valueClass">
            <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
            {{ formattedValue }}
          </h3>
          <small v-if="subtitle" class="text-muted">{{ subtitle }}</small>
        </div>
        <div class="widget-icon">
          <i :class="icon" :style="{ color: iconColor }"></i>
        </div>
      </div>
      
      <div v-if="trend" class="mt-3">
        <span class="badge" :class="trendClass">
          <i :class="trendIcon"></i>
          {{ trend.percentage }}%
        </span>
        <span class="text-muted ms-2">{{ trend.label }}</span>
      </div>
      
      <div v-if="showChart && chartData" class="mt-3">
        <canvas :ref="chartRef" height="60"></canvas>
      </div>
    </div>
  </div>
</template>

<script>
import { Chart, registerables } from 'chart.js'

Chart.register(...registerables)

export default {
  name: 'DashboardWidget',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: 'fas fa-chart-line'
    },
    iconColor: {
      type: String,
      default: '#007bff'
    },
    type: {
      type: String,
      default: 'number', // number, currency, percentage
      validator: value => ['number', 'currency', 'percentage'].includes(value)
    },
    currency: {
      type: String,
      default: 'EGP'
    },
    trend: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    variant: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
    },
    showChart: {
      type: Boolean,
      default: false
    },
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      chartRef: `chart-${Math.random().toString(36).substr(2, 9)}`
    }
  },
  computed: {
    formattedValue() {
      if (this.loading) return '...'
      
      switch (this.type) {
        case 'currency':
          return this.$formatCurrency(this.value, this.currency)
        case 'percentage':
          return `${this.value}%`
        default:
          return this.$formatNumber(this.value)
      }
    },
    widgetClass() {
      return `border-start border-${this.variant} border-4`
    },
    valueClass() {
      return `text-${this.variant}`
    },
    trendClass() {
      if (!this.trend) return ''
      
      const direction = this.trend.direction || 'neutral'
      return {
        'badge-success': direction === 'up',
        'badge-danger': direction === 'down',
        'badge-secondary': direction === 'neutral'
      }
    },
    trendIcon() {
      if (!this.trend) return ''
      
      const direction = this.trend.direction || 'neutral'
      return {
        'fas fa-arrow-up': direction === 'up',
        'fas fa-arrow-down': direction === 'down',
        'fas fa-minus': direction === 'neutral'
      }
    }
  },
  mounted() {
    if (this.showChart && this.chartData.length > 0) {
      this.initChart()
    }
  },
  watch: {
    chartData: {
      handler() {
        if (this.showChart && this.chartData.length > 0) {
          this.updateChart()
        }
      },
      deep: true
    }
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.destroy()
    }
  },
  methods: {
    initChart() {
      const ctx = this.$refs[this.chartRef]
      if (!ctx) return

      this.chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.chartData.map((_, index) => index + 1),
          datasets: [{
            data: this.chartData,
            borderColor: this.iconColor,
            backgroundColor: this.iconColor + '20',
            borderWidth: 2,
            fill: true,
            tension: 0.4,
            pointRadius: 0,
            pointHoverRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              display: false
            },
            y: {
              display: false
            }
          },
          interaction: {
            intersect: false,
            mode: 'index'
          }
        }
      })
    },
    updateChart() {
      if (!this.chart) {
        this.initChart()
        return
      }

      this.chart.data.labels = this.chartData.map((_, index) => index + 1)
      this.chart.data.datasets[0].data = this.chartData
      this.chart.update()
    }
  }
}
</script>

<style scoped>
.widget-icon {
  font-size: 2rem;
  opacity: 0.7;
}

.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.badge-success {
  background-color: #28a745;
}

.badge-danger {
  background-color: #dc3545;
}

.badge-secondary {
  background-color: #6c757d;
}
</style>
