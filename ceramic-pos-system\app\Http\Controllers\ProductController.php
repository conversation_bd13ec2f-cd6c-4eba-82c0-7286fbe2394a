<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\SimpleTenant;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * عرض قائمة المنتجات
     */
    public function index(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        $query = Product::query();

        // البحث
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        // فلترة حسب الفئة
        if ($request->filled('category')) {
            $query->where('category_id', $request->get('category'));
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('is_active', $request->get('status') === 'active');
        }

        $products = $query->with('category')->paginate(20);
        $categories = Category::all();

        return view('inventory.products.index', compact('products', 'categories', 'tenant'));
    }

    /**
     * عرض نموذج إضافة منتج جديد
     */
    public function create(Request $request)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);
        $categories = Category::all();

        return view('inventory.products.create', compact('categories', 'tenant'));
    }

    /**
     * حفظ منتج جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:products',
            'barcode' => 'nullable|string|max:50|unique:products',
            'category_id' => 'required|exists:categories,id',
            'unit' => 'required|string|max:50',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'min_stock' => 'required|integer|min:0',
            'current_stock' => 'required|integer|min:0',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'name.required' => 'اسم المنتج مطلوب',
            'code.required' => 'كود المنتج مطلوب',
            'code.unique' => 'كود المنتج موجود بالفعل',
            'barcode.unique' => 'الباركود موجود بالفعل',
            'category_id.required' => 'فئة المنتج مطلوبة',
            'unit.required' => 'وحدة القياس مطلوبة',
            'cost_price.required' => 'سعر التكلفة مطلوب',
            'selling_price.required' => 'سعر البيع مطلوب',
            'min_stock.required' => 'الحد الأدنى للمخزون مطلوب',
            'current_stock.required' => 'المخزون الحالي مطلوب',
        ]);

        $data = $request->all();

        // رفع الصورة
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('products', 'public');
        }

        Product::create($data);

        return redirect()->route('inventory.products.index', ['tenant' => $request->get('tenant')])
            ->with('success', 'تم إضافة المنتج بنجاح');
    }

    /**
     * عرض تفاصيل المنتج
     */
    public function show(Request $request, Product $product)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);

        return view('inventory.products.show', compact('product', 'tenant'));
    }

    /**
     * عرض نموذج تعديل المنتج
     */
    public function edit(Request $request, Product $product)
    {
        $tenantId = $request->get('tenant');
        $tenant = SimpleTenant::findOrFail($tenantId);
        $categories = Category::all();

        return view('inventory.products.edit', compact('product', 'categories', 'tenant'));
    }

    /**
     * تحديث المنتج
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:products,code,' . $product->id,
            'barcode' => 'nullable|string|max:50|unique:products,barcode,' . $product->id,
            'category_id' => 'required|exists:categories,id',
            'unit' => 'required|string|max:50',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'min_stock' => 'required|integer|min:0',
            'current_stock' => 'required|integer|min:0',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->all();

        // رفع الصورة الجديدة
        if ($request->hasFile('image')) {
            // حذف الصورة القديمة
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }
            $data['image'] = $request->file('image')->store('products', 'public');
        }

        $product->update($data);

        return redirect()->route('inventory.products.index', ['tenant' => $request->get('tenant')])
            ->with('success', 'تم تحديث المنتج بنجاح');
    }

    /**
     * حذف المنتج
     */
    public function destroy(Request $request, Product $product)
    {
        // حذف الصورة
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        $product->delete();

        return redirect()->route('inventory.products.index', ['tenant' => $request->get('tenant')])
            ->with('success', 'تم حذف المنتج بنجاح');
    }

    /**
     * البحث في المنتجات (للـ API)
     */
    public function search(Request $request)
    {
        $search = $request->get('q');

        $products = Product::where('name', 'like', "%{$search}%")
            ->orWhere('code', 'like', "%{$search}%")
            ->orWhere('barcode', 'like', "%{$search}%")
            ->where('is_active', true)
            ->limit(10)
            ->get(['id', 'name', 'code', 'barcode', 'selling_price', 'current_stock']);

        return response()->json($products);
    }
}
