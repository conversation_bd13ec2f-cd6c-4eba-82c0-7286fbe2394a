<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('taxes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('tax_code', 20)->unique(); // رمز الضريبة
            $table->string('name'); // اسم الضريبة
            $table->string('name_en')->nullable(); // اسم الضريبة بالإنجليزية
            $table->enum('type', ['percentage', 'fixed'])->default('percentage'); // نوع الضريبة
            $table->decimal('rate', 5, 2)->default(0); // معدل الضريبة
            $table->decimal('amount', 10, 2)->nullable(); // مبلغ ثابت (للضريبة الثابتة)
            $table->boolean('is_inclusive')->default(false); // ضريبة شاملة أم إضافية
            $table->boolean('is_default')->default(false); // ضريبة افتراضية
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index(['tenant_id', 'tax_code']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['tenant_id', 'is_default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('taxes');
    }
};
