<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\DashboardService;

class DashboardMiddleware
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // إضافة بيانات عامة للوحة التحكم
        if ($request->user()) {
            // إحصائيات سريعة للشريط العلوي
            $quickStats = $this->dashboardService->getComprehensiveStats('today');

            // مشاركة البيانات مع جميع الـ views
            view()->share('dashboardQuickStats', $quickStats);

            // إضافة التنبيهات للشريط الجانبي
            $alerts = [
                'low_stock_count' => \App\Models\Product::lowStock()->count(),
                'overdue_invoices_count' => \App\Models\Invoice::overdue()->count(),
                'pending_payments_count' => \App\Models\Payment::pending()->count(),
            ];

            view()->share('dashboardAlerts', $alerts);
        }

        return $next($request);
    }
}
