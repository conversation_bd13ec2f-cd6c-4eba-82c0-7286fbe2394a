@extends('layouts.super-admin')

@section('title', 'إضافة مشترك جديد')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-plus me-2 text-success"></i>
                إضافة مشترك جديد
            </h2>
            <p class="text-muted mb-0">إنشاء حساب مشترك جديد في النظام</p>
        </div>
        <div>
            <a href="{{ route('super-admin.tenants') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للمشتركين
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        بيانات المشترك
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super-admin.tenants.store') }}">
                        @csrf
                        
                        <!-- Company Information -->
                        <h6 class="fw-bold mb-3 text-primary">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الشركة
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الشركة <span class="text-danger">*</span></label>
                                    <input type="text" name="company_name" class="form-control" 
                                           value="{{ old('company_name') }}" required>
                                    @error('company_name')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">النطاق الفرعي <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="text" name="subdomain" class="form-control" 
                                               value="{{ old('subdomain') }}" required>
                                        <span class="input-group-text">.ceramicpos.com</span>
                                    </div>
                                    <small class="text-muted">يجب أن يحتوي على أحرف وأرقام فقط</small>
                                    @error('subdomain')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المدينة <span class="text-danger">*</span></label>
                                    <input type="text" name="city" class="form-control" 
                                           value="{{ old('city') }}" required>
                                    @error('city')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                    <input type="text" name="phone" class="form-control" 
                                           value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Owner Information -->
                        <h6 class="fw-bold mb-3 text-primary">
                            <i class="fas fa-user me-2"></i>
                            معلومات المالك
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم المالك <span class="text-danger">*</span></label>
                                    <input type="text" name="owner_name" class="form-control" 
                                           value="{{ old('owner_name') }}" required>
                                    @error('owner_name')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                    <input type="email" name="email" class="form-control" 
                                           value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                    <input type="password" name="password" class="form-control" required>
                                    <small class="text-muted">يجب أن تكون 8 أحرف على الأقل</small>
                                    @error('password')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                    <input type="password" name="password_confirmation" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Subscription Information -->
                        <h6 class="fw-bold mb-3 text-primary">
                            <i class="fas fa-credit-card me-2"></i>
                            معلومات الاشتراك
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الخطة <span class="text-danger">*</span></label>
                                    <select name="plan" class="form-select" required>
                                        <option value="">اختر الخطة</option>
                                        <option value="basic" {{ old('plan') == 'basic' ? 'selected' : '' }}>
                                            الخطة الأساسية - 299 ج.م/شهر
                                        </option>
                                        <option value="premium" {{ old('plan') == 'premium' ? 'selected' : '' }}>
                                            الخطة المتقدمة - 599 ج.م/شهر
                                        </option>
                                        <option value="enterprise" {{ old('plan') == 'enterprise' ? 'selected' : '' }}>
                                            الخطة المؤسسية - 999 ج.م/شهر
                                        </option>
                                    </select>
                                    @error('plan')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">حالة الاشتراك <span class="text-danger">*</span></label>
                                    <select name="status" class="form-select" required>
                                        <option value="trial" {{ old('status') == 'trial' ? 'selected' : '' }}>
                                            فترة تجريبية (30 يوم)
                                        </option>
                                        <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>
                                            نشط
                                        </option>
                                        <option value="suspended" {{ old('status') == 'suspended' ? 'selected' : '' }}>
                                            معلق
                                        </option>
                                    </select>
                                    @error('status')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ انتهاء الاشتراك</label>
                                    <input type="date" name="subscription_ends_at" class="form-control" 
                                           value="{{ old('subscription_ends_at') }}">
                                    <small class="text-muted">اتركه فارغاً للاشتراكات التجريبية</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3">{{ old('notes') }}</textarea>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('super-admin.tenants') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                إنشاء المشترك
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Plan Information Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الخطط
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold text-secondary">الخطة الأساسية</h6>
                        <p class="text-success fw-bold">299 ج.م/شهر</p>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-2"></i>نقطة بيع واحدة</li>
                            <li><i class="fas fa-check text-success me-2"></i>إدارة المخزون الأساسية</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقارير أساسية</li>
                            <li><i class="fas fa-check text-success me-2"></i>حتى 1000 منتج</li>
                        </ul>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold text-primary">الخطة المتقدمة</h6>
                        <p class="text-success fw-bold">599 ج.م/شهر</p>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-2"></i>نقاط بيع متعددة</li>
                            <li><i class="fas fa-check text-success me-2"></i>إدارة مخزون متقدمة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقارير تفصيلية</li>
                            <li><i class="fas fa-check text-success me-2"></i>منتجات غير محدودة</li>
                            <li><i class="fas fa-check text-success me-2"></i>نظام المحاسبة</li>
                        </ul>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold text-warning">الخطة المؤسسية</h6>
                        <p class="text-success fw-bold">999 ج.م/شهر</p>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-2"></i>جميع مميزات الخطة المتقدمة</li>
                            <li><i class="fas fa-check text-success me-2"></i>API متقدم</li>
                            <li><i class="fas fa-check text-success me-2"></i>دعم مخصص 24/7</li>
                            <li><i class="fas fa-check text-success me-2"></i>تخصيص كامل</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle me-2"></i>
                            يمكنك تغيير حالة المشترك وخطته لاحقاً من صفحة إدارة المشتركين.
                        </small>
                    </div>
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تأكد من صحة البريد الإلكتروني حيث سيتم إرسال بيانات الدخول إليه.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Auto-generate subdomain from company name
document.querySelector('input[name="company_name"]').addEventListener('input', function() {
    const companyName = this.value;
    const subdomain = companyName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 20);
    
    document.querySelector('input[name="subdomain"]').value = subdomain;
});

// Plan price display
document.querySelector('select[name="plan"]').addEventListener('change', function() {
    const prices = {
        'basic': '299 ج.م/شهر',
        'premium': '599 ج.م/شهر', 
        'enterprise': '999 ج.م/شهر'
    };
    
    // You can add price display logic here if needed
});
</script>
@endpush
@endsection
