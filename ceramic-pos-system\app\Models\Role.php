<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Models\Traits\BelongsToTenant;

class Role extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'name',
        'display_name',
        'description',
        'permissions',
        'is_system_role',
        'is_active',
    ];

    protected $casts = [
        'permissions' => 'array',
        'is_system_role' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * الأدوار الافتراضية للنظام
     */
    const SYSTEM_ROLES = [
        'super_admin' => [
            'display_name' => 'مدير النظام',
            'description' => 'صلاحيات كاملة على جميع أجزاء النظام',
            'permissions' => ['*'],
        ],
        'admin' => [
            'display_name' => 'مدير',
            'description' => 'صلاحيات إدارية على معظم أجزاء النظام',
            'permissions' => [
                'dashboard.view',
                'customers.*',
                'suppliers.*',
                'products.*',
                'invoices.*',
                'payments.*',
                'reports.view',
                'users.view',
            ],
        ],
        'accountant' => [
            'display_name' => 'محاسب',
            'description' => 'صلاحيات محاسبية ومالية',
            'permissions' => [
                'dashboard.view',
                'invoices.*',
                'payments.*',
                'reports.*',
                'chart_of_accounts.view',
            ],
        ],
        'sales_manager' => [
            'display_name' => 'مدير مبيعات',
            'description' => 'إدارة المبيعات والعملاء',
            'permissions' => [
                'dashboard.view',
                'customers.*',
                'invoices.sales.*',
                'payments.received.*',
                'products.view',
                'reports.sales.*',
            ],
        ],
        'warehouse_manager' => [
            'display_name' => 'مدير مخزن',
            'description' => 'إدارة المخزون والمنتجات',
            'permissions' => [
                'dashboard.view',
                'products.*',
                'warehouses.*',
                'stock_movements.*',
                'reports.inventory.*',
            ],
        ],
        'cashier' => [
            'display_name' => 'أمين صندوق',
            'description' => 'إدارة المدفوعات والصندوق',
            'permissions' => [
                'dashboard.view',
                'payments.*',
                'invoices.view',
                'customers.view',
                'reports.payments.*',
            ],
        ],
        'employee' => [
            'display_name' => 'موظف',
            'description' => 'صلاحيات أساسية للموظفين',
            'permissions' => [
                'dashboard.view',
                'customers.view',
                'products.view',
                'invoices.view',
            ],
        ],
    ];

    /**
     * العلاقة مع المستخدمين
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'role_user')
                    ->withPivot(['assigned_at', 'assigned_by'])
                    ->withTimestamps();
    }

    /**
     * العلاقة مع الصلاحيات
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'permission_role')
                    ->withTimestamps();
    }

    /**
     * Scope للأدوار النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للأدوار النظام
     */
    public function scopeSystemRoles($query)
    {
        return $query->where('is_system_role', true);
    }

    /**
     * Scope للأدوار المخصصة
     */
    public function scopeCustomRoles($query)
    {
        return $query->where('is_system_role', false);
    }

    /**
     * التحقق من وجود صلاحية
     */
    public function hasPermission(string $permission): bool
    {
        // إذا كان الدور يحتوي على صلاحية شاملة
        if (in_array('*', $this->permissions ?? [])) {
            return true;
        }

        // التحقق من الصلاحية المحددة
        if (in_array($permission, $this->permissions ?? [])) {
            return true;
        }

        // التحقق من الصلاحيات بالنمط (مثل customers.*)
        foreach ($this->permissions ?? [] as $rolePermission) {
            if (str_ends_with($rolePermission, '*')) {
                $pattern = str_replace('*', '', $rolePermission);
                if (str_starts_with($permission, $pattern)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * إضافة صلاحية
     */
    public function addPermission(string $permission): void
    {
        $permissions = $this->permissions ?? [];

        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            $this->permissions = $permissions;
            $this->save();
        }
    }

    /**
     * إزالة صلاحية
     */
    public function removePermission(string $permission): void
    {
        $permissions = $this->permissions ?? [];

        if (($key = array_search($permission, $permissions)) !== false) {
            unset($permissions[$key]);
            $this->permissions = array_values($permissions);
            $this->save();
        }
    }

    /**
     * تحديث الصلاحيات
     */
    public function updatePermissions(array $permissions): void
    {
        $this->permissions = $permissions;
        $this->save();
    }

    /**
     * الحصول على عدد المستخدمين
     */
    public function getUsersCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * التحقق من إمكانية التعديل
     */
    public function canEdit(): bool
    {
        return !$this->is_system_role;
    }

    /**
     * التحقق من إمكانية الحذف
     */
    public function canDelete(): bool
    {
        return !$this->is_system_role && $this->users()->count() === 0;
    }

    /**
     * إنشاء الأدوار الافتراضية
     */
    public static function createSystemRoles(string $tenantId): void
    {
        foreach (self::SYSTEM_ROLES as $roleName => $roleData) {
            self::updateOrCreate(
                [
                    'tenant_id' => $tenantId,
                    'name' => $roleName,
                ],
                [
                    'display_name' => $roleData['display_name'],
                    'description' => $roleData['description'],
                    'permissions' => $roleData['permissions'],
                    'is_system_role' => true,
                    'is_active' => true,
                ]
            );
        }
    }
}
