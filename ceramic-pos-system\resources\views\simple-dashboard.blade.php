<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>لوحة التحكم - نظام إدارة السيراميك</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-cube me-2"></i>
                Ceramic POS
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    مرحباً، {{ $user->name }}
                </span>
                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        تسجيل الخروج
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="welcome-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-3">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تسجيل الدخول بنجاح!
                    </h1>
                    <p class="lead mb-0">
                        مرحباً {{ $user->name }}، أهلاً بك في نظام إدارة السيراميك
                    </p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-user-check" style="font-size: 4rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>

        <!-- User Info -->
        <div class="info-card">
            <h4 class="mb-3">
                <i class="fas fa-user me-2"></i>
                معلومات المستخدم
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>الاسم:</strong> {{ $user->name }}</p>
                    <p><strong>البريد الإلكتروني:</strong> {{ $user->email }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>تاريخ التسجيل:</strong> {{ $user->created_at->format('Y-m-d') }}</p>
                    <p><strong>الحالة:</strong> 
                        @if($user->is_active)
                            <span class="badge bg-success">نشط</span>
                        @else
                            <span class="badge bg-danger">غير نشط</span>
                        @endif
                    </p>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="info-card">
            <h4 class="mb-3">
                <i class="fas fa-cogs me-2"></i>
                حالة النظام
            </h4>
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <i class="fas fa-database fa-2x text-success mb-2"></i>
                        <p class="mb-0"><strong>قاعدة البيانات</strong></p>
                        <span class="badge bg-success">متصلة</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <i class="fas fa-server fa-2x text-success mb-2"></i>
                        <p class="mb-0"><strong>الخادم</strong></p>
                        <span class="badge bg-success">يعمل</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                        <p class="mb-0"><strong>الأمان</strong></p>
                        <span class="badge bg-success">آمن</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="info-card">
            <h4 class="mb-3">
                <i class="fas fa-bolt me-2"></i>
                إجراءات سريعة
            </h4>
            <div class="row">
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-success w-100">
                        <i class="fas fa-file-invoice me-2"></i>
                        فاتورة جديدة
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-info w-100">
                        <i class="fas fa-users me-2"></i>
                        إدارة العملاء
                    </a>
                </div>
                <div class="col-md-3 mb-3">
                    <a href="#" class="btn btn-warning w-100">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                </div>
            </div>
        </div>

        @if(isset($error))
        <!-- Error Info (if any) -->
        <div class="alert alert-warning">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ملاحظة تقنية
            </h5>
            <p class="mb-0">
                تم تحميل صفحة لوحة التحكم البديلة بسبب: {{ $error }}
            </p>
        </div>
        @endif

        <!-- Success Message -->
        <div class="alert alert-success">
            <h5 class="alert-heading">
                <i class="fas fa-thumbs-up me-2"></i>
                تم تسجيل الدخول بنجاح!
            </h5>
            <p class="mb-0">
                النظام يعمل بشكل طبيعي. يمكنك الآن البدء في استخدام جميع مميزات النظام.
            </p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
