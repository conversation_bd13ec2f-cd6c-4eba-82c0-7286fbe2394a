<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $paymentTypes = ['received', 'paid'];
        $paymentMethods = ['cash', 'bank_transfer', 'check', 'card'];
        $statuses = ['pending', 'cleared'];
        $currencies = ['EGP', 'USD', 'EUR'];

        $paymentType = $this->faker->randomElement($paymentTypes);
        $paymentDate = $this->faker->dateTimeBetween('-3 months', 'now');
        $amount = $this->faker->randomFloat(2, 100, 10000);
        $currency = $this->faker->randomElement($currencies);
        $exchangeRate = $currency === 'EGP' ? 1 : $this->faker->randomFloat(4, 0.5, 2);

        return [
            'tenant_id' => 1, // سيتم تحديثه عند الاستخدام
            'payment_number' => $this->generatePaymentNumber($paymentType),
            'payment_type' => $paymentType,
            'payment_date' => $paymentDate,
            'customer_id' => $paymentType === 'received' ? 1 : null, // سيتم تحديثه
            'supplier_id' => $paymentType === 'paid' ? 1 : null, // سيتم تحديثه
            'payer_name' => $this->faker->name(),
            'payment_method' => $this->faker->randomElement($paymentMethods),
            'amount' => $amount,
            'currency' => $currency,
            'exchange_rate' => $exchangeRate,
            'amount_in_base_currency' => $amount * $exchangeRate,
            'bank_name' => $this->faker->optional()->company() . ' Bank',
            'account_number' => $this->faker->optional()->numerify('##########'),
            'check_number' => $this->faker->optional()->numerify('######'),
            'check_date' => $this->faker->optional()->dateTimeBetween($paymentDate, '+30 days'),
            'reference_number' => $this->faker->optional()->bothify('REF-####-????'),
            'transaction_id' => $this->faker->optional()->uuid(),
            'status' => $this->faker->randomElement($statuses),
            'cleared_date' => $this->faker->optional()->dateTimeBetween($paymentDate, '+7 days'),
            'notes' => $this->faker->optional()->sentence(),
            'created_by' => 1, // سيتم تحديثه
            'cleared_at' => $this->faker->optional()->dateTimeBetween($paymentDate, '+7 days'),
            'cleared_by' => 1, // سيتم تحديثه
            'created_at' => $paymentDate,
            'updated_at' => $paymentDate,
        ];
    }

    /**
     * إنشاء رقم مدفوعة
     */
    private function generatePaymentNumber(string $type): string
    {
        $prefix = $type === 'received' ? 'REC' : 'PAY';
        $year = date('Y');
        $month = date('m');
        $number = $this->faker->unique()->numberBetween(1, 9999);

        return sprintf('%s-%s%s%04d', $prefix, $year, $month, $number);
    }

    /**
     * مقبوضات
     */
    public function received()
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_type' => 'received',
                'payment_number' => $this->generatePaymentNumber('received'),
                'supplier_id' => null,
            ];
        });
    }

    /**
     * مدفوعات
     */
    public function paid()
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_type' => 'paid',
                'payment_number' => $this->generatePaymentNumber('paid'),
                'customer_id' => null,
            ];
        });
    }

    /**
     * مدفوعة مقاصة
     */
    public function cleared()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'cleared',
                'cleared_date' => now(),
                'cleared_at' => now(),
                'cleared_by' => 1,
            ];
        });
    }

    /**
     * مدفوعة معلقة
     */
    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'cleared_date' => null,
                'cleared_at' => null,
                'cleared_by' => null,
            ];
        });
    }

    /**
     * مدفوعة نقدية
     */
    public function cash()
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'cash',
                'bank_name' => null,
                'account_number' => null,
                'check_number' => null,
                'check_date' => null,
            ];
        });
    }

    /**
     * مدفوعة بنكية
     */
    public function bankTransfer()
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'bank_transfer',
                'bank_name' => $this->faker->company() . ' Bank',
                'account_number' => $this->faker->numerify('##########'),
                'check_number' => null,
                'check_date' => null,
            ];
        });
    }

    /**
     * مدفوعة بشيك
     */
    public function check()
    {
        return $this->state(function (array $attributes) {
            return [
                'payment_method' => 'check',
                'check_number' => $this->faker->numerify('######'),
                'check_date' => $this->faker->dateTimeBetween('now', '+30 days'),
                'bank_name' => $this->faker->company() . ' Bank',
            ];
        });
    }
}
