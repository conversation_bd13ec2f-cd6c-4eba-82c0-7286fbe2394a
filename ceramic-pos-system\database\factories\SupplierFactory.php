<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Supplier>
 */
class SupplierFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $supplierTypes = ['individual', 'company'];
        $balanceTypes = ['debit', 'credit'];
        $countries = ['EG', 'SA', 'AE', 'CN', 'TR', 'IT', 'ES'];

        $supplierType = $this->faker->randomElement($supplierTypes);
        $isCompany = $supplierType === 'company';

        return [
            'tenant_id' => 1, // سيتم تحديثه عند الاستخدام
            'supplier_code' => 'S' . $this->faker->unique()->numberBetween(1000, 9999),
            'name' => $this->faker->name(),
            'company_name' => $isCompany ? $this->faker->company() : null,
            'supplier_type' => $supplierType,
            'tax_number' => $isCompany ? $this->faker->numerify('###########') : null,
            'commercial_register' => $isCompany ? $this->faker->numerify('########') : null,
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'mobile' => $this->faker->phoneNumber(),
            'fax' => $this->faker->optional()->phoneNumber(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'postal_code' => $this->faker->postcode(),
            'country' => $this->faker->randomElement($countries),
            'payment_terms' => $this->faker->randomElement([15, 30, 45, 60]),
            'opening_balance' => $this->faker->randomFloat(2, 0, 20000),
            'current_balance' => $this->faker->randomFloat(2, 0, 20000),
            'balance_type' => $this->faker->randomElement($balanceTypes),
            'contact_person' => $isCompany ? $this->faker->name() : null,
            'contact_person_phone' => $isCompany ? $this->faker->phoneNumber() : null,
            'bank_name' => $this->faker->optional()->company() . ' Bank',
            'bank_account' => $this->faker->optional()->numerify('##########'),
            'iban' => $this->faker->optional()->iban(),
            'is_active' => $this->faker->boolean(95), // 95% نشط
            'notes' => $this->faker->optional()->sentence(),
            'additional_info' => null,
        ];
    }

    /**
     * مورد نشط
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    /**
     * مورد غير نشط
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * مورد فرد
     */
    public function individual()
    {
        return $this->state(function (array $attributes) {
            return [
                'supplier_type' => 'individual',
                'company_name' => null,
                'tax_number' => null,
                'commercial_register' => null,
                'contact_person' => null,
                'contact_person_phone' => null,
            ];
        });
    }

    /**
     * مورد شركة
     */
    public function company()
    {
        return $this->state(function (array $attributes) {
            return [
                'supplier_type' => 'company',
                'company_name' => $this->faker->company(),
                'tax_number' => $this->faker->numerify('###########'),
                'commercial_register' => $this->faker->numerify('########'),
                'contact_person' => $this->faker->name(),
                'contact_person_phone' => $this->faker->phoneNumber(),
            ];
        });
    }

    /**
     * مورد مدين
     */
    public function debit()
    {
        return $this->state(function (array $attributes) {
            return [
                'balance_type' => 'debit',
                'current_balance' => $this->faker->randomFloat(2, 100, 3000),
            ];
        });
    }

    /**
     * مورد دائن (مستحق الدفع)
     */
    public function credit()
    {
        return $this->state(function (array $attributes) {
            return [
                'balance_type' => 'credit',
                'current_balance' => $this->faker->randomFloat(2, 500, 15000),
            ];
        });
    }

    /**
     * مورد مستحق الدفع
     */
    public function payable()
    {
        return $this->state(function (array $attributes) {
            return [
                'balance_type' => 'credit',
                'current_balance' => $this->faker->randomFloat(2, 1000, 20000),
            ];
        });
    }

    /**
     * مورد محلي
     */
    public function local()
    {
        return $this->state(function (array $attributes) {
            return [
                'country' => 'EG',
            ];
        });
    }

    /**
     * مورد أجنبي
     */
    public function foreign()
    {
        $foreignCountries = ['CN', 'TR', 'IT', 'ES', 'SA', 'AE'];

        return $this->state(function (array $attributes) use ($foreignCountries) {
            return [
                'country' => $this->faker->randomElement($foreignCountries),
            ];
        });
    }
}
