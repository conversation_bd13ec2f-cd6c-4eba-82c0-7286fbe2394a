<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ChartOfAccount;
use Illuminate\Support\Facades\DB;

class ChartOfAccountController extends Controller
{
    /**
     * عرض قائمة الحسابات
     */
    public function index(Request $request)
    {
        $query = ChartOfAccount::with('parent', 'children');

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // التصفية حسب النوع
        if ($request->filled('account_type')) {
            $query->ofType($request->account_type);
        }

        // التصفية حسب الفئة
        if ($request->filled('account_category')) {
            $query->ofCategory($request->account_category);
        }

        // التصفية حسب الطبيعة
        if ($request->filled('nature')) {
            $query->ofNature($request->nature);
        }

        // التصفية حسب الحالة
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // الترتيب
        $query->orderBy('account_code');

        $accounts = $query->paginate(20);

        // إحصائيات
        $stats = [
            'total_accounts' => ChartOfAccount::count(),
            'active_accounts' => ChartOfAccount::active()->count(),
            'assets_accounts' => ChartOfAccount::ofType('assets')->count(),
            'liabilities_accounts' => ChartOfAccount::ofType('liabilities')->count(),
            'revenue_accounts' => ChartOfAccount::ofType('revenue')->count(),
            'expense_accounts' => ChartOfAccount::ofType('expenses')->count(),
        ];

        return view('tenant.chart-of-accounts.index', compact('accounts', 'stats'));
    }

    /**
     * عرض شجرة الحسابات
     */
    public function tree()
    {
        $rootAccounts = ChartOfAccount::roots()
            ->with('allChildren')
            ->orderBy('account_code')
            ->get();

        return view('tenant.chart-of-accounts.tree', compact('rootAccounts'));
    }

    /**
     * عرض نموذج إنشاء حساب جديد
     */
    public function create()
    {
        $parentAccounts = ChartOfAccount::active()
            ->orderBy('account_code')
            ->get();

        return view('tenant.chart-of-accounts.create', compact('parentAccounts'));
    }

    /**
     * حفظ حساب جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'account_code' => 'required|string|max:20|unique:chart_of_accounts,account_code',
            'account_name' => 'required|string|max:255',
            'account_name_en' => 'nullable|string|max:255',
            'account_type' => 'required|in:' . implode(',', array_keys(ChartOfAccount::ACCOUNT_TYPES)),
            'account_category' => 'required|in:' . implode(',', array_keys(ChartOfAccount::ACCOUNT_CATEGORIES)),
            'parent_id' => 'nullable|exists:chart_of_accounts,id',
            'nature' => 'required|in:debit,credit',
            'opening_balance' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
        ], [
            'account_code.required' => 'رقم الحساب مطلوب',
            'account_code.unique' => 'رقم الحساب مستخدم من قبل',
            'account_name.required' => 'اسم الحساب مطلوب',
            'account_type.required' => 'نوع الحساب مطلوب',
            'account_category.required' => 'فئة الحساب مطلوبة',
            'nature.required' => 'طبيعة الحساب مطلوبة',
        ]);

        DB::beginTransaction();

        try {
            $account = ChartOfAccount::create([
                'account_code' => $request->account_code,
                'account_name' => $request->account_name,
                'account_name_en' => $request->account_name_en,
                'account_type' => $request->account_type,
                'account_category' => $request->account_category,
                'parent_id' => $request->parent_id,
                'nature' => $request->nature,
                'opening_balance' => $request->opening_balance ?? 0,
                'current_balance' => $request->opening_balance ?? 0,
                'description' => $request->description,
                'is_active' => true,
            ]);

            // تحديث المستوى والمسار
            $account->updateLevel();
            $account->updatePath();

            DB::commit();

            return redirect()->route('tenant.chart-of-accounts.index')
                ->with('success', 'تم إنشاء الحساب بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء الحساب: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل الحساب
     */
    public function show(ChartOfAccount $chartOfAccount)
    {
        $chartOfAccount->load('parent', 'children');

        return view('tenant.chart-of-accounts.show', compact('chartOfAccount'));
    }

    /**
     * عرض نموذج تعديل الحساب
     */
    public function edit(ChartOfAccount $chartOfAccount)
    {
        // منع تعديل حسابات النظام
        if ($chartOfAccount->is_system) {
            return redirect()->back()
                ->with('error', 'لا يمكن تعديل حسابات النظام');
        }

        $parentAccounts = ChartOfAccount::active()
            ->where('id', '!=', $chartOfAccount->id)
            ->orderBy('account_code')
            ->get();

        return view('tenant.chart-of-accounts.edit', compact('chartOfAccount', 'parentAccounts'));
    }

    /**
     * تحديث الحساب
     */
    public function update(Request $request, ChartOfAccount $chartOfAccount)
    {
        // منع تعديل حسابات النظام
        if ($chartOfAccount->is_system) {
            return redirect()->back()
                ->with('error', 'لا يمكن تعديل حسابات النظام');
        }

        $request->validate([
            'account_code' => 'required|string|max:20|unique:chart_of_accounts,account_code,' . $chartOfAccount->id,
            'account_name' => 'required|string|max:255',
            'account_name_en' => 'nullable|string|max:255',
            'account_type' => 'required|in:' . implode(',', array_keys(ChartOfAccount::ACCOUNT_TYPES)),
            'account_category' => 'required|in:' . implode(',', array_keys(ChartOfAccount::ACCOUNT_CATEGORIES)),
            'parent_id' => 'nullable|exists:chart_of_accounts,id',
            'nature' => 'required|in:debit,credit',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        // التحقق من عدم جعل الحساب ابن لنفسه
        if ($request->parent_id == $chartOfAccount->id) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'لا يمكن جعل الحساب ابن لنفسه');
        }

        DB::beginTransaction();

        try {
            $chartOfAccount->update([
                'account_code' => $request->account_code,
                'account_name' => $request->account_name,
                'account_name_en' => $request->account_name_en,
                'account_type' => $request->account_type,
                'account_category' => $request->account_category,
                'parent_id' => $request->parent_id,
                'nature' => $request->nature,
                'description' => $request->description,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // تحديث المستوى والمسار
            $chartOfAccount->updateLevel();
            $chartOfAccount->updatePath();

            DB::commit();

            return redirect()->route('tenant.chart-of-accounts.index')
                ->with('success', 'تم تحديث الحساب بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث الحساب: ' . $e->getMessage());
        }
    }

    /**
     * حذف الحساب
     */
    public function destroy(ChartOfAccount $chartOfAccount)
    {
        // منع حذف حسابات النظام
        if ($chartOfAccount->is_system) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف حسابات النظام');
        }

        // منع حذف الحسابات التي لها حسابات فرعية
        if ($chartOfAccount->children()->count() > 0) {
            return redirect()->back()
                ->with('error', 'لا يمكن حذف حساب له حسابات فرعية');
        }

        // TODO: التحقق من عدم وجود حركات على الحساب

        try {
            $chartOfAccount->delete();

            return redirect()->route('tenant.chart-of-accounts.index')
                ->with('success', 'تم حذف الحساب بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف الحساب: ' . $e->getMessage());
        }
    }

    /**
     * تفعيل/إلغاء تفعيل الحساب
     */
    public function toggleStatus(ChartOfAccount $chartOfAccount)
    {
        if ($chartOfAccount->is_system) {
            return response()->json(['error' => 'لا يمكن تغيير حالة حسابات النظام'], 400);
        }

        $chartOfAccount->update(['is_active' => !$chartOfAccount->is_active]);

        return response()->json([
            'success' => true,
            'message' => $chartOfAccount->is_active ? 'تم تفعيل الحساب' : 'تم إلغاء تفعيل الحساب',
            'is_active' => $chartOfAccount->is_active
        ]);
    }
}
