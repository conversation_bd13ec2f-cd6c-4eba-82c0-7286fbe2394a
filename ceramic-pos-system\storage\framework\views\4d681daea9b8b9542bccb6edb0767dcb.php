<?php $__env->startSection('title', 'إدارة المنتجات'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-boxes me-2 text-primary"></i>
                إدارة المنتجات
            </h2>
            <p class="text-muted mb-0"><?php echo e($tenant->company_name); ?></p>
        </div>
        <div>
            <a href="<?php echo e(route('inventory.products.create', ['tenant' => $tenant->id])); ?>" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة منتج جديد
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <input type="hidden" name="tenant" value="<?php echo e($tenant->id); ?>">
                
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="اسم المنتج، الكود، أو الباركود" 
                           value="<?php echo e(request('search')); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>" 
                                <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>نشط</option>
                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>غير نشط</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-body">
            <?php if($products->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الصورة</th>
                                <th>اسم المنتج</th>
                                <th>الكود</th>
                                <th>الباركود</th>
                                <th>الفئة</th>
                                <th>سعر التكلفة</th>
                                <th>سعر البيع</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <?php if($product->image): ?>
                                        <img src="<?php echo e(asset('storage/' . $product->image)); ?>" 
                                             alt="<?php echo e($product->name); ?>" 
                                             class="rounded" 
                                             style="width: 50px; height: 50px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <strong><?php echo e($product->name); ?></strong>
                                        <?php if($product->description): ?>
                                            <br><small class="text-muted"><?php echo e(Str::limit($product->description, 50)); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><code><?php echo e($product->code); ?></code></td>
                                <td>
                                    <?php if($product->barcode): ?>
                                        <code><?php echo e($product->barcode); ?></code>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo e($product->category->name ?? 'غير محدد'); ?></span>
                                </td>
                                <td><?php echo e(number_format($product->cost_price, 2)); ?> ج.م</td>
                                <td><?php echo e(number_format($product->selling_price, 2)); ?> ج.م</td>
                                <td>
                                    <span class="badge bg-<?php echo e($product->current_stock <= $product->min_stock ? 'danger' : 'success'); ?>">
                                        <?php echo e($product->current_stock); ?> <?php echo e($product->unit); ?>

                                    </span>
                                    <?php if($product->current_stock <= $product->min_stock): ?>
                                        <br><small class="text-danger">أقل من الحد الأدنى</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($product->is_active): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?php echo e(route('inventory.products.show', ['product' => $product->id, 'tenant' => $tenant->id])); ?>" 
                                           class="btn btn-outline-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('inventory.products.edit', ['product' => $product->id, 'tenant' => $tenant->id])); ?>" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteProduct(<?php echo e($product->id); ?>)" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($products->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد منتجات</h5>
                    <p class="text-muted">ابدأ بإضافة منتجات جديدة لمخزونك</p>
                    <a href="<?php echo e(route('inventory.products.create', ['tenant' => $tenant->id])); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول منتج
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function deleteProduct(productId) {
    const form = document.getElementById('deleteForm');
    form.action = `/inventory/products/${productId}?tenant=<?php echo e($tenant->id); ?>`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Work\ceramic pos\ceramic-pos-system\resources\views/inventory/products/index.blade.php ENDPATH**/ ?>