<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\PaymentInvoice;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Tenant;
use Illuminate\Support\Facades\DB;

class PaymentService
{
    /**
     * إنشاء مدفوعات تجريبية للشركة الجديدة
     */
    public function createSamplePayments(Tenant $tenant): void
    {
        DB::beginTransaction();
        
        try {
            // الحصول على البيانات المطلوبة
            $customers = Customer::where('tenant_id', $tenant->id)->get();
            $suppliers = Supplier::where('tenant_id', $tenant->id)->get();
            $salesInvoices = Invoice::where('tenant_id', $tenant->id)
                                   ->where('invoice_type', 'sale')
                                   ->where('status', 'confirmed')
                                   ->get();
            $purchaseInvoices = Invoice::where('tenant_id', $tenant->id)
                                      ->where('invoice_type', 'purchase')
                                      ->where('status', 'confirmed')
                                      ->get();
            
            if ($customers->isEmpty() || $suppliers->isEmpty() || 
                $salesInvoices->isEmpty() || $purchaseInvoices->isEmpty()) {
                throw new \Exception('يجب إنشاء العملاء والموردين والفواتير أولاً');
            }
            
            // إنشاء مقبوضات
            $this->createSampleReceivedPayments($tenant, $customers, $salesInvoices);
            
            // إنشاء مدفوعات
            $this->createSamplePaidPayments($tenant, $suppliers, $purchaseInvoices);
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
    
    /**
     * إنشاء مقبوضات تجريبية
     */
    private function createSampleReceivedPayments(Tenant $tenant, $customers, $salesInvoices): void
    {
        $receivedPayments = [
            [
                'customer' => $customers->first(),
                'invoices' => $salesInvoices->where('customer_id', $customers->first()->id)->take(2),
                'payment_method' => 'cash',
                'status' => 'cleared',
                'days_ago' => 10,
            ],
            [
                'customer' => $customers->skip(1)->first(),
                'invoices' => $salesInvoices->where('customer_id', $customers->skip(1)->first()->id)->take(1),
                'payment_method' => 'bank_transfer',
                'status' => 'cleared',
                'days_ago' => 7,
            ],
            [
                'customer' => $customers->last(),
                'invoices' => $salesInvoices->where('customer_id', $customers->last()->id)->take(1),
                'payment_method' => 'check',
                'status' => 'pending',
                'days_ago' => 3,
            ],
        ];
        
        foreach ($receivedPayments as $paymentData) {
            if ($paymentData['invoices']->isEmpty()) {
                continue;
            }
            
            $paymentDate = now()->subDays($paymentData['days_ago']);
            $totalAmount = $paymentData['invoices']->sum('total_amount') * 0.8; // دفع جزئي
            
            $payment = Payment::create([
                'tenant_id' => $tenant->id,
                'payment_number' => Payment::generatePaymentNumber('received'),
                'payment_type' => 'received',
                'payment_date' => $paymentDate,
                'customer_id' => $paymentData['customer']->id,
                'payer_name' => $paymentData['customer']->full_name,
                'payment_method' => $paymentData['payment_method'],
                'amount' => $totalAmount,
                'currency' => 'EGP',
                'exchange_rate' => 1,
                'amount_in_base_currency' => $totalAmount,
                'bank_name' => $paymentData['payment_method'] === 'bank_transfer' ? 'البنك الأهلي المصري' : null,
                'account_number' => $paymentData['payment_method'] === 'bank_transfer' ? '**********' : null,
                'check_number' => $paymentData['payment_method'] === 'check' ? '123456' : null,
                'check_date' => $paymentData['payment_method'] === 'check' ? $paymentDate->addDays(30) : null,
                'reference_number' => 'REF-' . time(),
                'status' => $paymentData['status'],
                'cleared_date' => $paymentData['status'] === 'cleared' ? $paymentDate : null,
                'notes' => 'مقبوضات تجريبية',
                'created_by' => 1,
                'cleared_at' => $paymentData['status'] === 'cleared' ? $paymentDate : null,
                'cleared_by' => $paymentData['status'] === 'cleared' ? 1 : null,
                'created_at' => $paymentDate,
                'updated_at' => $paymentDate,
            ]);
            
            // ربط الفواتير
            $remainingAmount = $totalAmount;
            foreach ($paymentData['invoices'] as $invoice) {
                if ($remainingAmount <= 0) break;
                
                $invoiceAmount = min($remainingAmount, $invoice->total_amount);
                
                PaymentInvoice::create([
                    'tenant_id' => $tenant->id,
                    'payment_id' => $payment->id,
                    'invoice_id' => $invoice->id,
                    'amount' => $invoiceAmount,
                    'notes' => 'دفع جزئي',
                ]);
                
                $remainingAmount -= $invoiceAmount;
            }
            
            // مقاصة المدفوعة إذا كانت مقاصة
            if ($paymentData['status'] === 'cleared') {
                $payment->updateInvoiceBalances();
            }
        }
    }
    
    /**
     * إنشاء مدفوعات تجريبية
     */
    private function createSamplePaidPayments(Tenant $tenant, $suppliers, $purchaseInvoices): void
    {
        $paidPayments = [
            [
                'supplier' => $suppliers->first(),
                'invoices' => $purchaseInvoices->where('supplier_id', $suppliers->first()->id)->take(1),
                'payment_method' => 'bank_transfer',
                'status' => 'cleared',
                'days_ago' => 15,
            ],
            [
                'supplier' => $suppliers->skip(1)->first(),
                'invoices' => $purchaseInvoices->where('supplier_id', $suppliers->skip(1)->first()->id)->take(1),
                'payment_method' => 'check',
                'status' => 'cleared',
                'days_ago' => 12,
            ],
            [
                'supplier' => $suppliers->last(),
                'invoices' => $purchaseInvoices->where('supplier_id', $suppliers->last()->id)->take(1),
                'payment_method' => 'bank_transfer',
                'status' => 'pending',
                'days_ago' => 5,
            ],
        ];
        
        foreach ($paidPayments as $paymentData) {
            if ($paymentData['invoices']->isEmpty()) {
                continue;
            }
            
            $paymentDate = now()->subDays($paymentData['days_ago']);
            $totalAmount = $paymentData['invoices']->sum('total_amount'); // دفع كامل
            
            $payment = Payment::create([
                'tenant_id' => $tenant->id,
                'payment_number' => Payment::generatePaymentNumber('paid'),
                'payment_type' => 'paid',
                'payment_date' => $paymentDate,
                'supplier_id' => $paymentData['supplier']->id,
                'payer_name' => $paymentData['supplier']->full_name,
                'payment_method' => $paymentData['payment_method'],
                'amount' => $totalAmount,
                'currency' => 'EGP',
                'exchange_rate' => 1,
                'amount_in_base_currency' => $totalAmount,
                'bank_name' => $paymentData['payment_method'] === 'bank_transfer' ? 'بنك مصر' : 'البنك التجاري الدولي',
                'account_number' => $paymentData['payment_method'] === 'bank_transfer' ? '**********' : null,
                'check_number' => $paymentData['payment_method'] === 'check' ? '654321' : null,
                'check_date' => $paymentData['payment_method'] === 'check' ? $paymentDate->addDays(30) : null,
                'reference_number' => 'PAY-' . time(),
                'status' => $paymentData['status'],
                'cleared_date' => $paymentData['status'] === 'cleared' ? $paymentDate : null,
                'notes' => 'مدفوعات تجريبية',
                'created_by' => 1,
                'cleared_at' => $paymentData['status'] === 'cleared' ? $paymentDate : null,
                'cleared_by' => $paymentData['status'] === 'cleared' ? 1 : null,
                'created_at' => $paymentDate,
                'updated_at' => $paymentDate,
            ]);
            
            // ربط الفواتير
            foreach ($paymentData['invoices'] as $invoice) {
                PaymentInvoice::create([
                    'tenant_id' => $tenant->id,
                    'payment_id' => $payment->id,
                    'invoice_id' => $invoice->id,
                    'amount' => $invoice->total_amount,
                    'notes' => 'دفع كامل',
                ]);
            }
            
            // مقاصة المدفوعة إذا كانت مقاصة
            if ($paymentData['status'] === 'cleared') {
                $payment->updateInvoiceBalances();
            }
        }
    }
    
    /**
     * الحصول على إحصائيات المدفوعات
     */
    public function getPaymentStatistics(): array
    {
        return [
            'received' => [
                'total_payments' => Payment::received()->count(),
                'cleared_payments' => Payment::received()->cleared()->count(),
                'pending_payments' => Payment::received()->pending()->count(),
                'bounced_payments' => Payment::received()->bounced()->count(),
                'total_amount' => Payment::received()->sum('amount'),
                'cleared_amount' => Payment::received()->cleared()->sum('amount'),
                'pending_amount' => Payment::received()->pending()->sum('amount'),
            ],
            'paid' => [
                'total_payments' => Payment::paid()->count(),
                'cleared_payments' => Payment::paid()->cleared()->count(),
                'pending_payments' => Payment::paid()->pending()->count(),
                'bounced_payments' => Payment::paid()->bounced()->count(),
                'total_amount' => Payment::paid()->sum('amount'),
                'cleared_amount' => Payment::paid()->cleared()->sum('amount'),
                'pending_amount' => Payment::paid()->pending()->sum('amount'),
            ],
            'overall' => [
                'total_payments' => Payment::count(),
                'cleared_payments' => Payment::cleared()->count(),
                'pending_payments' => Payment::pending()->count(),
                'net_cash_flow' => $this->calculateNetCashFlow(),
            ],
            'by_method' => [
                'cash' => Payment::where('payment_method', 'cash')->cleared()->sum('amount'),
                'bank_transfer' => Payment::where('payment_method', 'bank_transfer')->cleared()->sum('amount'),
                'check' => Payment::where('payment_method', 'check')->cleared()->sum('amount'),
                'card' => Payment::where('payment_method', 'card')->cleared()->sum('amount'),
            ],
        ];
    }
    
    /**
     * حساب صافي التدفق النقدي
     */
    private function calculateNetCashFlow(): float
    {
        $totalReceived = Payment::received()->cleared()->sum('amount');
        $totalPaid = Payment::paid()->cleared()->sum('amount');
        
        return $totalReceived - $totalPaid;
    }
    
    /**
     * الحصول على المدفوعات المعلقة
     */
    public function getPendingPayments(): array
    {
        return [
            'received' => Payment::received()->pending()->with(['customer'])->get(),
            'paid' => Payment::paid()->pending()->with(['supplier'])->get(),
        ];
    }
    
    /**
     * الحصول على الشيكات المستحقة
     */
    public function getDueChecks(): array
    {
        return [
            'received' => Payment::received()
                                ->where('payment_method', 'check')
                                ->where('status', 'pending')
                                ->where('check_date', '<=', now())
                                ->with(['customer'])
                                ->get(),
            'paid' => Payment::paid()
                            ->where('payment_method', 'check')
                            ->where('status', 'pending')
                            ->where('check_date', '<=', now())
                            ->with(['supplier'])
                            ->get(),
        ];
    }
    
    /**
     * تقرير التدفق النقدي
     */
    public function getCashFlowReport(string $period = 'month'): array
    {
        $startDate = match($period) {
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
        
        $received = Payment::received()
                          ->cleared()
                          ->where('payment_date', '>=', $startDate)
                          ->selectRaw('DATE(payment_date) as date, SUM(amount) as total')
                          ->groupBy('date')
                          ->orderBy('date')
                          ->get();
        
        $paid = Payment::paid()
                      ->cleared()
                      ->where('payment_date', '>=', $startDate)
                      ->selectRaw('DATE(payment_date) as date, SUM(amount) as total')
                      ->groupBy('date')
                      ->orderBy('date')
                      ->get();
        
        return [
            'received' => $received,
            'paid' => $paid,
            'net_flow' => $received->sum('total') - $paid->sum('total'),
        ];
    }
}
