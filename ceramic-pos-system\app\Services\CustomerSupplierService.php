<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Tenant;
use Illuminate\Support\Facades\DB;

class CustomerSupplierService
{
    /**
     * إنشاء عملاء وموردين تجريبيين للشركة الجديدة
     */
    public function createSampleCustomersAndSuppliers(Tenant $tenant): void
    {
        DB::beginTransaction();
        
        try {
            // إنشاء عملاء تجريبيين
            $this->createSampleCustomers($tenant);
            
            // إنشاء موردين تجريبيين
            $this->createSampleSuppliers($tenant);
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
    
    /**
     * إنشاء عملاء تجريبيين
     */
    private function createSampleCustomers(Tenant $tenant): void
    {
        $customers = [
            [
                'tenant_id' => $tenant->id,
                'customer_code' => 'C001',
                'name' => 'أحمد محمد علي',
                'company_name' => null,
                'customer_type' => 'individual',
                'tax_number' => null,
                'commercial_register' => null,
                'email' => '<EMAIL>',
                'phone' => '02-********',
                'mobile' => '010********',
                'address' => 'شارع النيل، المعادي، القاهرة',
                'city' => 'القاهرة',
                'country' => 'EG',
                'credit_limit' => 10000.00,
                'payment_terms' => 30,
                'opening_balance' => 0,
                'current_balance' => 0,
                'balance_type' => 'debit',
                'is_active' => true,
                'notes' => 'عميل مميز',
            ],
            [
                'tenant_id' => $tenant->id,
                'customer_code' => 'C002',
                'name' => 'مدير المشتريات',
                'company_name' => 'شركة الإنشاءات الحديثة',
                'customer_type' => 'company',
                'tax_number' => '********901',
                'commercial_register' => '87654321',
                'email' => '<EMAIL>',
                'phone' => '02-87654321',
                'mobile' => '01087654321',
                'address' => 'شارع التحرير، وسط البلد، القاهرة',
                'city' => 'القاهرة',
                'country' => 'EG',
                'credit_limit' => 50000.00,
                'payment_terms' => 45,
                'opening_balance' => 5000.00,
                'current_balance' => 5000.00,
                'balance_type' => 'debit',
                'is_active' => true,
                'notes' => 'شركة إنشاءات كبيرة',
            ],
            [
                'tenant_id' => $tenant->id,
                'customer_code' => 'C003',
                'name' => 'فاطمة أحمد',
                'company_name' => null,
                'customer_type' => 'individual',
                'tax_number' => null,
                'commercial_register' => null,
                'email' => '<EMAIL>',
                'phone' => '03-11223344',
                'mobile' => '01111223344',
                'address' => 'شارع الجيش، الإسكندرية',
                'city' => 'الإسكندرية',
                'country' => 'EG',
                'credit_limit' => 15000.00,
                'payment_terms' => 30,
                'opening_balance' => 2500.00,
                'current_balance' => 2500.00,
                'balance_type' => 'debit',
                'is_active' => true,
                'notes' => 'عميلة من الإسكندرية',
            ],
        ];
        
        foreach ($customers as $customerData) {
            Customer::create($customerData);
        }
    }
    
    /**
     * إنشاء موردين تجريبيين
     */
    private function createSampleSuppliers(Tenant $tenant): void
    {
        $suppliers = [
            [
                'tenant_id' => $tenant->id,
                'supplier_code' => 'S001',
                'name' => 'مدير المبيعات',
                'company_name' => 'مصنع السيراميك المصري',
                'supplier_type' => 'company',
                'tax_number' => '**********1',
                'commercial_register' => '********',
                'email' => '<EMAIL>',
                'phone' => '02-33445566',
                'mobile' => '***********',
                'address' => 'المنطقة الصناعية، العاشر من رمضان',
                'city' => 'العاشر من رمضان',
                'country' => 'EG',
                'payment_terms' => 30,
                'opening_balance' => 15000.00,
                'current_balance' => 15000.00,
                'balance_type' => 'credit',
                'contact_person' => 'محمد السيد',
                'contact_person_phone' => '***********',
                'bank_name' => 'البنك الأهلي المصري',
                'bank_account' => '********90',
                'iban' => 'EG38000200********90********9',
                'is_active' => true,
                'notes' => 'مورد السيراميك الرئيسي',
            ],
            [
                'tenant_id' => $tenant->id,
                'supplier_code' => 'S002',
                'name' => 'مدير التصدير',
                'company_name' => 'شركة الأدوات الصحية الإيطالية',
                'supplier_type' => 'company',
                'tax_number' => 'IT********901',
                'commercial_register' => 'IT87654321',
                'email' => '<EMAIL>',
                'phone' => '+39-02-1234567',
                'mobile' => '+39-333-1234567',
                'address' => 'Via Roma 123, Milano',
                'city' => 'ميلانو',
                'country' => 'IT',
                'payment_terms' => 60,
                'opening_balance' => 25000.00,
                'current_balance' => 25000.00,
                'balance_type' => 'credit',
                'contact_person' => 'Marco Rossi',
                'contact_person_phone' => '+39-333-1234567',
                'bank_name' => 'Banca Intesa',
                'bank_account' => '***************************',
                'iban' => '***************************',
                'is_active' => true,
                'notes' => 'مورد أدوات صحية إيطالية عالية الجودة',
            ],
            [
                'tenant_id' => $tenant->id,
                'supplier_code' => 'S003',
                'name' => 'أحمد محمود',
                'company_name' => null,
                'supplier_type' => 'individual',
                'tax_number' => null,
                'commercial_register' => null,
                'email' => '<EMAIL>',
                'phone' => '02-********',
                'mobile' => '010********',
                'address' => 'شارع الهرم، الجيزة',
                'city' => 'الجيزة',
                'country' => 'EG',
                'payment_terms' => 15,
                'opening_balance' => 3000.00,
                'current_balance' => 3000.00,
                'balance_type' => 'credit',
                'contact_person' => null,
                'contact_person_phone' => null,
                'bank_name' => 'بنك مصر',
                'bank_account' => '**********',
                'iban' => null,
                'is_active' => true,
                'notes' => 'مورد محلي للإكسسوارات',
            ],
            [
                'tenant_id' => $tenant->id,
                'supplier_code' => 'S004',
                'name' => 'مدير المبيعات',
                'company_name' => 'مجموعة السيراميك الصينية',
                'supplier_type' => 'company',
                'tax_number' => 'CN********9012345',
                'commercial_register' => 'CN987654321',
                'email' => '<EMAIL>',
                'phone' => '+86-20-********',
                'mobile' => '+86-138-********',
                'address' => 'Foshan Ceramic Industrial Zone',
                'city' => 'فوشان',
                'country' => 'CN',
                'payment_terms' => 45,
                'opening_balance' => 35000.00,
                'current_balance' => 35000.00,
                'balance_type' => 'credit',
                'contact_person' => 'Li Wei',
                'contact_person_phone' => '+86-138-********',
                'bank_name' => 'Bank of China',
                'bank_account' => 'CN********90********9',
                'iban' => null,
                'is_active' => true,
                'notes' => 'مورد سيراميك صيني بأسعار تنافسية',
            ],
        ];
        
        foreach ($suppliers as $supplierData) {
            Supplier::create($supplierData);
        }
    }
    
    /**
     * الحصول على إحصائيات العملاء والموردين
     */
    public function getStatistics(): array
    {
        return [
            'customers' => [
                'total' => Customer::count(),
                'active' => Customer::active()->count(),
                'individual' => Customer::ofType('individual')->count(),
                'company' => Customer::ofType('company')->count(),
                'overdue' => Customer::overdue()->count(),
                'over_credit_limit' => Customer::overCreditLimit()->count(),
                'total_receivables' => Customer::where('balance_type', 'debit')->sum('current_balance'),
                'total_advances' => Customer::where('balance_type', 'credit')->sum('current_balance'),
            ],
            'suppliers' => [
                'total' => Supplier::count(),
                'active' => Supplier::active()->count(),
                'individual' => Supplier::ofType('individual')->count(),
                'company' => Supplier::ofType('company')->count(),
                'payable' => Supplier::payable()->count(),
                'total_payables' => Supplier::where('balance_type', 'credit')->sum('current_balance'),
                'total_advances' => Supplier::where('balance_type', 'debit')->sum('current_balance'),
                'local_suppliers' => Supplier::where('country', 'EG')->count(),
                'foreign_suppliers' => Supplier::where('country', '!=', 'EG')->count(),
            ],
        ];
    }
    
    /**
     * البحث في العملاء والموردين
     */
    public function searchCustomersAndSuppliers(string $query): array
    {
        $customers = Customer::search($query)
            ->active()
            ->limit(10)
            ->get(['id', 'customer_code', 'name', 'company_name', 'current_balance', 'balance_type']);
            
        $suppliers = Supplier::search($query)
            ->active()
            ->limit(10)
            ->get(['id', 'supplier_code', 'name', 'company_name', 'current_balance', 'balance_type']);
        
        return [
            'customers' => $customers->map(function ($customer) {
                return [
                    'id' => $customer->id,
                    'type' => 'customer',
                    'code' => $customer->customer_code,
                    'name' => $customer->full_name,
                    'balance' => $customer->current_balance,
                    'balance_type' => $customer->balance_type_name,
                ];
            })->toArray(),
            'suppliers' => $suppliers->map(function ($supplier) {
                return [
                    'id' => $supplier->id,
                    'type' => 'supplier',
                    'code' => $supplier->supplier_code,
                    'name' => $supplier->full_name,
                    'balance' => $supplier->current_balance,
                    'balance_type' => $supplier->balance_type_name,
                ];
            })->toArray(),
        ];
    }
    
    /**
     * تحديث أرصدة العملاء والموردين
     */
    public function updateBalances(array $updates): void
    {
        DB::beginTransaction();
        
        try {
            foreach ($updates as $update) {
                if ($update['type'] === 'customer') {
                    $customer = Customer::findOrFail($update['id']);
                    $customer->updateBalance($update['amount'], $update['balance_type']);
                } elseif ($update['type'] === 'supplier') {
                    $supplier = Supplier::findOrFail($update['id']);
                    $supplier->updateBalance($update['amount'], $update['balance_type']);
                }
            }
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
