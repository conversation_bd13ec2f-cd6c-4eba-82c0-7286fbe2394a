<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// API Routes for Tenant System
Route::prefix('v1')->name('api.v1.')->group(function () {
    Route::middleware(['auth:sanctum'])->group(function () {
        
        // Dashboard & Stats
        Route::get('/dashboard/stats', [\App\Http\Controllers\Api\V1\ApiController::class, 'dashboardStats'])->name('dashboard.stats');
        Route::get('/system/info', [\App\Http\Controllers\Api\V1\ApiController::class, 'systemInfo'])->name('system.info');
        
        // Search
        Route::get('/search', [\App\Http\Controllers\Api\V1\ApiController::class, 'search'])->name('search');
        
        // Customers
        Route::get('/customers', [\App\Http\Controllers\Api\V1\ApiController::class, 'customers'])->name('customers.index');
        Route::get('/customers/{id}', [\App\Http\Controllers\Api\V1\ApiController::class, 'customer'])->name('customers.show');
        
        // Suppliers
        Route::get('/suppliers', [\App\Http\Controllers\Api\V1\ApiController::class, 'suppliers'])->name('suppliers.index');
        Route::get('/suppliers/{id}', [\App\Http\Controllers\Api\V1\ApiController::class, 'supplier'])->name('suppliers.show');
        
        // Products
        Route::get('/products', [\App\Http\Controllers\Api\V1\ApiController::class, 'products'])->name('products.index');
        Route::get('/products/{id}', [\App\Http\Controllers\Api\V1\ApiController::class, 'product'])->name('products.show');
        
        // Invoices
        Route::get('/invoices', [\App\Http\Controllers\Api\V1\ApiController::class, 'invoices'])->name('invoices.index');
        Route::get('/invoices/{id}', [\App\Http\Controllers\Api\V1\ApiController::class, 'invoice'])->name('invoices.show');
        
        // Payments
        Route::get('/payments', [\App\Http\Controllers\Api\V1\ApiController::class, 'payments'])->name('payments.index');
        Route::get('/payments/{id}', [\App\Http\Controllers\Api\V1\ApiController::class, 'payment'])->name('payments.show');
        
        // Reports
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/profit-loss', [\App\Http\Controllers\Api\V1\ApiController::class, 'profitLossReport'])->name('profit-loss');
            Route::get('/balance-sheet', [\App\Http\Controllers\Api\V1\ApiController::class, 'balanceSheetReport'])->name('balance-sheet');
            Route::get('/cash-flow', [\App\Http\Controllers\Api\V1\ApiController::class, 'cashFlowReport'])->name('cash-flow');
        });
        
        // Settings
        Route::get('/settings', [\App\Http\Controllers\Api\V1\ApiController::class, 'settings'])->name('settings.index');
        Route::post('/settings', [\App\Http\Controllers\Api\V1\ApiController::class, 'updateSetting'])->name('settings.update');
    });
});
