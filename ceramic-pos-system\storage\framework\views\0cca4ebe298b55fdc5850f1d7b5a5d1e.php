<?php $__env->startSection('title', 'لوحة تحكم صاحب النظام'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="fw-bold mb-1">
                <i class="fas fa-crown me-2 text-warning"></i>
                لوحة تحكم صاحب النظام
            </h1>
            <p class="text-muted mb-0">إدارة شاملة لنظام Ceramic POS</p>
        </div>
        <div>
            <span class="badge bg-success fs-6">
                <i class="fas fa-shield-alt me-1"></i>
                Super Admin
            </span>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo e($stats['total_tenants']); ?></h3>
                            <p class="mb-0">إجمالي المشتركين</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo e($stats['active_tenants']); ?></h3>
                            <p class="mb-0">مشتركين نشطين</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo e($stats['trial_tenants']); ?></h3>
                            <p class="mb-0">فترة تجريبية</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0"><?php echo e(number_format($stats['total_revenue'])); ?></h3>
                            <p class="mb-0">الإيرادات الشهرية (ج.م)</p>
                        </div>
                        <div class="fs-1 opacity-75">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tenants -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        أحدث المشتركين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الشركة</th>
                                    <th>المالك</th>
                                    <th>الخطة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recent_tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <strong><?php echo e($tenant->company_name); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo e($tenant->subdomain); ?>.ceramicpos.com</small>
                                    </td>
                                    <td>
                                        <?php echo e($tenant->owner_name); ?>

                                        <br>
                                        <small class="text-muted"><?php echo e($tenant->email); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo e($tenant->getPlanName()); ?></span>
                                    </td>
                                    <td>
                                        <?php switch($tenant->status):
                                            case ('active'): ?>
                                                <span class="badge bg-success">نشط</span>
                                                <?php break; ?>
                                            <?php case ('trial'): ?>
                                                <span class="badge bg-warning">تجريبي</span>
                                                <?php break; ?>
                                            <?php case ('suspended'): ?>
                                                <span class="badge bg-danger">معلق</span>
                                                <?php break; ?>
                                            <?php case ('expired'): ?>
                                                <span class="badge bg-dark">منتهي</span>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </td>
                                    <td><?php echo e($tenant->created_at->format('Y/m/d')); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('super-admin.tenants.show', $tenant->id)); ?>" 
                                               class="btn btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('dashboard', ['tenant' => $tenant->id])); ?>" 
                                               class="btn btn-outline-primary" title="دخول للنظام" target="_blank">
                                                <i class="fas fa-sign-in-alt"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="<?php echo e(route('super-admin.tenants')); ?>" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع المشتركين
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expiring Trials & Quick Actions -->
        <div class="col-lg-4">
            <!-- Expiring Trials -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                        فترات تجريبية تنتهي قريباً
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($expiring_trials->count() > 0): ?>
                        <?php $__currentLoopData = $expiring_trials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong><?php echo e($tenant->company_name); ?></strong>
                                <br>
                                <small class="text-muted">
                                    ينتهي في <?php echo e($tenant->trial_ends_at->diffForHumans()); ?>

                                </small>
                            </div>
                            <a href="<?php echo e(route('super-admin.tenants.show', $tenant->id)); ?>" 
                               class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                        <?php if(!$loop->last): ?>
                            <hr>
                        <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد فترات تجريبية تنتهي قريباً</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('super-admin.tenants.create')); ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مشترك جديد
                        </a>
                        <a href="<?php echo e(route('super-admin.plans')); ?>" class="btn btn-info">
                            <i class="fas fa-tags me-2"></i>
                            إدارة الخطط والأسعار
                        </a>
                        <a href="<?php echo e(route('super-admin.financial-reports')); ?>" class="btn btn-warning">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير المالية
                        </a>
                        <a href="<?php echo e(route('super-admin.settings')); ?>" class="btn btn-secondary">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>
                        حالة النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">
                                    <i class="fas fa-check-circle"></i>
                                </h4>
                                <p class="text-muted mb-0">الخادم</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">
                                    <i class="fas fa-database"></i>
                                </h4>
                                <p class="text-muted mb-0">قاعدة البيانات</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">
                                    <i class="fas fa-cloud"></i>
                                </h4>
                                <p class="text-muted mb-0">التخزين السحابي</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success">
                                <i class="fas fa-shield-alt"></i>
                            </h4>
                            <p class="text-muted mb-0">الأمان</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.super-admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Work\ceramic pos\ceramic-pos-system\resources\views/super-admin/dashboard.blade.php ENDPATH**/ ?>