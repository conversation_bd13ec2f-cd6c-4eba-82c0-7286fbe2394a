<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Supplier;
use Illuminate\Support\Facades\DB;

class SupplierController extends Controller
{
    /**
     * عرض قائمة الموردين
     */
    public function index(Request $request)
    {
        $query = Supplier::query();

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // التصفية حسب النوع
        if ($request->filled('supplier_type')) {
            $query->ofType($request->supplier_type);
        }

        // التصفية حسب الحالة
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // التصفية حسب نوع الرصيد
        if ($request->filled('balance_type')) {
            $query->where('balance_type', $request->balance_type);
        }

        // التصفية للموردين المستحقين للدفع
        if ($request->boolean('payable')) {
            $query->payable();
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'supplier_code');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $suppliers = $query->paginate(20);

        // إحصائيات
        $stats = [
            'total_suppliers' => Supplier::count(),
            'active_suppliers' => Supplier::active()->count(),
            'individual_suppliers' => Supplier::ofType('individual')->count(),
            'company_suppliers' => Supplier::ofType('company')->count(),
            'payable_suppliers' => Supplier::payable()->count(),
            'total_payables' => Supplier::where('balance_type', 'credit')->sum('current_balance'),
        ];

        return view('tenant.suppliers.index', compact('suppliers', 'stats'));
    }

    /**
     * عرض نموذج إنشاء مورد جديد
     */
    public function create()
    {
        return view('tenant.suppliers.create');
    }

    /**
     * حفظ مورد جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'supplier_code' => 'required|string|max:20|unique:suppliers,supplier_code',
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'supplier_type' => 'required|in:individual,company',
            'tax_number' => 'nullable|string|max:50',
            'commercial_register' => 'nullable|string|max:50',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:2',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'opening_balance' => 'nullable|numeric|min:0',
            'balance_type' => 'required|in:debit,credit',
            'contact_person' => 'nullable|string|max:255',
            'contact_person_phone' => 'nullable|string|max:20',
            'bank_name' => 'nullable|string|max:255',
            'bank_account' => 'nullable|string|max:50',
            'iban' => 'nullable|string|max:50',
            'notes' => 'nullable|string',
        ], [
            'supplier_code.required' => 'رقم المورد مطلوب',
            'supplier_code.unique' => 'رقم المورد مستخدم من قبل',
            'name.required' => 'اسم المورد مطلوب',
            'supplier_type.required' => 'نوع المورد مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'country.required' => 'البلد مطلوب',
            'balance_type.required' => 'نوع الرصيد مطلوب',
        ]);

        try {
            $supplier = Supplier::create([
                'supplier_code' => $request->supplier_code,
                'name' => $request->name,
                'company_name' => $request->company_name,
                'supplier_type' => $request->supplier_type,
                'tax_number' => $request->tax_number,
                'commercial_register' => $request->commercial_register,
                'email' => $request->email,
                'phone' => $request->phone,
                'mobile' => $request->mobile,
                'fax' => $request->fax,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code,
                'country' => $request->country,
                'payment_terms' => $request->payment_terms ?? 30,
                'opening_balance' => $request->opening_balance ?? 0,
                'current_balance' => $request->opening_balance ?? 0,
                'balance_type' => $request->balance_type,
                'contact_person' => $request->contact_person,
                'contact_person_phone' => $request->contact_person_phone,
                'bank_name' => $request->bank_name,
                'bank_account' => $request->bank_account,
                'iban' => $request->iban,
                'notes' => $request->notes,
                'is_active' => true,
            ]);

            return redirect()->route('tenant.suppliers.index')
                ->with('success', 'تم إنشاء المورد بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المورد: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل المورد
     */
    public function show(Supplier $supplier)
    {
        // $supplier->load('purchaseInvoices', 'payments', 'purchaseOrders', 'products');

        // إحصائيات المورد
        $stats = [
            'total_purchases' => 0, // $supplier->total_purchases,
            'total_payments' => 0, // $supplier->total_payments,
            'invoices_count' => 0, // $supplier->purchaseInvoices()->count(),
            'products_count' => 0, // $supplier->products_count,
            'last_invoice_date' => null, // $supplier->last_purchase_invoice?->created_at,
            'last_payment_date' => null, // $supplier->last_payment?->created_at,
        ];

        return view('tenant.suppliers.show', compact('supplier', 'stats'));
    }

    /**
     * عرض نموذج تعديل المورد
     */
    public function edit(Supplier $supplier)
    {
        return view('tenant.suppliers.edit', compact('supplier'));
    }

    /**
     * تحديث المورد
     */
    public function update(Request $request, Supplier $supplier)
    {
        $request->validate([
            'supplier_code' => 'required|string|max:20|unique:suppliers,supplier_code,' . $supplier->id,
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'supplier_type' => 'required|in:individual,company',
            'tax_number' => 'nullable|string|max:50',
            'commercial_register' => 'nullable|string|max:50',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:2',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'contact_person' => 'nullable|string|max:255',
            'contact_person_phone' => 'nullable|string|max:20',
            'bank_name' => 'nullable|string|max:255',
            'bank_account' => 'nullable|string|max:50',
            'iban' => 'nullable|string|max:50',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        try {
            $supplier->update([
                'supplier_code' => $request->supplier_code,
                'name' => $request->name,
                'company_name' => $request->company_name,
                'supplier_type' => $request->supplier_type,
                'tax_number' => $request->tax_number,
                'commercial_register' => $request->commercial_register,
                'email' => $request->email,
                'phone' => $request->phone,
                'mobile' => $request->mobile,
                'fax' => $request->fax,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code,
                'country' => $request->country,
                'payment_terms' => $request->payment_terms ?? 30,
                'contact_person' => $request->contact_person,
                'contact_person_phone' => $request->contact_person_phone,
                'bank_name' => $request->bank_name,
                'bank_account' => $request->bank_account,
                'iban' => $request->iban,
                'notes' => $request->notes,
                'is_active' => $request->boolean('is_active', true),
            ]);

            return redirect()->route('tenant.suppliers.index')
                ->with('success', 'تم تحديث المورد بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث المورد: ' . $e->getMessage());
        }
    }

    /**
     * حذف المورد
     */
    public function destroy(Supplier $supplier)
    {
        // التحقق من عدم وجود فواتير أو حركات مالية أو منتجات مرتبطة
        // if ($supplier->purchaseInvoices()->count() > 0 ||
        //     $supplier->payments()->count() > 0 ||
        //     $supplier->products()->count() > 0) {
        //     return redirect()->back()
        //         ->with('error', 'لا يمكن حذف مورد له فواتير أو حركات مالية أو منتجات مرتبطة');
        // }

        try {
            $supplier->delete();

            return redirect()->route('tenant.suppliers.index')
                ->with('success', 'تم حذف المورد بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف المورد: ' . $e->getMessage());
        }
    }

    /**
     * تفعيل/إلغاء تفعيل المورد
     */
    public function toggleStatus(Supplier $supplier)
    {
        $supplier->update(['is_active' => !$supplier->is_active]);

        return response()->json([
            'success' => true,
            'message' => $supplier->is_active ? 'تم تفعيل المورد' : 'تم إلغاء تفعيل المورد',
            'is_active' => $supplier->is_active
        ]);
    }

    /**
     * البحث في الموردين (للـ API)
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suppliers = Supplier::search($query)
            ->active()
            ->limit(10)
            ->get(['id', 'supplier_code', 'name', 'company_name', 'current_balance', 'balance_type']);

        return response()->json($suppliers->map(function ($supplier) {
            return [
                'id' => $supplier->id,
                'code' => $supplier->supplier_code,
                'name' => $supplier->full_name,
                'balance' => $supplier->current_balance,
                'balance_type' => $supplier->balance_type_name,
            ];
        }));
    }

    /**
     * تقرير الموردين
     */
    public function report(Request $request)
    {
        $query = Supplier::query();

        // التصفية حسب التاريخ
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        // التصفية حسب النوع
        if ($request->filled('supplier_type')) {
            $query->ofType($request->supplier_type);
        }

        // التصفية حسب الحالة
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $suppliers = $query->orderBy('supplier_code')->get();

        return view('tenant.suppliers.report', compact('suppliers'));
    }
}
