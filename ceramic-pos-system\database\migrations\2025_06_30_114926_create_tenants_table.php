<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الشركة
            $table->string('slug')->unique(); // معرف فريد للشركة (للنطاق الفرعي)
            $table->string('domain')->nullable(); // النطاق المخصص
            $table->string('subdomain')->unique(); // النطاق الفرعي
            $table->string('database_name')->nullable(); // اسم قاعدة البيانات المخصصة
            $table->string('logo')->nullable(); // شعار الشركة
            $table->text('description')->nullable(); // وصف الشركة
            $table->json('settings')->nullable(); // إعدادات الشركة
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->enum('plan', ['basic', 'standard', 'premium'])->default('basic'); // خطة الاشتراك
            $table->date('subscription_start')->nullable(); // تاريخ بداية الاشتراك
            $table->date('subscription_end')->nullable(); // تاريخ انتهاء الاشتراك
            $table->integer('max_users')->default(5); // الحد الأقصى للمستخدمين
            $table->json('features')->nullable(); // الميزات المتاحة
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->default('SA');
            $table->string('currency', 3)->default('SAR');
            $table->string('timezone')->default('Asia/Riyadh');
            $table->string('language', 2)->default('ar');
            $table->timestamps();

            $table->index(['status', 'subscription_end']);
            $table->index('slug');
            $table->index('subdomain');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};
