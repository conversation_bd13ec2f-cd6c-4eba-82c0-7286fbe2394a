<?php

namespace App\Helpers;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Supplier;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class DashboardHelper
{
    /**
     * تنسيق الأرقام للعرض
     */
    public static function formatNumber($number, $decimals = 2): string
    {
        if ($number >= 1000000) {
            return number_format($number / 1000000, $decimals) . 'M';
        } elseif ($number >= 1000) {
            return number_format($number / 1000, $decimals) . 'K';
        }
        
        return number_format($number, $decimals);
    }

    /**
     * تنسيق المبالغ المالية
     */
    public static function formatCurrency($amount, $currency = 'EGP'): string
    {
        $symbols = [
            'EGP' => 'ج.م',
            'USD' => '$',
            'EUR' => '€',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
        ];

        $symbol = $symbols[$currency] ?? $currency;
        return self::formatNumber($amount) . ' ' . $symbol;
    }

    /**
     * حساب النسبة المئوية للتغيير
     */
    public static function calculatePercentageChange($current, $previous): array
    {
        if ($previous == 0) {
            return [
                'percentage' => $current > 0 ? 100 : 0,
                'direction' => $current > 0 ? 'up' : 'neutral',
                'color' => $current > 0 ? 'success' : 'secondary',
            ];
        }

        $percentage = (($current - $previous) / $previous) * 100;
        
        return [
            'percentage' => abs($percentage),
            'direction' => $percentage > 0 ? 'up' : ($percentage < 0 ? 'down' : 'neutral'),
            'color' => $percentage > 0 ? 'success' : ($percentage < 0 ? 'danger' : 'secondary'),
        ];
    }

    /**
     * الحصول على لون حالة الصحة
     */
    public static function getHealthColor($percentage): string
    {
        if ($percentage >= 80) {
            return 'success';
        } elseif ($percentage >= 60) {
            return 'warning';
        } elseif ($percentage >= 40) {
            return 'info';
        } else {
            return 'danger';
        }
    }

    /**
     * الحصول على أيقونة الاتجاه
     */
    public static function getTrendIcon($direction): string
    {
        return match($direction) {
            'up' => 'fas fa-arrow-up',
            'down' => 'fas fa-arrow-down',
            default => 'fas fa-minus',
        };
    }

    /**
     * تحويل الفترة إلى نص عربي
     */
    public static function getPeriodLabel($period): string
    {
        return match($period) {
            'today' => 'اليوم',
            'week' => 'هذا الأسبوع',
            'month' => 'هذا الشهر',
            'quarter' => 'هذا الربع',
            'year' => 'هذا العام',
            default => 'هذا الشهر',
        };
    }

    /**
     * الحصول على إحصائيات سريعة من الكاش
     */
    public static function getQuickStats($period = 'month'): array
    {
        $cacheKey = 'dashboard_quick_stats_' . tenant('id') . '_' . $period;
        
        return Cache::remember($cacheKey, now()->addMinutes(15), function() use ($period) {
            $startDate = self::getStartDate($period);
            $endDate = now();

            return [
                'sales' => Invoice::sales()
                                 ->confirmed()
                                 ->whereBetween('invoice_date', [$startDate, $endDate])
                                 ->sum('total_amount'),
                'purchases' => Invoice::purchases()
                                     ->confirmed()
                                     ->whereBetween('invoice_date', [$startDate, $endDate])
                                     ->sum('total_amount'),
                'received' => Payment::received()
                                    ->cleared()
                                    ->whereBetween('payment_date', [$startDate, $endDate])
                                    ->sum('amount'),
                'paid' => Payment::paid()
                                ->cleared()
                                ->whereBetween('payment_date', [$startDate, $endDate])
                                ->sum('amount'),
            ];
        });
    }

    /**
     * الحصول على عدد التنبيهات
     */
    public static function getAlertsCount(): array
    {
        $cacheKey = 'dashboard_alerts_count_' . tenant('id');
        
        return Cache::remember($cacheKey, now()->addMinutes(10), function() {
            return [
                'low_stock' => Product::lowStock()->count(),
                'out_of_stock' => Product::outOfStock()->count(),
                'overdue_sales' => Invoice::sales()->overdue()->count(),
                'overdue_purchases' => Invoice::purchases()->overdue()->count(),
                'pending_payments' => Payment::pending()->count(),
                'bounced_checks' => Payment::bounced()->count(),
            ];
        });
    }

    /**
     * الحصول على أفضل العملاء
     */
    public static function getTopCustomers($limit = 5): array
    {
        $cacheKey = 'dashboard_top_customers_' . tenant('id') . '_' . $limit;
        
        return Cache::remember($cacheKey, now()->addHours(1), function() use ($limit) {
            return Customer::withSum(['salesInvoices' => function($query) {
                    $query->where('status', 'confirmed');
                }], 'total_amount')
                ->orderBy('sales_invoices_sum_total_amount', 'desc')
                ->limit($limit)
                ->get(['id', 'name', 'customer_code'])
                ->toArray();
        });
    }

    /**
     * الحصول على أفضل المنتجات
     */
    public static function getTopProducts($limit = 5): array
    {
        $cacheKey = 'dashboard_top_products_' . tenant('id') . '_' . $limit;
        
        return Cache::remember($cacheKey, now()->addHours(1), function() use ($limit) {
            return Product::withSum(['invoiceItems' => function($query) {
                    $query->whereHas('invoice', function($invoiceQuery) {
                        $invoiceQuery->where('invoice_type', 'sale')
                                    ->where('status', 'confirmed');
                    });
                }], 'quantity')
                ->orderBy('invoice_items_sum_quantity', 'desc')
                ->limit($limit)
                ->get(['id', 'name', 'product_code'])
                ->toArray();
        });
    }

    /**
     * تنظيف كاش لوحة التحكم
     */
    public static function clearDashboardCache(): void
    {
        $tenantId = tenant('id');
        $patterns = [
            "dashboard_stats_{$tenantId}_*",
            "dashboard_quick_stats_{$tenantId}_*",
            "dashboard_alerts_count_{$tenantId}",
            "dashboard_top_customers_{$tenantId}_*",
            "dashboard_top_products_{$tenantId}_*",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * الحصول على تاريخ البداية حسب الفترة
     */
    private static function getStartDate($period): Carbon
    {
        return match($period) {
            'today' => now()->startOfDay(),
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    /**
     * تحديد لون الحالة حسب النوع
     */
    public static function getStatusColor($status, $type = 'invoice'): string
    {
        if ($type === 'invoice') {
            return match($status) {
                'draft' => 'secondary',
                'confirmed' => 'primary',
                'paid' => 'success',
                'cancelled' => 'danger',
                default => 'secondary',
            };
        } elseif ($type === 'payment') {
            return match($status) {
                'pending' => 'warning',
                'cleared' => 'success',
                'bounced' => 'danger',
                'cancelled' => 'secondary',
                default => 'secondary',
            };
        }

        return 'secondary';
    }

    /**
     * تحديد أيقونة الحالة
     */
    public static function getStatusIcon($status, $type = 'invoice'): string
    {
        if ($type === 'invoice') {
            return match($status) {
                'draft' => 'fas fa-edit',
                'confirmed' => 'fas fa-check-circle',
                'paid' => 'fas fa-money-check-alt',
                'cancelled' => 'fas fa-times-circle',
                default => 'fas fa-question-circle',
            };
        } elseif ($type === 'payment') {
            return match($status) {
                'pending' => 'fas fa-clock',
                'cleared' => 'fas fa-check-circle',
                'bounced' => 'fas fa-exclamation-triangle',
                'cancelled' => 'fas fa-times-circle',
                default => 'fas fa-question-circle',
            };
        }

        return 'fas fa-question-circle';
    }

    /**
     * تحويل البيانات للرسم البياني
     */
    public static function prepareChartData($data, $labelKey = 'period', $valueKey = 'total'): array
    {
        return [
            'labels' => $data->pluck($labelKey)->toArray(),
            'datasets' => [
                [
                    'data' => $data->pluck($valueKey)->toArray(),
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ]
            ]
        ];
    }

    /**
     * حساب معدل النمو
     */
    public static function calculateGrowthRate($current, $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return (($current - $previous) / $previous) * 100;
    }

    /**
     * تحديد مستوى الأداء
     */
    public static function getPerformanceLevel($percentage): array
    {
        if ($percentage >= 90) {
            return ['level' => 'ممتاز', 'color' => 'success', 'icon' => 'fas fa-star'];
        } elseif ($percentage >= 75) {
            return ['level' => 'جيد جداً', 'color' => 'info', 'icon' => 'fas fa-thumbs-up'];
        } elseif ($percentage >= 60) {
            return ['level' => 'جيد', 'color' => 'warning', 'icon' => 'fas fa-check'];
        } elseif ($percentage >= 40) {
            return ['level' => 'مقبول', 'color' => 'secondary', 'icon' => 'fas fa-minus'];
        } else {
            return ['level' => 'ضعيف', 'color' => 'danger', 'icon' => 'fas fa-exclamation-triangle'];
        }
    }
}
