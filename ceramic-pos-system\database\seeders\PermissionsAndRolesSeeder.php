<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;
use App\Models\Tenant;

class PermissionsAndRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الصلاحيات الافتراضية
        Permission::createSystemPermissions();

        // إنشاء الأدوار الافتراضية لجميع الشركات
        $tenants = Tenant::all();

        foreach ($tenants as $tenant) {
            Role::createSystemRoles($tenant->id);
        }

        $this->command->info('تم إنشاء الصلاحيات والأدوار الافتراضية بنجاح');
    }
}
