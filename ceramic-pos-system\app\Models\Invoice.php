<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Traits\BelongsToTenant;

class Invoice extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'invoice_number',
        'invoice_type',
        'invoice_date',
        'due_date',
        'customer_id',
        'supplier_id',
        'customer_name',
        'customer_address',
        'customer_phone',
        'customer_tax_number',
        'warehouse_id',
        'status',
        'payment_status',
        'payment_method',
        'subtotal',
        'discount_amount',
        'discount_percentage',
        'tax_amount',
        'total_amount',
        'paid_amount',
        'remaining_amount',
        'notes',
        'terms_conditions',
        'metadata',
        'created_by',
        'updated_by',
        'confirmed_at',
        'confirmed_by',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'metadata' => 'array',
        'confirmed_at' => 'datetime',
    ];

    /**
     * أنواع الفواتير
     */
    const INVOICE_TYPES = [
        'sale' => 'فاتورة بيع',
        'purchase' => 'فاتورة شراء',
    ];

    /**
     * حالات الفاتورة
     */
    const STATUSES = [
        'draft' => 'مسودة',
        'confirmed' => 'مؤكدة',
        'paid' => 'مدفوعة',
        'cancelled' => 'ملغية',
    ];

    /**
     * حالات الدفع
     */
    const PAYMENT_STATUSES = [
        'unpaid' => 'غير مدفوعة',
        'partial' => 'مدفوعة جزئياً',
        'paid' => 'مدفوعة بالكامل',
    ];

    /**
     * طرق الدفع
     */
    const PAYMENT_METHODS = [
        'cash' => 'نقداً',
        'bank_transfer' => 'تحويل بنكي',
        'check' => 'شيك',
        'credit' => 'آجل',
        'card' => 'بطاقة ائتمان',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع المورد
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * العلاقة مع المخزن
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * العلاقة مع بنود الفاتورة
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع المستخدم المحدث
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * العلاقة مع المستخدم المؤكد
     */
    public function confirmedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'confirmed_by');
    }

    /**
     * العلاقة مع ربط المدفوعات
     */
    public function paymentInvoices(): HasMany
    {
        return $this->hasMany(PaymentInvoice::class);
    }

    /**
     * العلاقة مع المدفوعات (Many-to-Many)
     */
    public function payments(): BelongsToMany
    {
        return $this->belongsToMany(Payment::class, 'payment_invoices')
                    ->withPivot('amount', 'notes')
                    ->withTimestamps();
    }

    /**
     * Scope لفواتير البيع
     */
    public function scopeSales($query)
    {
        return $query->where('invoice_type', 'sale');
    }

    /**
     * Scope لفواتير الشراء
     */
    public function scopePurchases($query)
    {
        return $query->where('invoice_type', 'purchase');
    }

    /**
     * Scope للفواتير المؤكدة
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope للفواتير المستحقة
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'confirmed')
                    ->where('due_date', '<', now())
                    ->where('payment_status', '!=', 'paid');
    }

    /**
     * Scope للفواتير المدفوعة
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    /**
     * Scope للفواتير غير المدفوعة
     */
    public function scopeUnpaid($query)
    {
        return $query->where('payment_status', 'unpaid');
    }



    /**
     * Scope للبحث في الفواتير
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('invoice_number', 'like', "%{$search}%")
              ->orWhere('customer_name', 'like', "%{$search}%")
              ->orWhereHas('customer', function($customerQuery) use ($search) {
                  $customerQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('customer_code', 'like', "%{$search}%");
              })
              ->orWhereHas('supplier', function($supplierQuery) use ($search) {
                  $supplierQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('supplier_code', 'like', "%{$search}%");
              });
        });
    }

    /**
     * الحصول على اسم نوع الفاتورة
     */
    public function getInvoiceTypeNameAttribute(): string
    {
        return self::INVOICE_TYPES[$this->invoice_type] ?? $this->invoice_type;
    }

    /**
     * الحصول على اسم حالة الفاتورة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * الحصول على اسم حالة الدفع
     */
    public function getPaymentStatusNameAttribute(): string
    {
        return self::PAYMENT_STATUSES[$this->payment_status] ?? $this->payment_status;
    }

    /**
     * الحصول على اسم طريقة الدفع
     */
    public function getPaymentMethodNameAttribute(): string
    {
        return self::PAYMENT_METHODS[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * التحقق من كون الفاتورة فاتورة بيع
     */
    public function isSale(): bool
    {
        return $this->invoice_type === 'sale';
    }

    /**
     * التحقق من كون الفاتورة فاتورة شراء
     */
    public function isPurchase(): bool
    {
        return $this->invoice_type === 'purchase';
    }

    /**
     * التحقق من كون الفاتورة مؤكدة
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * التحقق من كون الفاتورة مدفوعة
     */
    public function isPaid(): bool
    {
        return $this->payment_status === 'paid';
    }

    /**
     * التحقق من كون الفاتورة مستحقة
     */
    public function isOverdue(): bool
    {
        return $this->due_date && $this->due_date->isPast() && !$this->isPaid();
    }

    /**
     * التحقق من إمكانية التعديل
     */
    public function canEdit(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * التحقق من إمكانية الحذف
     */
    public function canDelete(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * الحصول على لون حالة الفاتورة
     */
    public function getStatusColorAttribute(): string
    {
        $colors = [
            'draft' => 'secondary',
            'confirmed' => 'primary',
            'paid' => 'success',
            'cancelled' => 'danger',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * الحصول على لون حالة الدفع
     */
    public function getPaymentStatusColorAttribute(): string
    {
        $colors = [
            'unpaid' => 'danger',
            'partial' => 'warning',
            'paid' => 'success',
        ];

        return $colors[$this->payment_status] ?? 'secondary';
    }

    /**
     * حساب المجاميع
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum(function($item) {
            return $item->quantity * $item->unit_price;
        });

        $this->tax_amount = $this->items->sum('tax_amount');
        $this->discount_amount = $this->items->sum('discount_amount');

        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->remaining_amount = $this->total_amount - $this->paid_amount;

        // تحديث حالة الدفع
        if ($this->paid_amount <= 0) {
            $this->payment_status = 'unpaid';
        } elseif ($this->paid_amount >= $this->total_amount) {
            $this->payment_status = 'paid';
        } else {
            $this->payment_status = 'partial';
        }

        $this->save();
    }

    /**
     * تأكيد الفاتورة
     */
    public function confirm(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $this->status = 'confirmed';
        $this->confirmed_at = now();
        $this->confirmed_by = auth()->id();
        $this->save();

        // تحديث المخزون
        $this->updateStock();

        // تحديث أرصدة العملاء/الموردين
        $this->updateCustomerSupplierBalance();

        return true;
    }

    /**
     * إلغاء الفاتورة
     */
    public function cancel(): bool
    {
        if (!in_array($this->status, ['draft', 'confirmed'])) {
            return false;
        }

        // إذا كانت مؤكدة، استرجع المخزون والأرصدة
        if ($this->status === 'confirmed') {
            $this->reverseStock();
            $this->reverseCustomerSupplierBalance();
        }

        $this->status = 'cancelled';
        $this->save();

        return true;
    }

    /**
     * تحديث المخزون
     */
    private function updateStock(): void
    {
        foreach ($this->items as $item) {
            if ($item->product_id && $this->warehouse_id) {
                if ($this->isSale()) {
                    // خصم من المخزون
                    $this->warehouse->updateProductStock(
                        $item->product_id,
                        $item->quantity,
                        'subtract'
                    );

                    // تسجيل حركة إخراج
                    StockMovement::createOutboundMovement(
                        $item->product_id,
                        $this->warehouse_id,
                        $item->quantity,
                        $item->unit_price,
                        'invoice',
                        $this->id,
                        $this->invoice_number,
                        'بيع - فاتورة رقم ' . $this->invoice_number
                    );
                } else {
                    // إضافة للمخزون
                    $this->warehouse->updateProductStock(
                        $item->product_id,
                        $item->quantity,
                        'add',
                        $item->unit_price
                    );

                    // تسجيل حركة إدخال
                    StockMovement::createInboundMovement(
                        $item->product_id,
                        $this->warehouse_id,
                        $item->quantity,
                        $item->unit_price,
                        'invoice',
                        $this->id,
                        $this->invoice_number,
                        'شراء - فاتورة رقم ' . $this->invoice_number
                    );
                }
            }
        }
    }

    /**
     * استرجاع المخزون
     */
    private function reverseStock(): void
    {
        foreach ($this->items as $item) {
            if ($item->product_id && $this->warehouse_id) {
                if ($this->isSale()) {
                    // إضافة للمخزون (عكس الخصم)
                    $this->warehouse->updateProductStock(
                        $item->product_id,
                        $item->quantity,
                        'add',
                        $item->unit_price
                    );
                } else {
                    // خصم من المخزون (عكس الإضافة)
                    $this->warehouse->updateProductStock(
                        $item->product_id,
                        $item->quantity,
                        'subtract'
                    );
                }
            }
        }
    }

    /**
     * تحديث أرصدة العملاء/الموردين
     */
    private function updateCustomerSupplierBalance(): void
    {
        if ($this->isSale() && $this->customer_id) {
            $this->customer->updateBalance($this->total_amount, 'debit');
        } elseif ($this->isPurchase() && $this->supplier_id) {
            $this->supplier->updateBalance($this->total_amount, 'credit');
        }
    }

    /**
     * استرجاع أرصدة العملاء/الموردين
     */
    private function reverseCustomerSupplierBalance(): void
    {
        if ($this->isSale() && $this->customer_id) {
            $this->customer->updateBalance($this->total_amount, 'credit');
        } elseif ($this->isPurchase() && $this->supplier_id) {
            $this->supplier->updateBalance($this->total_amount, 'debit');
        }
    }

    /**
     * إنشاء رقم فاتورة تلقائي
     */
    public static function generateInvoiceNumber(string $type): string
    {
        $prefix = $type === 'sale' ? 'SAL' : 'PUR';
        $year = date('Y');
        $month = date('m');

        $lastInvoice = self::where('invoice_type', $type)
                          ->where('invoice_number', 'like', "{$prefix}-{$year}{$month}%")
                          ->orderBy('invoice_number', 'desc')
                          ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('%s-%s%s%04d', $prefix, $year, $month, $newNumber);
    }
}
