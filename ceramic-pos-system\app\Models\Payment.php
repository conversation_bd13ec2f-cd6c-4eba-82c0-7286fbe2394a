<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Models\Traits\BelongsToTenant;

class Payment extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'payment_number',
        'payment_type',
        'payment_date',
        'customer_id',
        'supplier_id',
        'payer_name',
        'payment_method',
        'amount',
        'currency',
        'exchange_rate',
        'amount_in_base_currency',
        'bank_name',
        'account_number',
        'check_number',
        'check_date',
        'reference_number',
        'transaction_id',
        'status',
        'cleared_date',
        'notes',
        'metadata',
        'created_by',
        'updated_by',
        'cleared_at',
        'cleared_by',
    ];

    protected $casts = [
        'payment_date' => 'date',
        'check_date' => 'date',
        'cleared_date' => 'date',
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'amount_in_base_currency' => 'decimal:2',
        'metadata' => 'array',
        'cleared_at' => 'datetime',
    ];

    /**
     * أنواع المدفوعات
     */
    const PAYMENT_TYPES = [
        'received' => 'مقبوضات',
        'paid' => 'مدفوعات',
    ];

    /**
     * طرق الدفع
     */
    const PAYMENT_METHODS = [
        'cash' => 'نقداً',
        'bank_transfer' => 'تحويل بنكي',
        'check' => 'شيك',
        'card' => 'بطاقة ائتمان',
        'other' => 'أخرى',
    ];

    /**
     * حالات المدفوعة
     */
    const STATUSES = [
        'pending' => 'معلقة',
        'cleared' => 'مقاصة',
        'bounced' => 'مرتدة',
        'cancelled' => 'ملغية',
    ];

    /**
     * العملات المدعومة
     */
    const CURRENCIES = [
        'EGP' => 'جنيه مصري',
        'USD' => 'دولار أمريكي',
        'EUR' => 'يورو',
        'SAR' => 'ريال سعودي',
        'AED' => 'درهم إماراتي',
    ];

    /**
     * العلاقة مع العميل
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * العلاقة مع المورد
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع المستخدم المحدث
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * العلاقة مع المستخدم الذي قام بالمقاصة
     */
    public function clearedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cleared_by');
    }

    /**
     * العلاقة مع ربط الفواتير
     */
    public function paymentInvoices(): HasMany
    {
        return $this->hasMany(PaymentInvoice::class);
    }

    /**
     * العلاقة مع الفواتير (Many-to-Many)
     */
    public function invoices(): BelongsToMany
    {
        return $this->belongsToMany(Invoice::class, 'payment_invoices')
                    ->withPivot('amount', 'notes')
                    ->withTimestamps();
    }

    /**
     * Scope للمقبوضات
     */
    public function scopeReceived($query)
    {
        return $query->where('payment_type', 'received');
    }

    /**
     * Scope للمدفوعات
     */
    public function scopePaid($query)
    {
        return $query->where('payment_type', 'paid');
    }

    /**
     * Scope للمدفوعات المقاصة
     */
    public function scopeCleared($query)
    {
        return $query->where('status', 'cleared');
    }

    /**
     * Scope للمدفوعات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope للمدفوعات المرتدة
     */
    public function scopeBounced($query)
    {
        return $query->where('status', 'bounced');
    }

    /**
     * Scope للبحث في المدفوعات
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('payment_number', 'like', "%{$search}%")
              ->orWhere('payer_name', 'like', "%{$search}%")
              ->orWhere('reference_number', 'like', "%{$search}%")
              ->orWhere('check_number', 'like', "%{$search}%")
              ->orWhereHas('customer', function($customerQuery) use ($search) {
                  $customerQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('customer_code', 'like', "%{$search}%");
              })
              ->orWhereHas('supplier', function($supplierQuery) use ($search) {
                  $supplierQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('supplier_code', 'like', "%{$search}%");
              });
        });
    }

    /**
     * الحصول على اسم نوع المدفوعة
     */
    public function getPaymentTypeNameAttribute(): string
    {
        return self::PAYMENT_TYPES[$this->payment_type] ?? $this->payment_type;
    }

    /**
     * الحصول على اسم طريقة الدفع
     */
    public function getPaymentMethodNameAttribute(): string
    {
        return self::PAYMENT_METHODS[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * الحصول على اسم العملة
     */
    public function getCurrencyNameAttribute(): string
    {
        return self::CURRENCIES[$this->currency] ?? $this->currency;
    }

    /**
     * التحقق من كون المدفوعة مقبوضات
     */
    public function isReceived(): bool
    {
        return $this->payment_type === 'received';
    }

    /**
     * التحقق من كون المدفوعة مدفوعات
     */
    public function isPaid(): bool
    {
        return $this->payment_type === 'paid';
    }

    /**
     * التحقق من كون المدفوعة مقاصة
     */
    public function isCleared(): bool
    {
        return $this->status === 'cleared';
    }

    /**
     * التحقق من كون المدفوعة معلقة
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * التحقق من إمكانية التعديل
     */
    public function canEdit(): bool
    {
        return in_array($this->status, ['pending']);
    }

    /**
     * التحقق من إمكانية الحذف
     */
    public function canDelete(): bool
    {
        return in_array($this->status, ['pending']);
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute(): string
    {
        $colors = [
            'pending' => 'warning',
            'cleared' => 'success',
            'bounced' => 'danger',
            'cancelled' => 'secondary',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * الحصول على أيقونة طريقة الدفع
     */
    public function getPaymentMethodIconAttribute(): string
    {
        $icons = [
            'cash' => 'fas fa-money-bill-wave',
            'bank_transfer' => 'fas fa-university',
            'check' => 'fas fa-money-check',
            'card' => 'fas fa-credit-card',
            'other' => 'fas fa-question-circle',
        ];

        return $icons[$this->payment_method] ?? 'fas fa-question-circle';
    }

    /**
     * مقاصة المدفوعة
     */
    public function clear(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->status = 'cleared';
        $this->cleared_date = now();
        $this->cleared_at = now();
        $this->cleared_by = auth()->id();
        $this->save();

        // تحديث أرصدة الفواتير المرتبطة
        $this->updateInvoiceBalances();

        return true;
    }

    /**
     * إلغاء المدفوعة
     */
    public function cancel(): bool
    {
        if (!in_array($this->status, ['pending', 'cleared'])) {
            return false;
        }

        // إذا كانت مقاصة، استرجع الأرصدة
        if ($this->status === 'cleared') {
            $this->reverseInvoiceBalances();
        }

        $this->status = 'cancelled';
        $this->save();

        return true;
    }

    /**
     * تحديث أرصدة الفواتير
     */
    private function updateInvoiceBalances(): void
    {
        foreach ($this->paymentInvoices as $paymentInvoice) {
            $invoice = $paymentInvoice->invoice;
            $invoice->paid_amount += $paymentInvoice->amount;
            $invoice->remaining_amount = $invoice->total_amount - $invoice->paid_amount;

            // تحديث حالة الدفع
            if ($invoice->remaining_amount <= 0) {
                $invoice->payment_status = 'paid';
            } elseif ($invoice->paid_amount > 0) {
                $invoice->payment_status = 'partial';
            }

            $invoice->save();
        }
    }

    /**
     * استرجاع أرصدة الفواتير
     */
    private function reverseInvoiceBalances(): void
    {
        foreach ($this->paymentInvoices as $paymentInvoice) {
            $invoice = $paymentInvoice->invoice;
            $invoice->paid_amount -= $paymentInvoice->amount;
            $invoice->remaining_amount = $invoice->total_amount - $invoice->paid_amount;

            // تحديث حالة الدفع
            if ($invoice->paid_amount <= 0) {
                $invoice->payment_status = 'unpaid';
            } elseif ($invoice->remaining_amount > 0) {
                $invoice->payment_status = 'partial';
            }

            $invoice->save();
        }
    }

    /**
     * إنشاء رقم مدفوعة تلقائي
     */
    public static function generatePaymentNumber(string $type): string
    {
        $prefix = $type === 'received' ? 'REC' : 'PAY';
        $year = date('Y');
        $month = date('m');

        $lastPayment = self::where('payment_type', $type)
                          ->where('payment_number', 'like', "{$prefix}-{$year}{$month}%")
                          ->orderBy('payment_number', 'desc')
                          ->first();

        if ($lastPayment) {
            $lastNumber = (int) substr($lastPayment->payment_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('%s-%s%s%04d', $prefix, $year, $month, $newNumber);
    }
}
