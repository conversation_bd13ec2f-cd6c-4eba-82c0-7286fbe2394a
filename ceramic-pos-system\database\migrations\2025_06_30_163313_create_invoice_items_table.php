<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('invoice_id')->constrained('invoices')->onDelete('cascade');
            $table->foreignId('product_id')->nullable()->constrained('products')->onDelete('set null');

            // معلومات المنتج (نسخة احتياطية)
            $table->string('product_code')->nullable();
            $table->string('product_name');
            $table->string('product_description')->nullable();
            $table->string('unit')->nullable();

            // الكميات والأسعار
            $table->decimal('quantity', 10, 3); // الكمية
            $table->decimal('unit_price', 10, 2); // سعر الوحدة
            $table->decimal('discount_amount', 10, 2)->default(0); // خصم البند
            $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة خصم البند
            $table->decimal('tax_amount', 10, 2)->default(0); // ضريبة البند
            $table->decimal('tax_percentage', 5, 2)->default(0); // نسبة ضريبة البند
            $table->decimal('total_amount', 15, 2); // إجمالي البند

            // معلومات إضافية
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['tenant_id', 'invoice_id']);
            $table->index(['tenant_id', 'product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_items');
    }
};
