<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('product_code', 50)->unique(); // رقم المنتج
            $table->string('barcode')->nullable()->unique(); // الباركود
            $table->string('name'); // اسم المنتج
            $table->string('name_en')->nullable(); // اسم المنتج بالإنجليزية
            $table->text('description')->nullable(); // وصف المنتج
            $table->enum('product_type', ['ceramic', 'sanitary', 'accessory', 'other'])->default('ceramic'); // نوع المنتج
            $table->string('category')->nullable(); // الفئة
            $table->string('brand')->nullable(); // العلامة التجارية
            $table->string('model')->nullable(); // الموديل
            $table->string('size')->nullable(); // الحجم
            $table->string('color')->nullable(); // اللون
            $table->string('material')->nullable(); // المادة
            $table->string('origin_country')->nullable(); // بلد المنشأ
            $table->string('unit', 10)->default('piece'); // وحدة القياس
            $table->decimal('purchase_price', 10, 2)->default(0); // سعر الشراء
            $table->decimal('selling_price', 10, 2)->default(0); // سعر البيع
            $table->decimal('wholesale_price', 10, 2)->nullable(); // سعر الجملة
            $table->decimal('minimum_price', 10, 2)->nullable(); // أقل سعر بيع
            $table->decimal('weight', 8, 3)->nullable(); // الوزن
            $table->string('dimensions')->nullable(); // الأبعاد
            $table->integer('minimum_stock')->default(0); // الحد الأدنى للمخزون
            $table->integer('maximum_stock')->nullable(); // الحد الأقصى للمخزون
            $table->integer('reorder_level')->nullable(); // نقطة إعادة الطلب
            $table->boolean('track_quantity')->default(true); // تتبع الكمية
            $table->boolean('is_active')->default(true);
            $table->string('image')->nullable(); // صورة المنتج
            $table->json('images')->nullable(); // صور إضافية
            $table->json('specifications')->nullable(); // المواصفات التقنية
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['tenant_id', 'product_code']);
            $table->index(['tenant_id', 'product_type']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['tenant_id', 'category']);
            $table->index('barcode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
