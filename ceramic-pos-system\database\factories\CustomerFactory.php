<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $customerTypes = ['individual', 'company'];
        $balanceTypes = ['debit', 'credit'];
        $countries = ['EG', 'SA', 'AE', 'KW', 'QA'];

        $customerType = $this->faker->randomElement($customerTypes);
        $isCompany = $customerType === 'company';

        return [
            'tenant_id' => 1, // سيتم تحديثه عند الاستخدام
            'customer_code' => 'C' . $this->faker->unique()->numberBetween(1000, 9999),
            'name' => $this->faker->name(),
            'company_name' => $isCompany ? $this->faker->company() : null,
            'customer_type' => $customerType,
            'tax_number' => $isCompany ? $this->faker->numerify('###########') : null,
            'commercial_register' => $isCompany ? $this->faker->numerify('########') : null,
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'mobile' => $this->faker->phoneNumber(),
            'fax' => $this->faker->optional()->phoneNumber(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'postal_code' => $this->faker->postcode(),
            'country' => $this->faker->randomElement($countries),
            'credit_limit' => $this->faker->randomFloat(2, 1000, 50000),
            'payment_terms' => $this->faker->randomElement([15, 30, 45, 60]),
            'opening_balance' => $this->faker->randomFloat(2, 0, 10000),
            'current_balance' => $this->faker->randomFloat(2, 0, 10000),
            'balance_type' => $this->faker->randomElement($balanceTypes),
            'is_active' => $this->faker->boolean(90), // 90% نشط
            'notes' => $this->faker->optional()->sentence(),
            'additional_info' => null,
        ];
    }

    /**
     * عميل نشط
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    /**
     * عميل غير نشط
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * عميل فرد
     */
    public function individual()
    {
        return $this->state(function (array $attributes) {
            return [
                'customer_type' => 'individual',
                'company_name' => null,
                'tax_number' => null,
                'commercial_register' => null,
            ];
        });
    }

    /**
     * عميل شركة
     */
    public function company()
    {
        return $this->state(function (array $attributes) {
            return [
                'customer_type' => 'company',
                'company_name' => $this->faker->company(),
                'tax_number' => $this->faker->numerify('###########'),
                'commercial_register' => $this->faker->numerify('########'),
            ];
        });
    }

    /**
     * عميل مدين
     */
    public function debit()
    {
        return $this->state(function (array $attributes) {
            return [
                'balance_type' => 'debit',
                'current_balance' => $this->faker->randomFloat(2, 100, 5000),
            ];
        });
    }

    /**
     * عميل دائن
     */
    public function credit()
    {
        return $this->state(function (array $attributes) {
            return [
                'balance_type' => 'credit',
                'current_balance' => $this->faker->randomFloat(2, 100, 2000),
            ];
        });
    }

    /**
     * عميل متأخر في السداد
     */
    public function overdue()
    {
        return $this->state(function (array $attributes) {
            return [
                'balance_type' => 'debit',
                'current_balance' => $this->faker->randomFloat(2, 1000, 10000),
            ];
        });
    }

    /**
     * عميل تجاوز حد الائتمان
     */
    public function overCreditLimit()
    {
        $creditLimit = $this->faker->randomFloat(2, 5000, 20000);

        return $this->state(function (array $attributes) use ($creditLimit) {
            return [
                'balance_type' => 'debit',
                'credit_limit' => $creditLimit,
                'current_balance' => $creditLimit + $this->faker->randomFloat(2, 1000, 5000),
            ];
        });
    }
}
