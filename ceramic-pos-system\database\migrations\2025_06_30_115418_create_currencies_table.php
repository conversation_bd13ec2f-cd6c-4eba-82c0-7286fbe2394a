<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('code', 3)->unique(); // رمز العملة (SAR, USD, EUR)
            $table->string('name'); // اسم العملة
            $table->string('name_ar'); // اسم العملة بالعربية
            $table->string('symbol', 5); // رمز العملة ($, ر.س)
            $table->decimal('exchange_rate', 10, 4)->default(1); // سعر الصرف مقابل العملة الأساسية
            $table->boolean('is_base')->default(false); // العملة الأساسية
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index('code');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
