@extends('layouts.app')

@section('title', 'إضافة عميل جديد')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-user-plus me-2 text-primary"></i>
                إضافة عميل جديد
            </h2>
            <p class="text-muted mb-0">{{ $tenant->company_name }}</p>
        </div>
        <div>
            <a href="{{ route('customers.index', ['tenant' => $tenant->id]) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ route('customers.store') }}">
                        @csrf
                        <input type="hidden" name="tenant" value="{{ $tenant->id }}">
                        
                        <!-- نوع العميل -->
                        <div class="mb-4">
                            <label class="form-label">نوع العميل *</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="type" id="individual" 
                                               value="individual" {{ old('type', 'individual') == 'individual' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="individual">
                                            <i class="fas fa-user me-2"></i>
                                            فرد
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="type" id="company" 
                                               value="company" {{ old('type') == 'company' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="company">
                                            <i class="fas fa-building me-2"></i>
                                            شركة
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- اسم العميل -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم العميل *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- رقم الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone') }}" required>
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email') }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- المدينة -->
                            <div class="col-md-6 mb-3">
                                <label for="city" class="form-label">المدينة</label>
                                <select class="form-select @error('city') is-invalid @enderror" id="city" name="city">
                                    <option value="">اختر المدينة</option>
                                    <option value="القاهرة" {{ old('city') == 'القاهرة' ? 'selected' : '' }}>القاهرة</option>
                                    <option value="الإسكندرية" {{ old('city') == 'الإسكندرية' ? 'selected' : '' }}>الإسكندرية</option>
                                    <option value="الجيزة" {{ old('city') == 'الجيزة' ? 'selected' : '' }}>الجيزة</option>
                                    <option value="شبرا الخيمة" {{ old('city') == 'شبرا الخيمة' ? 'selected' : '' }}>شبرا الخيمة</option>
                                    <option value="بورسعيد" {{ old('city') == 'بورسعيد' ? 'selected' : '' }}>بورسعيد</option>
                                    <option value="السويس" {{ old('city') == 'السويس' ? 'selected' : '' }}>السويس</option>
                                    <option value="المحلة الكبرى" {{ old('city') == 'المحلة الكبرى' ? 'selected' : '' }}>المحلة الكبرى</option>
                                    <option value="طنطا" {{ old('city') == 'طنطا' ? 'selected' : '' }}>طنطا</option>
                                    <option value="أسيوط" {{ old('city') == 'أسيوط' ? 'selected' : '' }}>أسيوط</option>
                                    <option value="الفيوم" {{ old('city') == 'الفيوم' ? 'selected' : '' }}>الفيوم</option>
                                    <option value="الزقازيق" {{ old('city') == 'الزقازيق' ? 'selected' : '' }}>الزقازيق</option>
                                    <option value="أسوان" {{ old('city') == 'أسوان' ? 'selected' : '' }}>أسوان</option>
                                    <option value="دمياط" {{ old('city') == 'دمياط' ? 'selected' : '' }}>دمياط</option>
                                    <option value="المنيا" {{ old('city') == 'المنيا' ? 'selected' : '' }}>المنيا</option>
                                    <option value="بني سويف" {{ old('city') == 'بني سويف' ? 'selected' : '' }}>بني سويف</option>
                                    <option value="قنا" {{ old('city') == 'قنا' ? 'selected' : '' }}>قنا</option>
                                    <option value="سوهاج" {{ old('city') == 'سوهاج' ? 'selected' : '' }}>سوهاج</option>
                                    <option value="الإسماعيلية" {{ old('city') == 'الإسماعيلية' ? 'selected' : '' }}>الإسماعيلية</option>
                                    <option value="كفر الشيخ" {{ old('city') == 'كفر الشيخ' ? 'selected' : '' }}>كفر الشيخ</option>
                                    <option value="البحيرة" {{ old('city') == 'البحيرة' ? 'selected' : '' }}>البحيرة</option>
                                    <option value="الأقصر" {{ old('city') == 'الأقصر' ? 'selected' : '' }}>الأقصر</option>
                                    <option value="أخرى" {{ old('city') == 'أخرى' ? 'selected' : '' }}>أخرى</option>
                                </select>
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- العنوان -->
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="3">{{ old('address') }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- معلومات إضافية للشركات -->
                        <div id="company-fields" style="display: none;">
                            <div class="row">
                                <!-- الرقم الضريبي -->
                                <div class="col-md-6 mb-3">
                                    <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control @error('tax_number') is-invalid @enderror" 
                                           id="tax_number" name="tax_number" value="{{ old('tax_number') }}">
                                    @error('tax_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <!-- الحد الائتماني -->
                                <div class="col-md-6 mb-3">
                                    <label for="credit_limit" class="form-label">الحد الائتماني</label>
                                    <div class="input-group">
                                        <input type="number" step="0.01" min="0" 
                                               class="form-control @error('credit_limit') is-invalid @enderror" 
                                               id="credit_limit" name="credit_limit" value="{{ old('credit_limit') }}">
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                    @error('credit_limit')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <!-- الحالة -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    عميل نشط
                                </label>
                            </div>
                        </div>
                        
                        <!-- أزرار الحفظ -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ العميل
                            </button>
                            <button type="submit" name="save_and_new" value="1" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                حفظ وإضافة آخر
                            </button>
                            <a href="{{ route('customers.index', ['tenant' => $tenant->id]) }}" class="btn btn-outline-secondary">
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح
                    </h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من صحة رقم الهاتف للتواصل
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            أضف البريد الإلكتروني لإرسال الفواتير
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            حدد الحد الائتماني للشركات
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            أضف الرقم الضريبي للشركات
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-info-circle me-2"></i>
                        الفرق بين الفرد والشركة
                    </h6>
                    <div class="mb-3">
                        <strong>الفرد:</strong>
                        <ul class="mt-2">
                            <li>عميل شخصي</li>
                            <li>لا يحتاج رقم ضريبي</li>
                            <li>حد ائتماني محدود</li>
                        </ul>
                    </div>
                    <div>
                        <strong>الشركة:</strong>
                        <ul class="mt-2">
                            <li>عميل تجاري</li>
                            <li>يحتاج رقم ضريبي</li>
                            <li>حد ائتماني أعلى</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// إظهار/إخفاء حقول الشركة
function toggleCompanyFields() {
    const companyFields = document.getElementById('company-fields');
    const companyRadio = document.getElementById('company');
    
    if (companyRadio.checked) {
        companyFields.style.display = 'block';
    } else {
        companyFields.style.display = 'none';
    }
}

// ربط الأحداث
document.getElementById('individual').addEventListener('change', toggleCompanyFields);
document.getElementById('company').addEventListener('change', toggleCompanyFields);

// تشغيل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', toggleCompanyFields);

// تنسيق رقم الهاتف
document.getElementById('phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
    
    if (value.length > 0) {
        // تنسيق الرقم المصري
        if (value.startsWith('20')) {
            value = '+' + value;
        } else if (value.startsWith('01')) {
            value = '+20' + value.substring(1);
        } else if (value.length === 10 && value.startsWith('1')) {
            value = '+20' + value;
        }
    }
    
    this.value = value;
});
</script>
@endpush
@endsection
