<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تعطيل foreign key checks مؤقتاً
        Schema::disableForeignKeyConstraints();

        // إسقاط الجدول الحالي وإعادة إنشاؤه بالشكل الصحيح لـ Stancl Tenancy
        Schema::dropIfExists('tenants');

        Schema::create('tenants', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->timestamps();
            $table->json('data')->nullable();
        });

        // إعادة تفعيل foreign key checks
        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');

        // إعادة إنشاء الجدول القديم
        Schema::create('tenants', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('domain')->nullable();
            $table->string('subdomain')->unique();
            $table->string('database_name')->nullable();
            $table->string('logo')->nullable();
            $table->text('description')->nullable();
            $table->json('settings')->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->enum('plan', ['basic', 'standard', 'premium'])->default('basic');
            $table->date('subscription_start')->nullable();
            $table->date('subscription_end')->nullable();
            $table->integer('max_users')->default(5);
            $table->json('features')->nullable();
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->default('EG');
            $table->string('currency', 3)->default('EGP');
            $table->string('timezone')->default('Africa/Cairo');
            $table->string('language', 2)->default('ar');
            $table->timestamps();

            $table->index(['status', 'subscription_end']);
            $table->index('slug');
            $table->index('subdomain');
        });
    }
};
