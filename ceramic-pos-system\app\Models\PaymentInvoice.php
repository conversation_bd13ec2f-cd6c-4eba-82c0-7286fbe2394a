<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Traits\BelongsToTenant;

class PaymentInvoice extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'payment_id',
        'invoice_id',
        'amount',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    /**
     * العلاقة مع المدفوعة
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * العلاقة مع الفاتورة
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * التحقق من صحة المبلغ
     */
    public function validateAmount(): bool
    {
        // التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي في الفاتورة
        $remainingAmount = $this->invoice->remaining_amount;

        // إذا كان هذا تحديث، أضف المبلغ السابق للمبلغ المتبقي
        if ($this->exists) {
            $remainingAmount += $this->getOriginal('amount');
        }

        return $this->amount <= $remainingAmount;
    }

    /**
     * التحقق من توافق نوع المدفوعة مع نوع الفاتورة
     */
    public function validatePaymentType(): bool
    {
        $paymentType = $this->payment->payment_type;
        $invoiceType = $this->invoice->invoice_type;

        // المقبوضات للبيع، المدفوعات للشراء
        return ($paymentType === 'received' && $invoiceType === 'sale') ||
               ($paymentType === 'paid' && $invoiceType === 'purchase');
    }
}
