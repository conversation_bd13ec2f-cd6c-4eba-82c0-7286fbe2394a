name: Run tests

on:
    push:
    pull_request:
    schedule:
        -   cron: '0 0 * * *'

jobs:
    php-tests:
        runs-on: ${{ matrix.os }}

        strategy:
            matrix:
                php: [ 8.0, 7.4, 7.3 ]
                dependency-version: [ prefer-lowest, prefer-stable ]
                os: [ ubuntu-latest, windows-latest ]
                allow_failures:
                    -   php: 8.0

        name: P${{ matrix.php }} - ${{ matrix.dependency-version }} - ${{ matrix.os }}

        steps:
            -   name: Checkout code
                uses: actions/checkout@v2

            -   name: Set up PHP
                uses: shivammathur/setup-php@v2
                with:
                    php-version: ${{ matrix.php }}
                    coverage: none
                    tools: composer:v2

            -   name: Install PHP 7 dependencies
                run: composer update --${{ matrix.dependency-version }} --no-interaction --no-progress
                if: "matrix.php < 8"

            -   name: Install PHP 8 dependencies
                run: composer update --prefer-stable --ignore-platform-req=php --no-interaction --no-progress
                if: "matrix.php >= 8"

            -   name: Execute tests
                run: vendor/bin/phpunit
