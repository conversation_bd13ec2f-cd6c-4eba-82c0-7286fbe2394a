{"name": "stancl/tenancy", "description": "Automatic multi-tenancy for your Laravel application.", "keywords": ["laravel", "multi-tenancy", "multi-database", "tenancy"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0", "ext-json": "*", "illuminate/support": "^10.0|^11.0|^12.0", "facade/ignition-contracts": "^1.0.2", "ramsey/uuid": "^4.7.3", "stancl/jobpipeline": "^1.8.0", "stancl/virtualcolumn": "^1.5.0"}, "require-dev": {"laravel/framework": "^10.0|^11.0|^12.0", "orchestra/testbench": "^8.0|^9.0|^10.0", "league/flysystem-aws-s3-v3": "^3.12.2", "doctrine/dbal": "^3.6.0", "spatie/valuestore": "^1.3.2"}, "autoload": {"psr-4": {"Stancl\\Tenancy\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Stancl\\Tenancy\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["Stancl\\Tenancy\\TenancyServiceProvider"], "aliases": {"Tenancy": "Stancl\\Tenancy\\Facades\\Tenancy", "GlobalCache": "Stancl\\Tenancy\\Facades\\GlobalCache"}}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"pestphp/pest-plugin": true}}}