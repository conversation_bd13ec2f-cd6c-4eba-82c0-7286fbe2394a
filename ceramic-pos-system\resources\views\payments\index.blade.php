@extends('layouts.app')

@section('title', 'إدارة المدفوعات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-credit-card me-2 text-primary"></i>
                إدارة المدفوعات
            </h2>
            <p class="text-muted mb-0">{{ $tenant->company_name }}</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentModal">
                <i class="fas fa-plus me-2"></i>
                إضافة دفعة جديدة
            </button>
        </div>
    </div>

    <!-- Payment Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                    <h3 class="text-success">
                        {{ number_format(\App\Models\Payment::sum('amount'), 2) }}
                    </h3>
                    <p class="text-muted mb-0">إجمالي المدفوعات (ج.م)</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-hand-holding-usd fa-2x"></i>
                    </div>
                    <h3 class="text-primary">
                        {{ number_format(\App\Models\Payment::where('payment_method', 'cash')->sum('amount'), 2) }}
                    </h3>
                    <p class="text-muted mb-0">المدفوعات النقدية (ج.م)</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-credit-card fa-2x"></i>
                    </div>
                    <h3 class="text-info">
                        {{ number_format(\App\Models\Payment::where('payment_method', 'card')->sum('amount'), 2) }}
                    </h3>
                    <p class="text-muted mb-0">مدفوعات البطاقات (ج.م)</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h3 class="text-warning">
                        {{ number_format(\App\Models\Invoice::where('payment_status', 'pending')->sum('remaining_amount'), 2) }}
                    </h3>
                    <p class="text-muted mb-0">مدفوعات معلقة (ج.م)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <input type="hidden" name="tenant" value="{{ $tenant->id }}">
                
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">طريقة الدفع</label>
                    <select name="payment_method" class="form-select">
                        <option value="">جميع الطرق</option>
                        <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>نقدي</option>
                        <option value="card" {{ request('payment_method') == 'card' ? 'selected' : '' }}>بطاقة</option>
                        <option value="bank_transfer" {{ request('payment_method') == 'bank_transfer' ? 'selected' : '' }}>تحويل بنكي</option>
                        <option value="credit" {{ request('payment_method') == 'credit' ? 'selected' : '' }}>آجل</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                آخر المدفوعات
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>الملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            // محاكاة بيانات المدفوعات
                            $samplePayments = [
                                [
                                    'date' => '2025/06/30',
                                    'invoice_number' => 'SAL-20250630-0001',
                                    'customer' => 'أحمد محمد',
                                    'amount' => 1500.00,
                                    'method' => 'cash',
                                    'status' => 'completed',
                                    'notes' => 'دفعة كاملة'
                                ],
                                [
                                    'date' => '2025/06/30',
                                    'invoice_number' => 'SAL-20250630-0002',
                                    'customer' => 'فاطمة أحمد',
                                    'amount' => 2300.00,
                                    'method' => 'card',
                                    'status' => 'completed',
                                    'notes' => 'دفع بالفيزا'
                                ],
                                [
                                    'date' => '2025/06/29',
                                    'invoice_number' => 'SAL-********-0015',
                                    'customer' => 'شركة البناء الحديث',
                                    'amount' => 5000.00,
                                    'method' => 'bank_transfer',
                                    'status' => 'pending',
                                    'notes' => 'في انتظار التأكيد'
                                ],
                                [
                                    'date' => '2025/06/29',
                                    'invoice_number' => 'SAL-********-0012',
                                    'customer' => 'محمد حسن',
                                    'amount' => 800.00,
                                    'method' => 'cash',
                                    'status' => 'completed',
                                    'notes' => 'دفعة جزئية'
                                ],
                            ];
                        @endphp
                        
                        @foreach($samplePayments as $payment)
                        <tr>
                            <td>{{ $payment['date'] }}</td>
                            <td>
                                <a href="#" class="text-decoration-none">
                                    {{ $payment['invoice_number'] }}
                                </a>
                            </td>
                            <td>{{ $payment['customer'] }}</td>
                            <td>{{ number_format($payment['amount'], 2) }} ج.م</td>
                            <td>
                                @switch($payment['method'])
                                    @case('cash')
                                        <span class="badge bg-success">نقدي</span>
                                        @break
                                    @case('card')
                                        <span class="badge bg-primary">بطاقة</span>
                                        @break
                                    @case('bank_transfer')
                                        <span class="badge bg-info">تحويل بنكي</span>
                                        @break
                                    @case('credit')
                                        <span class="badge bg-warning">آجل</span>
                                        @break
                                @endswitch
                            </td>
                            <td>
                                @if($payment['status'] === 'completed')
                                    <span class="badge bg-success">مكتمل</span>
                                @elseif($payment['status'] === 'pending')
                                    <span class="badge bg-warning">معلق</span>
                                @else
                                    <span class="badge bg-danger">ملغي</span>
                                @endif
                            </td>
                            <td>{{ $payment['notes'] }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-primary" title="طباعة إيصال">
                                        <i class="fas fa-print"></i>
                                    </button>
                                    @if($payment['status'] === 'pending')
                                        <button class="btn btn-outline-success" title="تأكيد">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pending Invoices -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        فواتير معلقة الدفع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>تاريخ الفاتورة</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>إجمالي الفاتورة</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $pendingInvoices = [
                                        [
                                            'number' => 'SAL-20250625-0008',
                                            'customer' => 'شركة المقاولات الكبرى',
                                            'date' => '2025/06/25',
                                            'due_date' => '2025/07/25',
                                            'total' => 15000.00,
                                            'paid' => 10000.00,
                                            'remaining' => 5000.00
                                        ],
                                        [
                                            'number' => 'SAL-20250620-0003',
                                            'customer' => 'محمد عبد الله',
                                            'date' => '2025/06/20',
                                            'due_date' => '2025/07/20',
                                            'total' => 3500.00,
                                            'paid' => 0.00,
                                            'remaining' => 3500.00
                                        ],
                                    ];
                                @endphp
                                
                                @foreach($pendingInvoices as $invoice)
                                <tr>
                                    <td>
                                        <a href="#" class="text-decoration-none">
                                            {{ $invoice['number'] }}
                                        </a>
                                    </td>
                                    <td>{{ $invoice['customer'] }}</td>
                                    <td>{{ $invoice['date'] }}</td>
                                    <td>
                                        <span class="text-{{ \Carbon\Carbon::parse($invoice['due_date'])->isPast() ? 'danger' : 'muted' }}">
                                            {{ $invoice['due_date'] }}
                                        </span>
                                    </td>
                                    <td>{{ number_format($invoice['total'], 2) }} ج.م</td>
                                    <td>{{ number_format($invoice['paid'], 2) }} ج.م</td>
                                    <td>
                                        <strong class="text-danger">{{ number_format($invoice['remaining'], 2) }} ج.م</strong>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="addPayment('{{ $invoice['number'] }}', {{ $invoice['remaining'] }})">
                                            <i class="fas fa-plus me-1"></i>
                                            إضافة دفعة
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Payment Modal -->
<div class="modal fade" id="addPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دفعة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="paymentForm">
                    <div class="mb-3">
                        <label class="form-label">رقم الفاتورة</label>
                        <input type="text" class="form-control" id="invoiceNumber" placeholder="SAL-20250630-0001">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المبلغ</label>
                        <input type="number" step="0.01" class="form-control" id="paymentAmount" placeholder="0.00">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="paymentMethod">
                            <option value="cash">نقدي</option>
                            <option value="card">بطاقة</option>
                            <option value="bank_transfer">تحويل بنكي</option>
                            <option value="credit">آجل</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">تاريخ الدفع</label>
                        <input type="date" class="form-control" id="paymentDate" value="{{ date('Y-m-d') }}">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="paymentNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="savePayment()">حفظ الدفعة</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function addPayment(invoiceNumber, amount) {
    document.getElementById('invoiceNumber').value = invoiceNumber;
    document.getElementById('paymentAmount').value = amount;
    
    const modal = new bootstrap.Modal(document.getElementById('addPaymentModal'));
    modal.show();
}

function savePayment() {
    // هنا يمكن إضافة كود حفظ الدفعة
    alert('تم حفظ الدفعة بنجاح!');
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('addPaymentModal'));
    modal.hide();
    
    // إعادة تحميل الصفحة أو تحديث البيانات
    location.reload();
}
</script>
@endpush
@endsection
