<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('supplier_code', 20)->unique(); // رقم المورد
            $table->string('name'); // اسم المورد
            $table->string('company_name')->nullable(); // اسم الشركة
            $table->enum('supplier_type', ['individual', 'company'])->default('company'); // نوع المورد
            $table->string('tax_number')->nullable(); // الرقم الضريبي
            $table->string('commercial_register')->nullable(); // السجل التجاري
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('fax')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('EG');
            $table->integer('payment_terms')->default(30); // شروط الدفع (بالأيام)
            $table->decimal('opening_balance', 15, 2)->default(0); // الرصيد الافتتاحي
            $table->decimal('current_balance', 15, 2)->default(0); // الرصيد الحالي
            $table->enum('balance_type', ['debit', 'credit'])->default('credit'); // نوع الرصيد
            $table->string('contact_person')->nullable(); // الشخص المسؤول
            $table->string('contact_person_phone')->nullable();
            $table->string('bank_name')->nullable(); // اسم البنك
            $table->string('bank_account')->nullable(); // رقم الحساب البنكي
            $table->string('iban')->nullable(); // رقم الآيبان
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->json('additional_info')->nullable(); // معلومات إضافية
            $table->timestamps();

            $table->index(['tenant_id', 'supplier_code']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['tenant_id', 'supplier_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suppliers');
    }
};
