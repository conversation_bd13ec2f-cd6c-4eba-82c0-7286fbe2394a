<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Traits\BelongsToTenant;

class Supplier extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'supplier_code',
        'name',
        'company_name',
        'supplier_type',
        'tax_number',
        'commercial_register',
        'email',
        'phone',
        'mobile',
        'fax',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'payment_terms',
        'opening_balance',
        'current_balance',
        'balance_type',
        'contact_person',
        'contact_person_phone',
        'bank_name',
        'bank_account',
        'iban',
        'is_active',
        'notes',
        'additional_info',
    ];

    protected $casts = [
        'payment_terms' => 'integer',
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'additional_info' => 'array',
    ];

    /**
     * أنواع الموردين
     */
    const SUPPLIER_TYPES = [
        'individual' => 'فرد',
        'company' => 'شركة',
    ];

    /**
     * أنواع الأرصدة
     */
    const BALANCE_TYPES = [
        'debit' => 'مدين',
        'credit' => 'دائن',
    ];

    /**
     * البلدان المدعومة
     */
    const COUNTRIES = [
        'EG' => 'مصر',
        'SA' => 'السعودية',
        'AE' => 'الإمارات',
        'KW' => 'الكويت',
        'QA' => 'قطر',
        'BH' => 'البحرين',
        'OM' => 'عمان',
        'JO' => 'الأردن',
        'LB' => 'لبنان',
        'SY' => 'سوريا',
        'CN' => 'الصين',
        'TR' => 'تركيا',
        'IT' => 'إيطاليا',
        'ES' => 'إسبانيا',
    ];

    /**
     * العلاقة مع فواتير الشراء
     */
    public function purchaseInvoices(): HasMany
    {
        return $this->hasMany(PurchaseInvoice::class);
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * العلاقة مع أوامر الشراء
     */
    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    /**
     * العلاقة مع المنتجات
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'main_supplier_id');
    }

    /**
     * Scope للموردين النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للموردين حسب النوع
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('supplier_type', $type);
    }

    /**
     * Scope للبحث في الموردين
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('supplier_code', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%")
              ->orWhere('mobile', 'like', "%{$search}%");
        });
    }

    /**
     * Scope للموردين المستحقين للدفع
     */
    public function scopePayable($query)
    {
        return $query->where('current_balance', '>', 0)
                    ->where('balance_type', 'credit');
    }

    /**
     * الحصول على اسم نوع المورد
     */
    public function getSupplierTypeNameAttribute(): string
    {
        return self::SUPPLIER_TYPES[$this->supplier_type] ?? $this->supplier_type;
    }

    /**
     * الحصول على اسم نوع الرصيد
     */
    public function getBalanceTypeNameAttribute(): string
    {
        return self::BALANCE_TYPES[$this->balance_type] ?? $this->balance_type;
    }

    /**
     * الحصول على اسم البلد
     */
    public function getCountryNameAttribute(): string
    {
        return self::COUNTRIES[$this->country] ?? $this->country;
    }

    /**
     * الحصول على الاسم الكامل
     */
    public function getFullNameAttribute(): string
    {
        if ($this->supplier_type === 'company' && $this->company_name) {
            return $this->company_name . ' (' . $this->name . ')';
        }

        return $this->name;
    }

    /**
     * تحديث الرصيد
     */
    public function updateBalance(float $amount, string $type = 'credit'): void
    {
        if ($type === 'credit') {
            if ($this->balance_type === 'credit') {
                $this->current_balance += $amount;
            } else {
                $this->current_balance -= $amount;
                if ($this->current_balance < 0) {
                    $this->current_balance = abs($this->current_balance);
                    $this->balance_type = 'credit';
                }
            }
        } else { // debit
            if ($this->balance_type === 'debit') {
                $this->current_balance += $amount;
            } else {
                $this->current_balance -= $amount;
                if ($this->current_balance < 0) {
                    $this->current_balance = abs($this->current_balance);
                    $this->balance_type = 'debit';
                }
            }
        }

        $this->save();
    }

    /**
     * الحصول على إجمالي المشتريات
     */
    public function getTotalPurchasesAttribute(): float
    {
        return $this->purchaseInvoices()->sum('total_amount') ?? 0;
    }

    /**
     * الحصول على إجمالي المدفوعات
     */
    public function getTotalPaymentsAttribute(): float
    {
        return $this->payments()->sum('amount') ?? 0;
    }

    /**
     * الحصول على آخر فاتورة شراء
     */
    public function getLastPurchaseInvoiceAttribute()
    {
        return $this->purchaseInvoices()->latest()->first();
    }

    /**
     * الحصول على آخر دفعة
     */
    public function getLastPaymentAttribute()
    {
        return $this->payments()->latest()->first();
    }

    /**
     * الحصول على عدد المنتجات المرتبطة
     */
    public function getProductsCountAttribute(): int
    {
        return $this->products()->count();
    }
}
