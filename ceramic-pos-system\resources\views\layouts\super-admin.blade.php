<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Super Admin') - Ceramic POS</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .sidebar-heading {
            color: rgba(255,255,255,0.6);
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .main-content {
            background-color: #ffffff;
            min-height: 100vh;
            border-radius: 15px 0 0 0;
            box-shadow: -2px 0 10px rgba(0,0,0,0.05);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 500;
        }
        
        .badge {
            font-weight: 500;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .super-admin-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
        }
    </style>

    @stack('styles')
</head>

<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ route('super-admin.dashboard') }}">
                <i class="fas fa-crown me-2"></i>
                Ceramic POS - Super Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        {{ auth()->user()->name }}
                        <span class="super-admin-badge ms-2">
                            <i class="fas fa-shield-alt me-1"></i>
                            Super Admin
                        </span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('super-admin.settings') }}">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <nav class="nav flex-column py-3">
                        <a class="nav-link {{ request()->routeIs('super-admin.dashboard') ? 'active' : '' }}"
                           href="{{ route('super-admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>

                        <hr class="my-2 mx-3" style="border-color: rgba(255,255,255,0.2);">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1">
                            إدارة المشتركين
                        </h6>
                        <a class="nav-link {{ request()->routeIs('super-admin.tenants*') ? 'active' : '' }}"
                           href="{{ route('super-admin.tenants') }}">
                            <i class="fas fa-building me-2"></i>
                            جميع المشتركين
                        </a>
                        <a class="nav-link {{ request()->routeIs('super-admin.tenants.create') ? 'active' : '' }}"
                           href="{{ route('super-admin.tenants.create') }}">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مشترك
                        </a>

                        <hr class="my-2 mx-3" style="border-color: rgba(255,255,255,0.2);">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1">
                            الخطط والأسعار
                        </h6>
                        <a class="nav-link {{ request()->routeIs('super-admin.plans') ? 'active' : '' }}"
                           href="{{ route('super-admin.plans') }}">
                            <i class="fas fa-tags me-2"></i>
                            إدارة الخطط
                        </a>

                        <hr class="my-2 mx-3" style="border-color: rgba(255,255,255,0.2);">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1">
                            التقارير والإحصائيات
                        </h6>
                        <a class="nav-link {{ request()->routeIs('super-admin.financial-reports') ? 'active' : '' }}"
                           href="{{ route('super-admin.financial-reports') }}">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير المالية
                        </a>

                        <hr class="my-2 mx-3" style="border-color: rgba(255,255,255,0.2);">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1">
                            إعدادات النظام
                        </h6>
                        <a class="nav-link {{ request()->routeIs('super-admin.settings') ? 'active' : '' }}"
                           href="{{ route('super-admin.settings') }}">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات العامة
                        </a>

                        <hr class="my-2 mx-3" style="border-color: rgba(255,255,255,0.2);">

                        <h6 class="sidebar-heading px-3 mt-3 mb-1">
                            روابط سريعة
                        </h6>
                        <a class="nav-link" href="{{ route('home') }}" target="_blank">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                        <a class="nav-link" href="{{ route('modules.status') }}" target="_blank">
                            <i class="fas fa-tasks me-2"></i>
                            حالة النظام
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 px-0">
                <div class="main-content p-4">
                    <!-- Success Messages -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Error Messages -->
                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Validation Errors -->
                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>يرجى تصحيح الأخطاء التالية:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    @stack('scripts')
</body>
</html>
