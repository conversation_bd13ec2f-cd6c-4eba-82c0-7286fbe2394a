@extends('layouts.app')

@section('title', 'نقطة البيع')

@push('styles')
<style>
    .pos-container {
        height: calc(100vh - 120px);
    }
    
    .product-grid {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .product-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    
    .product-card:hover {
        border-color: #007bff;
        transform: translateY(-2px);
    }
    
    .cart-items {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .total-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
    }
    
    .keypad {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }
    
    .keypad button {
        height: 50px;
        font-size: 18px;
        font-weight: bold;
    }
</style>
@endpush

@section('content')
<div class="container-fluid pos-container">
    <div class="row h-100">
        <!-- Products Section -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas fa-boxes me-2"></i>
                                المنتجات
                            </h5>
                        </div>
                        <div class="col-auto">
                            <div class="input-group">
                                <input type="text" id="product-search" class="form-control" 
                                       placeholder="البحث بالاسم، الكود، أو الباركود">
                                <button class="btn btn-outline-primary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="product-grid">
                        <div class="row" id="products-container">
                            @foreach($products as $product)
                            <div class="col-md-3 col-sm-4 col-6 mb-3">
                                <div class="card product-card h-100" onclick="addToCart({{ $product->id }})">
                                    <div class="card-body text-center p-2">
                                        @if($product->image)
                                            <img src="{{ asset('storage/' . $product->image) }}" 
                                                 alt="{{ $product->name }}" 
                                                 class="img-fluid mb-2" 
                                                 style="height: 60px; object-fit: cover;">
                                        @else
                                            <i class="fas fa-cube fa-2x text-muted mb-2"></i>
                                        @endif
                                        <h6 class="card-title mb-1" style="font-size: 0.9rem;">{{ Str::limit($product->name, 20) }}</h6>
                                        <p class="card-text mb-1">
                                            <small class="text-muted">{{ $product->code }}</small>
                                        </p>
                                        <div class="fw-bold text-primary">{{ number_format($product->selling_price, 2) }} ج.م</div>
                                        <small class="text-muted">متوفر: {{ $product->current_stock }} {{ $product->unit }}</small>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Cart Section -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        سلة المشتريات
                    </h5>
                </div>
                <div class="card-body d-flex flex-column">
                    <!-- Customer Selection -->
                    <div class="mb-3">
                        <label class="form-label">العميل</label>
                        <select class="form-select" id="customer-select">
                            <option value="">عميل نقدي</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <!-- Cart Items -->
                    <div class="cart-items flex-grow-1 mb-3">
                        <div id="cart-items">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                <p>السلة فارغة</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Totals -->
                    <div class="total-section mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span id="subtotal">0.00 ج.م</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الخصم:</span>
                            <span id="discount-amount">0.00 ج.م</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الضريبة:</span>
                            <span id="tax-amount">0.00 ج.م</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>الإجمالي:</strong>
                            <strong id="total">0.00 ج.م</strong>
                        </div>
                    </div>
                    
                    <!-- Payment -->
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="payment-method">
                            <option value="cash">نقدي</option>
                            <option value="card">بطاقة</option>
                            <option value="credit">آجل</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المبلغ المدفوع</label>
                        <input type="number" class="form-control" id="paid-amount" step="0.01" min="0">
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-success btn-lg" onclick="checkout()" id="checkout-btn" disabled>
                            <i class="fas fa-check me-2"></i>
                            إتمام البيع
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearCart()">
                            <i class="fas fa-trash me-2"></i>
                            مسح السلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Amount Modal -->
<div class="modal fade" id="quickAmountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدخال سريع للمبلغ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="keypad mb-3">
                    <button class="btn btn-outline-primary" onclick="addToAmount('1')">1</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('2')">2</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('3')">3</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('4')">4</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('5')">5</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('6')">6</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('7')">7</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('8')">8</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('9')">9</button>
                    <button class="btn btn-outline-secondary" onclick="addToAmount('.')">.</button>
                    <button class="btn btn-outline-primary" onclick="addToAmount('0')">0</button>
                    <button class="btn btn-outline-danger" onclick="clearAmount()">مسح</button>
                </div>
                <input type="text" class="form-control text-center" id="keypad-amount" readonly>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="setAmount()">تأكيد</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let cart = [];
let products = @json($products);

// إضافة منتج للسلة
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    const existingItem = cart.find(item => item.product_id === productId);
    
    if (existingItem) {
        if (existingItem.quantity < product.current_stock) {
            existingItem.quantity++;
        } else {
            alert('المخزون غير كافي');
            return;
        }
    } else {
        cart.push({
            product_id: productId,
            name: product.name,
            price: product.selling_price,
            quantity: 1,
            max_stock: product.current_stock
        });
    }
    
    updateCartDisplay();
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartContainer = document.getElementById('cart-items');
    
    if (cart.length === 0) {
        cartContainer.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        document.getElementById('checkout-btn').disabled = true;
    } else {
        let html = '';
        cart.forEach((item, index) => {
            html += `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                    <div class="flex-grow-1">
                        <div class="fw-bold">${item.name}</div>
                        <small class="text-muted">${item.price} ج.م × ${item.quantity}</small>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${index}, -1)">-</button>
                        <span class="fw-bold">${item.quantity}</span>
                        <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${index}, 1)">+</button>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });
        cartContainer.innerHTML = html;
        document.getElementById('checkout-btn').disabled = false;
    }
    
    updateTotals();
}

// تحديث الكمية
function updateQuantity(index, change) {
    const item = cart[index];
    const newQuantity = item.quantity + change;
    
    if (newQuantity <= 0) {
        removeFromCart(index);
    } else if (newQuantity <= item.max_stock) {
        item.quantity = newQuantity;
        updateCartDisplay();
    } else {
        alert('المخزون غير كافي');
    }
}

// حذف من السلة
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
}

// مسح السلة
function clearCart() {
    cart = [];
    updateCartDisplay();
}

// تحديث الإجماليات
function updateTotals() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discount = 0; // يمكن إضافة خصم لاحقاً
    const tax = subtotal * 0.14; // ضريبة 14%
    const total = subtotal - discount + tax;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ج.م';
    document.getElementById('discount-amount').textContent = discount.toFixed(2) + ' ج.م';
    document.getElementById('tax-amount').textContent = tax.toFixed(2) + ' ج.م';
    document.getElementById('total').textContent = total.toFixed(2) + ' ج.م';
    
    // تحديث المبلغ المدفوع تلقائياً
    document.getElementById('paid-amount').value = total.toFixed(2);
}

// إتمام البيع
function checkout() {
    if (cart.length === 0) {
        alert('السلة فارغة');
        return;
    }
    
    const data = {
        customer_id: document.getElementById('customer-select').value || null,
        items: cart,
        payment_method: document.getElementById('payment-method').value,
        paid_amount: parseFloat(document.getElementById('paid-amount').value) || 0,
        tax_rate: 14
    };
    
    fetch('/pos/checkout?tenant={{ $tenant->id }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إتمام البيع بنجاح!\nالباقي: ' + data.change.toFixed(2) + ' ج.م');
            clearCart();
            // يمكن إضافة طباعة الفاتورة هنا
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إتمام البيع');
    });
}

// البحث في المنتجات
document.getElementById('product-search').addEventListener('input', function() {
    const search = this.value.toLowerCase();
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        const text = card.textContent.toLowerCase();
        if (text.includes(search)) {
            card.closest('.col-md-3').style.display = 'block';
        } else {
            card.closest('.col-md-3').style.display = 'none';
        }
    });
});
</script>
@endpush
@endsection
