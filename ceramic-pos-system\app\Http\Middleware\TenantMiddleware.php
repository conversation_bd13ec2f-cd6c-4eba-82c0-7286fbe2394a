<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SimpleTenant;

class TenantMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $tenantId = $request->get('tenant');

        if (!$tenantId) {
            return redirect()->route('tenant.select')->with('error', 'يجب اختيار مشترك أولاً');
        }

        $tenant = SimpleTenant::find($tenantId);

        if (!$tenant) {
            return redirect()->route('tenant.select')->with('error', 'المشترك غير موجود');
        }

        if (!$tenant->isActive()) {
            return redirect()->route('tenant.select')->with('error', 'المشترك غير نشط');
        }

        // Share tenant data with all views
        view()->share('tenant', $tenant);
        view()->share('tenant_id', $tenantId);

        return $next($request);
    }
}
