<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * صفحة الإعدادات الرئيسية
     */
    public function index()
    {
        $settings = Setting::getAllGrouped();

        return view('tenant.settings.index', compact('settings'));
    }

    /**
     * إعدادات الشركة
     */
    public function company()
    {
        $settings = Setting::getGroup('company');

        return view('tenant.settings.company', compact('settings'));
    }

    /**
     * إعدادات النظام
     */
    public function system()
    {
        $settings = Setting::getGroup('system');

        return view('tenant.settings.system', compact('settings'));
    }

    /**
     * إعدادات الأمان
     */
    public function security()
    {
        $settings = Setting::getGroup('security');

        return view('tenant.settings.security', compact('settings'));
    }

    /**
     * إعدادات الفوترة
     */
    public function invoicing()
    {
        $settings = Setting::getGroup('invoicing');

        return view('tenant.settings.invoicing', compact('settings'));
    }

    /**
     * إعدادات المخزون
     */
    public function inventory()
    {
        $settings = Setting::getGroup('inventory');

        return view('tenant.settings.inventory', compact('settings'));
    }

    /**
     * إعدادات الواجهة
     */
    public function ui()
    {
        $settings = Setting::getGroup('ui');

        return view('tenant.settings.ui', compact('settings'));
    }

    /**
     * إعدادات الطباعة
     */
    public function printing()
    {
        $settings = Setting::getGroup('printing');

        return view('tenant.settings.printing', compact('settings'));
    }

    /**
     * تحديث الإعدادات
     */
    public function update(Request $request)
    {
        $group = $request->get('group', 'general');
        $settings = $request->get('settings', []);

        foreach ($settings as $key => $value) {
            // معالجة رفع الملفات
            if ($request->hasFile("settings.{$key}")) {
                $file = $request->file("settings.{$key}");
                $path = $file->store('settings', 'public');
                $value = $path;
            }

            // تحديد نوع البيانات
            $type = $this->determineValueType($value);

            Setting::set($key, $value, [
                'group' => $group,
                'type' => $type,
            ]);
        }

        // مسح الكاش
        Setting::clearCache();

        return redirect()->back()
            ->with('success', 'تم تحديث الإعدادات بنجاح');
    }

    /**
     * تحديث إعداد واحد عبر AJAX
     */
    public function updateSetting(Request $request)
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required',
            'group' => 'string',
        ]);

        $value = $request->value;
        $type = $this->determineValueType($value);

        $setting = Setting::set(
            $request->key,
            $value,
            [
                'group' => $request->get('group', 'general'),
                'type' => $type,
            ]
        );

        Setting::clearCache();

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث الإعداد بنجاح',
            'setting' => $setting,
        ]);
    }

    /**
     * رفع شعار الشركة
     */
    public function uploadLogo(Request $request)
    {
        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // حذف الشعار القديم
        $oldLogo = Setting::get('company_logo');
        if ($oldLogo && Storage::disk('public')->exists($oldLogo)) {
            Storage::disk('public')->delete($oldLogo);
        }

        // رفع الشعار الجديد
        $path = $request->file('logo')->store('logos', 'public');

        Setting::set('company_logo', $path, [
            'group' => 'company',
            'type' => 'file',
        ]);

        Setting::clearCache();

        return response()->json([
            'success' => true,
            'message' => 'تم رفع الشعار بنجاح',
            'logo_url' => Storage::url($path),
        ]);
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    public function reset(Request $request)
    {
        $group = $request->get('group');

        if ($group) {
            // إعادة تعيين مجموعة محددة
            Setting::where('group', $group)->delete();
            Setting::createDefaultSettings(tenant('id'));
        } else {
            // إعادة تعيين جميع الإعدادات
            Setting::truncate();
            Setting::createDefaultSettings(tenant('id'));
        }

        Setting::clearCache();

        return redirect()->back()
            ->with('success', 'تم إعادة تعيين الإعدادات للقيم الافتراضية');
    }

    /**
     * تصدير الإعدادات
     */
    public function export()
    {
        $settings = Setting::getAllGrouped();

        $filename = 'settings_' . tenant('id') . '_' . date('Y-m-d_H-i-s') . '.json';

        return response()->json($settings)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * استيراد الإعدادات
     */
    public function import(Request $request)
    {
        $request->validate([
            'settings_file' => 'required|file|mimes:json',
        ]);

        $content = file_get_contents($request->file('settings_file')->path());
        $settings = json_decode($content, true);

        if (!$settings) {
            return redirect()->back()
                ->with('error', 'ملف الإعدادات غير صالح');
        }

        foreach ($settings as $group => $groupSettings) {
            foreach ($groupSettings as $key => $value) {
                Setting::set($key, $value, ['group' => $group]);
            }
        }

        Setting::clearCache();

        return redirect()->back()
            ->with('success', 'تم استيراد الإعدادات بنجاح');
    }

    /**
     * تحديد نوع القيمة
     */
    private function determineValueType($value): string
    {
        if (is_bool($value) || $value === 'true' || $value === 'false') {
            return 'boolean';
        }

        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? 'decimal' : 'integer';
        }

        if (is_array($value)) {
            return 'json';
        }

        if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
            return 'email';
        }

        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return 'url';
        }

        if (strlen($value) > 255) {
            return 'text';
        }

        return 'string';
    }

    /**
     * اختبار الاتصال بقاعدة البيانات
     */
    public function testDatabaseConnection()
    {
        try {
            \DB::connection()->getPdo();
            return response()->json([
                'success' => true,
                'message' => 'الاتصال بقاعدة البيانات ناجح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * اختبار إرسال البريد الإلكتروني
     */
    public function testEmailConnection(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        try {
            // إرسال بريد تجريبي
            \Mail::raw('هذا بريد تجريبي لاختبار إعدادات البريد الإلكتروني', function($message) use ($request) {
                $message->to($request->test_email)
                        ->subject('اختبار إعدادات البريد الإلكتروني');
            });

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال البريد التجريبي بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل إرسال البريد الإلكتروني: ' . $e->getMessage(),
            ], 500);
        }
    }
}
