@extends('layouts.app')

@section('title', 'تقرير المبيعات')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-chart-line me-2 text-success"></i>
                تقرير المبيعات
            </h2>
            <p class="text-muted mb-0">{{ $tenant->company_name }}</p>
        </div>
        <div>
            <a href="{{ route('reports.index', ['tenant' => $tenant->id]) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للتقارير
            </a>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <input type="hidden" name="tenant" value="{{ $tenant->id }}">
                
                <div class="col-md-4">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" 
                           value="{{ request('date_from', $dateFrom->format('Y-m-d')) }}">
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" 
                           value="{{ request('date_to', $dateTo->format('Y-m-d')) }}">
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            تطبيق الفلتر
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Sales Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                    <h3 class="text-primary">{{ $salesStats['total_invoices'] }}</h3>
                    <p class="text-muted mb-0">إجمالي الفواتير</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                    <h3 class="text-success">{{ number_format($salesStats['total_amount'], 2) }}</h3>
                    <p class="text-muted mb-0">إجمالي المبيعات (ج.م)</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-calculator fa-2x"></i>
                    </div>
                    <h3 class="text-info">{{ number_format($salesStats['average_invoice'] ?? 0, 2) }}</h3>
                    <p class="text-muted mb-0">متوسط الفاتورة (ج.م)</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-credit-card fa-2x"></i>
                    </div>
                    <h3 class="text-warning">{{ number_format($salesStats['paid_amount'], 2) }}</h3>
                    <p class="text-muted mb-0">المبلغ المحصل (ج.م)</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Daily Sales Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>
                        المبيعات اليومية
                    </h5>
                </div>
                <div class="card-body">
                    @if($dailySales->count() > 0)
                        <canvas id="dailySalesChart" height="100"></canvas>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد بيانات مبيعات للفترة المحددة</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-crown me-2"></i>
                        أفضل العملاء
                    </h5>
                </div>
                <div class="card-body">
                    @if($topCustomers->count() > 0)
                        @foreach($topCustomers as $customer)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>
                                    @if($customer->customer)
                                        {{ $customer->customer->name }}
                                    @else
                                        عميل نقدي
                                    @endif
                                </strong>
                                <br>
                                <small class="text-muted">{{ $customer->count() }} فاتورة</small>
                            </div>
                            <div class="text-end">
                                <span class="fw-bold text-success">{{ number_format($customer->total, 2) }} ج.م</span>
                            </div>
                        </div>
                        @if(!$loop->last)
                            <hr>
                        @endif
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد بيانات عملاء</h6>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Summary Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        ملخص المبيعات اليومية
                    </h5>
                </div>
                <div class="card-body">
                    @if($dailySales->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المبيعات (ج.م)</th>
                                        <th>النسبة من الإجمالي</th>
                                        <th>الرسم البياني</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($dailySales as $sale)
                                    @php
                                        $percentage = $salesStats['total_amount'] > 0 ? ($sale->total / $salesStats['total_amount']) * 100 : 0;
                                    @endphp
                                    <tr>
                                        <td>{{ \Carbon\Carbon::parse($sale->date)->format('Y/m/d') }}</td>
                                        <td>{{ number_format($sale->total, 2) }}</td>
                                        <td>{{ number_format($percentage, 1) }}%</td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" 
                                                     style="width: {{ $percentage }}%"
                                                     aria-valuenow="{{ $percentage }}" 
                                                     aria-valuemin="0" 
                                                     aria-valuemax="100">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th>الإجمالي</th>
                                        <th>{{ number_format($salesStats['total_amount'], 2) }} ج.م</th>
                                        <th>100%</th>
                                        <th>-</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد بيانات مبيعات للفترة المحددة</h6>
                            <p class="text-muted">جرب تغيير الفترة الزمنية أو تأكد من وجود فواتير مبيعات</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
@if($dailySales->count() > 0)
// Daily Sales Chart
const ctx = document.getElementById('dailySalesChart').getContext('2d');
const dailySalesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {!! json_encode($dailySales->pluck('date')->map(function($date) { return \Carbon\Carbon::parse($date)->format('m/d'); })) !!},
        datasets: [{
            label: 'المبيعات اليومية (ج.م)',
            data: {!! json_encode($dailySales->pluck('total')) !!},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'المبيعات اليومية'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ج.م';
                    }
                }
            }
        }
    }
});
@endif
</script>
@endpush
@endsection
