<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Traits\BelongsToTenant;

class Tax extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'tax_code',
        'name',
        'name_en',
        'type',
        'rate',
        'amount',
        'is_inclusive',
        'is_default',
        'is_active',
        'description',
    ];

    protected $casts = [
        'rate' => 'decimal:2',
        'amount' => 'decimal:2',
        'is_inclusive' => 'boolean',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * أنواع الضرائب
     */
    const TYPES = [
        'percentage' => 'نسبة مئوية',
        'fixed' => 'مبلغ ثابت',
    ];

    /**
     * Scope للضرائب النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للضرائب الافتراضية
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * الحصول على اسم نوع الضريبة
     */
    public function getTypeNameAttribute(): string
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * حساب مبلغ الضريبة
     */
    public function calculateTax(float $amount): float
    {
        if ($this->type === 'percentage') {
            return $amount * ($this->rate / 100);
        }

        return $this->amount ?? 0;
    }
}
