<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Traits\BelongsToTenant;

class Product extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'product_code',
        'barcode',
        'name',
        'name_en',
        'description',
        'product_type',
        'category',
        'brand',
        'model',
        'size',
        'color',
        'material',
        'origin_country',
        'unit',
        'purchase_price',
        'selling_price',
        'wholesale_price',
        'minimum_price',
        'weight',
        'dimensions',
        'minimum_stock',
        'maximum_stock',
        'reorder_level',
        'track_quantity',
        'is_active',
        'image',
        'images',
        'specifications',
        'notes',
        'main_supplier_id',
    ];

    protected $casts = [
        'purchase_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'wholesale_price' => 'decimal:2',
        'minimum_price' => 'decimal:2',
        'weight' => 'decimal:3',
        'minimum_stock' => 'integer',
        'maximum_stock' => 'integer',
        'reorder_level' => 'integer',
        'track_quantity' => 'boolean',
        'is_active' => 'boolean',
        'images' => 'array',
        'specifications' => 'array',
    ];

    /**
     * العلاقة مع المورد الرئيسي
     */
    public function mainSupplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'main_supplier_id');
    }

    /**
     * العلاقة مع مخزون المنتج
     */
    public function stock(): HasMany
    {
        return $this->hasMany(ProductStock::class);
    }

    /**
     * العلاقة مع حركات المخزون
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * العلاقة مع بنود الفواتير
     */
    public function invoiceItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Scope للمنتجات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للمنتجات منخفضة المخزون
     */
    public function scopeLowStock($query)
    {
        return $query->whereHas('stock', function($stockQuery) {
            $stockQuery->whereRaw('quantity <= minimum_stock');
        });
    }

    /**
     * Scope للمنتجات نافدة المخزون
     */
    public function scopeOutOfStock($query)
    {
        return $query->whereHas('stock', function($stockQuery) {
            $stockQuery->where('quantity', '<=', 0);
        });
    }

    /**
     * Scope للمنتجات حسب النوع
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('product_type', $type);
    }

    /**
     * Scope للبحث في المنتجات
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('product_code', 'like', "%{$search}%")
              ->orWhere('barcode', 'like', "%{$search}%")
              ->orWhere('name_en', 'like', "%{$search}%")
              ->orWhere('brand', 'like', "%{$search}%")
              ->orWhere('model', 'like', "%{$search}%");
        });
    }



    /**
     * Scope للمنتجات حسب الفئة
     */
    public function scopeOfCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope للمنتجات حسب العلامة التجارية
     */
    public function scopeOfBrand($query, $brand)
    {
        return $query->where('brand', $brand);
    }

    /**
     * أنواع المنتجات
     */
    const PRODUCT_TYPES = [
        'ceramic' => 'سيراميك',
        'sanitary' => 'أدوات صحية',
        'accessory' => 'إكسسوارات',
        'other' => 'أخرى',
    ];

    /**
     * وحدات القياس
     */
    const UNITS = [
        'piece' => 'قطعة',
        'box' => 'علبة',
        'pack' => 'حزمة',
        'meter' => 'متر',
        'square_meter' => 'متر مربع',
        'kilogram' => 'كيلوجرام',
        'liter' => 'لتر',
        'set' => 'طقم',
    ];

    /**
     * الحصول على اسم نوع المنتج
     */
    public function getProductTypeNameAttribute(): string
    {
        return self::PRODUCT_TYPES[$this->product_type] ?? $this->product_type;
    }

    /**
     * الحصول على اسم وحدة القياس
     */
    public function getUnitNameAttribute(): string
    {
        return self::UNITS[$this->unit] ?? $this->unit;
    }

    /**
     * الحصول على إجمالي الكمية في جميع المخازن
     */
    public function getTotalQuantityAttribute(): int
    {
        return $this->stock()->sum('quantity') ?? 0;
    }

    /**
     * الحصول على إجمالي الكمية المتاحة
     */
    public function getAvailableQuantityAttribute(): int
    {
        return $this->stock()->sum('available_quantity') ?? 0;
    }

    /**
     * الحصول على إجمالي الكمية المحجوزة
     */
    public function getReservedQuantityAttribute(): int
    {
        return $this->stock()->sum('reserved_quantity') ?? 0;
    }

    /**
     * الحصول على متوسط التكلفة
     */
    public function getAverageCostAttribute(): float
    {
        $totalValue = 0;
        $totalQuantity = 0;

        foreach ($this->stock as $stock) {
            $totalValue += $stock->quantity * $stock->average_cost;
            $totalQuantity += $stock->quantity;
        }

        return $totalQuantity > 0 ? $totalValue / $totalQuantity : 0;
    }

    /**
     * التحقق من كون المنتج منخفض المخزون
     */
    public function isLowStock(): bool
    {
        return $this->total_quantity <= $this->minimum_stock;
    }

    /**
     * التحقق من كون المنتج نفد من المخزون
     */
    public function isOutOfStock(): bool
    {
        return $this->total_quantity <= 0;
    }

    /**
     * التحقق من الحاجة لإعادة الطلب
     */
    public function needsReorder(): bool
    {
        return $this->reorder_level && $this->total_quantity <= $this->reorder_level;
    }

    /**
     * الحصول على حالة المخزون
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->isOutOfStock()) {
            return 'out_of_stock';
        } elseif ($this->isLowStock()) {
            return 'low_stock';
        } elseif ($this->needsReorder()) {
            return 'needs_reorder';
        } else {
            return 'in_stock';
        }
    }

    /**
     * الحصول على اسم حالة المخزون
     */
    public function getStockStatusNameAttribute(): string
    {
        $statuses = [
            'out_of_stock' => 'نفد من المخزون',
            'low_stock' => 'مخزون منخفض',
            'needs_reorder' => 'يحتاج إعادة طلب',
            'in_stock' => 'متوفر',
        ];

        return $statuses[$this->stock_status] ?? 'غير محدد';
    }

    /**
     * الحصول على لون حالة المخزون
     */
    public function getStockStatusColorAttribute(): string
    {
        $colors = [
            'out_of_stock' => 'danger',
            'low_stock' => 'warning',
            'needs_reorder' => 'info',
            'in_stock' => 'success',
        ];

        return $colors[$this->stock_status] ?? 'secondary';
    }

    /**
     * الحصول على الكمية في مخزن محدد
     */
    public function getQuantityInWarehouse(int $warehouseId): int
    {
        $stock = $this->stock()->where('warehouse_id', $warehouseId)->first();
        return $stock ? $stock->quantity : 0;
    }

    /**
     * الحصول على الكمية المتاحة في مخزن محدد
     */
    public function getAvailableQuantityInWarehouse(int $warehouseId): int
    {
        $stock = $this->stock()->where('warehouse_id', $warehouseId)->first();
        return $stock ? $stock->available_quantity : 0;
    }

    /**
     * تحديث المخزون في مخزن محدد
     */
    public function updateStock(int $warehouseId, int $quantity, string $type = 'add'): void
    {
        $stock = $this->stock()->firstOrCreate(
            ['warehouse_id' => $warehouseId],
            ['quantity' => 0, 'reserved_quantity' => 0, 'available_quantity' => 0, 'average_cost' => 0]
        );

        if ($type === 'add') {
            $stock->quantity += $quantity;
            $stock->available_quantity += $quantity;
        } elseif ($type === 'subtract') {
            $stock->quantity -= $quantity;
            $stock->available_quantity -= $quantity;
        } elseif ($type === 'set') {
            $stock->quantity = $quantity;
            $stock->available_quantity = $quantity - $stock->reserved_quantity;
        }

        // التأكد من عدم وجود كميات سالبة
        $stock->quantity = max(0, $stock->quantity);
        $stock->available_quantity = max(0, $stock->available_quantity);

        $stock->save();
    }

    /**
     * حجز كمية من المنتج
     */
    public function reserveQuantity(int $warehouseId, int $quantity): bool
    {
        $stock = $this->stock()->where('warehouse_id', $warehouseId)->first();

        if (!$stock || $stock->available_quantity < $quantity) {
            return false;
        }

        $stock->reserved_quantity += $quantity;
        $stock->available_quantity -= $quantity;
        $stock->save();

        return true;
    }

    /**
     * إلغاء حجز كمية من المنتج
     */
    public function unreserveQuantity(int $warehouseId, int $quantity): void
    {
        $stock = $this->stock()->where('warehouse_id', $warehouseId)->first();

        if ($stock) {
            $stock->reserved_quantity = max(0, $stock->reserved_quantity - $quantity);
            $stock->available_quantity = $stock->quantity - $stock->reserved_quantity;
            $stock->save();
        }
    }
}
