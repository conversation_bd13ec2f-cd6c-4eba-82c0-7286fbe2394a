<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Traits\BelongsToTenant;

class Product extends Model
{
    use BelongsToTenant;

    protected $fillable = [
        'tenant_id',
        'product_code',
        'barcode',
        'name',
        'name_en',
        'description',
        'product_type',
        'category',
        'brand',
        'model',
        'size',
        'color',
        'material',
        'origin_country',
        'unit',
        'purchase_price',
        'selling_price',
        'wholesale_price',
        'minimum_price',
        'weight',
        'dimensions',
        'minimum_stock',
        'maximum_stock',
        'reorder_level',
        'track_quantity',
        'is_active',
        'image',
        'images',
        'specifications',
        'notes',
        'main_supplier_id',
    ];

    protected $casts = [
        'purchase_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'wholesale_price' => 'decimal:2',
        'minimum_price' => 'decimal:2',
        'weight' => 'decimal:3',
        'minimum_stock' => 'integer',
        'maximum_stock' => 'integer',
        'reorder_level' => 'integer',
        'track_quantity' => 'boolean',
        'is_active' => 'boolean',
        'images' => 'array',
        'specifications' => 'array',
    ];

    /**
     * العلاقة مع المورد الرئيسي
     */
    public function mainSupplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'main_supplier_id');
    }

    /**
     * العلاقة مع مخزون المنتج
     */
    public function stock(): HasMany
    {
        return $this->hasMany(ProductStock::class);
    }

    /**
     * Scope للمنتجات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للمنتجات حسب النوع
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('product_type', $type);
    }

    /**
     * Scope للبحث في المنتجات
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('product_code', 'like', "%{$search}%")
              ->orWhere('barcode', 'like', "%{$search}%")
              ->orWhere('name_en', 'like', "%{$search}%");
        });
    }
}
