<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'code' => 'SAR',
                'name' => 'Saudi Riyal',
                'name_ar' => 'ريال سعودي',
                'symbol' => 'ر.س',
                'exchange_rate' => 1.0000,
                'is_base' => true,
                'is_active' => true,
            ],
            [
                'code' => 'USD',
                'name' => 'US Dollar',
                'name_ar' => 'دولار أمريكي',
                'symbol' => '$',
                'exchange_rate' => 3.7500,
                'is_base' => false,
                'is_active' => true,
            ],
            [
                'code' => 'EUR',
                'name' => 'Euro',
                'name_ar' => 'يورو',
                'symbol' => '€',
                'exchange_rate' => 4.1000,
                'is_base' => false,
                'is_active' => true,
            ],
            [
                'code' => 'AED',
                'name' => 'UAE Dirham',
                'name_ar' => 'درهم إماراتي',
                'symbol' => 'د.إ',
                'exchange_rate' => 1.0200,
                'is_base' => false,
                'is_active' => true,
            ],
        ];

        foreach ($currencies as $currency) {
            \DB::table('currencies')->insert(array_merge($currency, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
