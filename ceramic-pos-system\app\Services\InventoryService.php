<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Warehouse;
use App\Models\ProductStock;
use App\Models\StockMovement;
use App\Models\Tenant;
use Illuminate\Support\Facades\DB;

class InventoryService
{
    /**
     * إنشاء مخازن ومنتجات تجريبية للشركة الجديدة
     */
    public function createSampleInventory(Tenant $tenant): void
    {
        DB::beginTransaction();
        
        try {
            // إنشاء مخازن تجريبية
            $warehouses = $this->createSampleWarehouses($tenant);
            
            // إنشاء منتجات تجريبية
            $products = $this->createSampleProducts($tenant);
            
            // إنشاء مخزون أولي
            $this->createInitialStock($products, $warehouses);
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
    
    /**
     * إنشاء مخازن تجريبية
     */
    private function createSampleWarehouses(Tenant $tenant): array
    {
        $warehouses = [
            [
                'tenant_id' => $tenant->id,
                'warehouse_code' => 'WH001',
                'name' => 'المخزن الرئيسي',
                'description' => 'المخزن الرئيسي للشركة',
                'address' => 'شارع الصناعة، المنطقة الصناعية',
                'city' => 'القاهرة',
                'phone' => '02-12345678',
                'manager_name' => 'أحمد محمد',
                'manager_phone' => '01012345678',
                'is_main' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenant->id,
                'warehouse_code' => 'WH002',
                'name' => 'مخزن الفرع الأول',
                'description' => 'مخزن فرع الإسكندرية',
                'address' => 'شارع الجيش، الإسكندرية',
                'city' => 'الإسكندرية',
                'phone' => '03-11223344',
                'manager_name' => 'فاطمة أحمد',
                'manager_phone' => '01111223344',
                'is_main' => false,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenant->id,
                'warehouse_code' => 'WH003',
                'name' => 'مخزن المعرض',
                'description' => 'مخزن المعرض الرئيسي',
                'address' => 'شارع التحرير، وسط البلد',
                'city' => 'القاهرة',
                'phone' => '02-87654321',
                'manager_name' => 'محمد علي',
                'manager_phone' => '01087654321',
                'is_main' => false,
                'is_active' => true,
            ],
        ];
        
        $createdWarehouses = [];
        foreach ($warehouses as $warehouseData) {
            $createdWarehouses[] = Warehouse::create($warehouseData);
        }
        
        return $createdWarehouses;
    }
    
    /**
     * إنشاء منتجات تجريبية
     */
    private function createSampleProducts(Tenant $tenant): array
    {
        $products = [
            [
                'tenant_id' => $tenant->id,
                'product_code' => 'CER001',
                'barcode' => '1234567890123',
                'name' => 'سيراميك أرضيات 60x60',
                'name_en' => 'Floor Ceramic 60x60',
                'description' => 'سيراميك أرضيات عالي الجودة مقاس 60x60 سم',
                'product_type' => 'ceramic',
                'category' => 'أرضيات',
                'brand' => 'الجوهرة',
                'model' => 'JW-6060',
                'size' => '60x60',
                'color' => 'بيج',
                'material' => 'سيراميك',
                'origin_country' => 'مصر',
                'unit' => 'square_meter',
                'purchase_price' => 45.00,
                'selling_price' => 65.00,
                'wholesale_price' => 55.00,
                'minimum_price' => 50.00,
                'weight' => 1.5,
                'dimensions' => '60x60x0.8',
                'minimum_stock' => 100,
                'maximum_stock' => 1000,
                'reorder_level' => 200,
                'track_quantity' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenant->id,
                'product_code' => 'CER002',
                'barcode' => '1234567890124',
                'name' => 'سيراميك حوائط 30x60',
                'name_en' => 'Wall Ceramic 30x60',
                'description' => 'سيراميك حوائط لامع مقاس 30x60 سم',
                'product_type' => 'ceramic',
                'category' => 'حوائط',
                'brand' => 'الجوهرة',
                'model' => 'JW-3060',
                'size' => '30x60',
                'color' => 'أبيض',
                'material' => 'سيراميك',
                'origin_country' => 'مصر',
                'unit' => 'square_meter',
                'purchase_price' => 35.00,
                'selling_price' => 50.00,
                'wholesale_price' => 42.00,
                'minimum_price' => 38.00,
                'weight' => 1.2,
                'dimensions' => '30x60x0.8',
                'minimum_stock' => 150,
                'maximum_stock' => 1500,
                'reorder_level' => 300,
                'track_quantity' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenant->id,
                'product_code' => 'SAN001',
                'barcode' => '1234567890125',
                'name' => 'مرحاض إفرنجي',
                'name_en' => 'Western Toilet',
                'description' => 'مرحاض إفرنجي عالي الجودة مع خزان',
                'product_type' => 'sanitary',
                'category' => 'مراحيض',
                'brand' => 'إيديال ستاندرد',
                'model' => 'IS-WC001',
                'size' => 'قياس عادي',
                'color' => 'أبيض',
                'material' => 'بورسلين',
                'origin_country' => 'إيطاليا',
                'unit' => 'piece',
                'purchase_price' => 850.00,
                'selling_price' => 1200.00,
                'wholesale_price' => 1000.00,
                'minimum_price' => 950.00,
                'weight' => 25.0,
                'dimensions' => '65x35x75',
                'minimum_stock' => 10,
                'maximum_stock' => 100,
                'reorder_level' => 20,
                'track_quantity' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenant->id,
                'product_code' => 'SAN002',
                'barcode' => '1234567890126',
                'name' => 'حوض غسيل يدين',
                'name_en' => 'Hand Wash Basin',
                'description' => 'حوض غسيل يدين مع خلاط',
                'product_type' => 'sanitary',
                'category' => 'أحواض',
                'brand' => 'إيديال ستاندرد',
                'model' => 'IS-HB001',
                'size' => '50x40',
                'color' => 'أبيض',
                'material' => 'بورسلين',
                'origin_country' => 'إيطاليا',
                'unit' => 'piece',
                'purchase_price' => 320.00,
                'selling_price' => 450.00,
                'wholesale_price' => 380.00,
                'minimum_price' => 350.00,
                'weight' => 8.0,
                'dimensions' => '50x40x15',
                'minimum_stock' => 20,
                'maximum_stock' => 200,
                'reorder_level' => 40,
                'track_quantity' => true,
                'is_active' => true,
            ],
            [
                'tenant_id' => $tenant->id,
                'product_code' => 'ACC001',
                'barcode' => '1234567890127',
                'name' => 'خلاط حوض',
                'name_en' => 'Basin Mixer',
                'description' => 'خلاط حوض نحاس مطلي كروم',
                'product_type' => 'accessory',
                'category' => 'خلاطات',
                'brand' => 'جروهي',
                'model' => 'GR-BM001',
                'size' => 'قياس عادي',
                'color' => 'كروم',
                'material' => 'نحاس مطلي',
                'origin_country' => 'ألمانيا',
                'unit' => 'piece',
                'purchase_price' => 180.00,
                'selling_price' => 280.00,
                'wholesale_price' => 220.00,
                'minimum_price' => 200.00,
                'weight' => 1.5,
                'dimensions' => '15x10x20',
                'minimum_stock' => 50,
                'maximum_stock' => 500,
                'reorder_level' => 100,
                'track_quantity' => true,
                'is_active' => true,
            ],
        ];
        
        $createdProducts = [];
        foreach ($products as $productData) {
            $createdProducts[] = Product::create($productData);
        }
        
        return $createdProducts;
    }
    
    /**
     * إنشاء مخزون أولي
     */
    private function createInitialStock(array $products, array $warehouses): void
    {
        foreach ($products as $product) {
            foreach ($warehouses as $warehouse) {
                // توزيع المخزون بشكل عشوائي
                $quantity = 0;
                if ($warehouse->is_main) {
                    // المخزن الرئيسي يحصل على كمية أكبر
                    $quantity = rand(50, 200);
                } else {
                    // المخازن الفرعية تحصل على كمية أقل
                    $quantity = rand(10, 50);
                }
                
                ProductStock::create([
                    'product_id' => $product->id,
                    'warehouse_id' => $warehouse->id,
                    'quantity' => $quantity,
                    'reserved_quantity' => 0,
                    'available_quantity' => $quantity,
                    'average_cost' => $product->purchase_price,
                    'last_purchase_date' => now()->subDays(rand(1, 30)),
                ]);
                
                // إنشاء حركة مخزون أولية
                StockMovement::create([
                    'product_id' => $product->id,
                    'warehouse_id' => $warehouse->id,
                    'movement_type' => 'in',
                    'quantity' => $quantity,
                    'quantity_before' => 0,
                    'quantity_after' => $quantity,
                    'unit_cost' => $product->purchase_price,
                    'total_cost' => $quantity * $product->purchase_price,
                    'reference_type' => 'initial_stock',
                    'reference_number' => 'INIT-' . $product->product_code . '-' . $warehouse->warehouse_code,
                    'notes' => 'مخزون أولي',
                    'created_by' => 1, // المستخدم الأول
                    'created_at' => now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }
    
    /**
     * الحصول على إحصائيات المخزون
     */
    public function getInventoryStatistics(): array
    {
        return [
            'products' => [
                'total' => Product::count(),
                'active' => Product::active()->count(),
                'ceramic' => Product::ofType('ceramic')->count(),
                'sanitary' => Product::ofType('sanitary')->count(),
                'accessories' => Product::ofType('accessory')->count(),
                'low_stock' => Product::lowStock()->count(),
                'out_of_stock' => Product::outOfStock()->count(),
            ],
            'warehouses' => [
                'total' => Warehouse::count(),
                'active' => Warehouse::active()->count(),
                'main' => Warehouse::main()->count(),
            ],
            'stock' => [
                'total_value' => ProductStock::get()->sum('stock_value'),
                'total_quantity' => ProductStock::sum('quantity'),
                'available_quantity' => ProductStock::sum('available_quantity'),
                'reserved_quantity' => ProductStock::sum('reserved_quantity'),
            ],
            'movements' => [
                'total' => StockMovement::count(),
                'inbound' => StockMovement::inbound()->count(),
                'outbound' => StockMovement::outbound()->count(),
                'transfers' => StockMovement::transfers()->count(),
                'adjustments' => StockMovement::adjustments()->count(),
            ],
        ];
    }
    
    /**
     * تنبيهات المخزون
     */
    public function getStockAlerts(): array
    {
        return [
            'low_stock_products' => Product::lowStock()->with('stock')->get(),
            'out_of_stock_products' => Product::outOfStock()->with('stock')->get(),
            'reorder_products' => Product::whereNotNull('reorder_level')
                ->whereHas('stock', function($query) {
                    $query->whereRaw('quantity <= products.reorder_level');
                })->with('stock')->get(),
        ];
    }
}
