<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_features', function (Blueprint $table) {
            $table->id();
            $table->string('plan_key'); // basic, premium, enterprise
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->integer('max_users')->nullable(); // null = unlimited
            $table->integer('max_products')->nullable(); // null = unlimited
            $table->integer('max_customers')->nullable(); // null = unlimited
            $table->integer('max_invoices_per_month')->nullable(); // null = unlimited
            $table->json('modules'); // enabled modules
            $table->json('features'); // additional features
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->unique('plan_key');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_features');
    }
};
