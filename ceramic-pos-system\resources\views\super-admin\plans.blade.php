@extends('layouts.super-admin')

@section('title', 'إدارة الخطط والأسعار')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-tags me-2 text-info"></i>
                إدارة الخطط والأسعار
            </h2>
            <p class="text-muted mb-0">إدارة خطط الاشتراك والأسعار</p>
        </div>
    </div>

    <!-- Plans Grid -->
    <div class="row">
        @foreach($plans as $plan)
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 {{ $plan->is_featured ? 'border-primary' : '' }}">
                @if($plan->is_featured)
                    <div class="card-header bg-primary text-white text-center">
                        <i class="fas fa-star me-2"></i>
                        الأكثر شعبية
                    </div>
                @endif

                <div class="card-body">
                    <div class="text-center mb-3">
                        <h3 class="card-title">{{ $plan->name }}</h3>
                        <div class="display-4 fw-bold text-primary mb-2">
                            {{ number_format($plan->price) }}
                            <small class="fs-6 text-muted">ج.م/شهر</small>
                        </div>
                        <p class="text-muted">{{ $plan->description }}</p>
                    </div>

                    <!-- Limits -->
                    <div class="mb-3">
                        <h6 class="fw-bold text-secondary">الحدود:</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-users text-info me-2"></i>
                                المستخدمين: {{ $plan->max_users ? number_format($plan->max_users) : 'غير محدود' }}
                            </li>
                            <li><i class="fas fa-boxes text-warning me-2"></i>
                                المنتجات: {{ $plan->max_products ? number_format($plan->max_products) : 'غير محدود' }}
                            </li>
                            <li><i class="fas fa-address-book text-success me-2"></i>
                                العملاء: {{ $plan->max_customers ? number_format($plan->max_customers) : 'غير محدود' }}
                            </li>
                            <li><i class="fas fa-file-invoice text-primary me-2"></i>
                                الفواتير/شهر: {{ $plan->max_invoices_per_month ? number_format($plan->max_invoices_per_month) : 'غير محدود' }}
                            </li>
                        </ul>
                    </div>

                    <!-- Modules -->
                    <div class="mb-3">
                        <h6 class="fw-bold text-secondary">الوحدات:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            @foreach($plan->modules as $module)
                                <span class="badge bg-secondary small">{{ $availableModules[$module] ?? $module }}</span>
                            @endforeach
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="mb-3">
                        <h6 class="fw-bold text-secondary">المميزات:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            @foreach($plan->features as $feature)
                                <span class="badge bg-info small">{{ $availableFeatures[$feature] ?? $feature }}</span>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary"
                                onclick="editPlan({{ $plan->id }})">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الخطة
                        </button>

                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-info"
                                    onclick="viewPlanStats('{{ $plan->plan_key }}')">
                                <i class="fas fa-chart-bar me-1"></i>
                                الإحصائيات
                            </button>
                            <button class="btn btn-sm btn-outline-success"
                                    onclick="duplicatePlan({{ $plan->id }})">
                                <i class="fas fa-copy me-1"></i>
                                نسخ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Plan Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات الخطط
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <canvas id="plansChart" height="100"></canvas>
                        </div>
                        <div class="col-lg-4">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الخطة</th>
                                            <th>المشتركين</th>
                                            <th>الإيرادات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                            $planStats = [
                                                'basic' => ['subscribers' => 15, 'revenue' => 15 * 299],
                                                'premium' => ['subscribers' => 32, 'revenue' => 32 * 599],
                                                'enterprise' => ['subscribers' => 8, 'revenue' => 8 * 999]
                                            ];
                                        @endphp

                                        @foreach($planStats as $planKey => $stats)
                                        @php
                                            $plan = $plans->where('plan_key', $planKey)->first();
                                        @endphp
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">{{ $plan ? $plan->name : ucfirst($planKey) }}</span>
                                            </td>
                                            <td>{{ $stats['subscribers'] }}</td>
                                            <td>{{ number_format($stats['revenue']) }} ج.م</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th>الإجمالي</th>
                                            <th>{{ array_sum(array_column($planStats, 'subscribers')) }}</th>
                                            <th>{{ number_format(array_sum(array_column($planStats, 'revenue'))) }} ج.م</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pricing History -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        تاريخ تغييرات الأسعار
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الخطة</th>
                                    <th>السعر السابق</th>
                                    <th>السعر الجديد</th>
                                    <th>التغيير</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2025/06/30</td>
                                    <td><span class="badge bg-secondary">الخطة المتقدمة</span></td>
                                    <td>549 ج.م</td>
                                    <td>599 ج.م</td>
                                    <td><span class="text-success">+50 ج.م (+9.1%)</span></td>
                                    <td>Super Admin</td>
                                </tr>
                                <tr>
                                    <td>2025/06/25</td>
                                    <td><span class="badge bg-secondary">الخطة الأساسية</span></td>
                                    <td>249 ج.م</td>
                                    <td>299 ج.م</td>
                                    <td><span class="text-success">+50 ج.م (+20.1%)</span></td>
                                    <td>Super Admin</td>
                                </tr>
                                <tr>
                                    <td>2025/06/20</td>
                                    <td><span class="badge bg-secondary">الخطة المؤسسية</span></td>
                                    <td>899 ج.م</td>
                                    <td>999 ج.م</td>
                                    <td><span class="text-success">+100 ج.م (+11.1%)</span></td>
                                    <td>Super Admin</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Plan Modal -->
<div class="modal fade" id="editPlanModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الخطة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPlanForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم الخطة</label>
                                <input type="text" name="name" id="planName" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">السعر الشهري (ج.م)</label>
                                <input type="number" name="price" id="planPrice" class="form-control" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">وصف الخطة</label>
                        <textarea name="description" id="planDescription" class="form-control" rows="3"></textarea>
                    </div>

                    <!-- Limits -->
                    <h6 class="fw-bold text-primary mb-3">الحدود والقيود</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى للمستخدمين</label>
                                <input type="number" name="max_users" id="maxUsers" class="form-control" min="1">
                                <small class="text-muted">اتركه فارغاً للعدد غير المحدود</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى للمنتجات</label>
                                <input type="number" name="max_products" id="maxProducts" class="form-control" min="1">
                                <small class="text-muted">اتركه فارغاً للعدد غير المحدود</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى للعملاء</label>
                                <input type="number" name="max_customers" id="maxCustomers" class="form-control" min="1">
                                <small class="text-muted">اتركه فارغاً للعدد غير المحدود</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">الحد الأقصى للفواتير/شهر</label>
                                <input type="number" name="max_invoices_per_month" id="maxInvoices" class="form-control" min="1">
                                <small class="text-muted">اتركه فارغاً للعدد غير المحدود</small>
                            </div>
                        </div>
                    </div>

                    <!-- Modules -->
                    <h6 class="fw-bold text-primary mb-3">الوحدات المتاحة</h6>
                    <div class="row">
                        @foreach($availableModules as $key => $name)
                        <div class="col-md-4 col-lg-3">
                            <div class="form-check mb-2">
                                <input class="form-check-input module-checkbox" type="checkbox"
                                       name="modules[]" value="{{ $key }}" id="module_{{ $key }}">
                                <label class="form-check-label" for="module_{{ $key }}">
                                    {{ $name }}
                                </label>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Features -->
                    <h6 class="fw-bold text-primary mb-3 mt-4">المميزات الإضافية</h6>
                    <div class="row">
                        @foreach($availableFeatures as $key => $name)
                        <div class="col-md-4 col-lg-3">
                            <div class="form-check mb-2">
                                <input class="form-check-input feature-checkbox" type="checkbox"
                                       name="features[]" value="{{ $key }}" id="feature_{{ $key }}">
                                <label class="form-check-label" for="feature_{{ $key }}">
                                    {{ $name }}
                                </label>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="planActive">
                                <label class="form-check-label" for="planActive">
                                    خطة نشطة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_featured" id="planFeatured">
                                <label class="form-check-label" for="planFeatured">
                                    خطة مميزة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Plans Chart
const ctx = document.getElementById('plansChart').getContext('2d');
const plansChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['الخطة الأساسية', 'الخطة المتقدمة', 'الخطة المؤسسية'],
        datasets: [{
            data: [15, 32, 8],
            backgroundColor: [
                '#6c757d',
                '#0d6efd', 
                '#198754'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            title: {
                display: true,
                text: 'توزيع المشتركين حسب الخطة'
            }
        }
    }
});

function editPlan(planId) {
    // Get plan data
    const plans = @json($plans);
    const plan = plans.find(p => p.id === planId);

    if (!plan) return;

    // Set form action
    document.getElementById('editPlanForm').action = `/super-admin/plans/${planId}`;

    // Fill basic info
    document.getElementById('planName').value = plan.name;
    document.getElementById('planPrice').value = plan.price;
    document.getElementById('planDescription').value = plan.description || '';

    // Fill limits
    document.getElementById('maxUsers').value = plan.max_users || '';
    document.getElementById('maxProducts').value = plan.max_products || '';
    document.getElementById('maxCustomers').value = plan.max_customers || '';
    document.getElementById('maxInvoices').value = plan.max_invoices_per_month || '';

    // Clear all checkboxes first
    document.querySelectorAll('.module-checkbox').forEach(cb => cb.checked = false);
    document.querySelectorAll('.feature-checkbox').forEach(cb => cb.checked = false);

    // Check modules
    if (plan.modules) {
        plan.modules.forEach(module => {
            const checkbox = document.getElementById(`module_${module}`);
            if (checkbox) checkbox.checked = true;
        });
    }

    // Check features
    if (plan.features) {
        plan.features.forEach(feature => {
            const checkbox = document.getElementById(`feature_${feature}`);
            if (checkbox) checkbox.checked = true;
        });
    }

    // Set status checkboxes
    document.getElementById('planActive').checked = plan.is_active;
    document.getElementById('planFeatured').checked = plan.is_featured;

    const modal = new bootstrap.Modal(document.getElementById('editPlanModal'));
    modal.show();
}

function addFeature() {
    addFeatureInput('');
}

function addFeatureInput(value = '') {
    const container = document.getElementById('featuresContainer');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" value="${value}" placeholder="اكتب الميزة هنا">
        <button class="btn btn-outline-danger" type="button" onclick="this.parentElement.remove()">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function viewPlanStats(planKey) {
    alert(`إحصائيات الخطة: ${planKey}`);
}

function duplicatePlan(planKey) {
    if (confirm(`هل تريد نسخ الخطة؟`)) {
        alert(`تم نسخ الخطة بنجاح!`);
    }
}

document.getElementById('editPlanForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ التغييرات بنجاح!');
    bootstrap.Modal.getInstance(document.getElementById('editPlanModal')).hide();
});
</script>
@endpush
@endsection
