@extends('layouts.super-admin')

@section('title', 'إدارة الخطط والأسعار')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-tags me-2 text-info"></i>
                إدارة الخطط والأسعار
            </h2>
            <p class="text-muted mb-0">إدارة خطط الاشتراك والأسعار</p>
        </div>
    </div>

    <!-- Plans Grid -->
    <div class="row">
        @foreach($plans as $planKey => $plan)
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 {{ $planKey === 'premium' ? 'border-primary' : '' }}">
                @if($planKey === 'premium')
                    <div class="card-header bg-primary text-white text-center">
                        <i class="fas fa-star me-2"></i>
                        الأكثر شعبية
                    </div>
                @endif
                
                <div class="card-body text-center">
                    <h3 class="card-title">{{ $plan['name'] }}</h3>
                    <div class="display-4 fw-bold text-primary mb-3">
                        {{ number_format($plan['price']) }}
                        <small class="fs-6 text-muted">ج.م/شهر</small>
                    </div>
                    
                    <ul class="list-unstyled text-start">
                        @foreach($plan['features'] as $feature)
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            {{ $feature }}
                        </li>
                        @endforeach
                    </ul>
                </div>
                
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" 
                                onclick="editPlan('{{ $planKey }}', '{{ $plan['name'] }}', {{ $plan['price'] }})">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الخطة
                        </button>
                        
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-info" 
                                    onclick="viewPlanStats('{{ $planKey }}')">
                                <i class="fas fa-chart-bar me-1"></i>
                                الإحصائيات
                            </button>
                            <button class="btn btn-sm btn-outline-success" 
                                    onclick="duplicatePlan('{{ $planKey }}')">
                                <i class="fas fa-copy me-1"></i>
                                نسخ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Plan Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات الخطط
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <canvas id="plansChart" height="100"></canvas>
                        </div>
                        <div class="col-lg-4">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الخطة</th>
                                            <th>المشتركين</th>
                                            <th>الإيرادات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                            $planStats = [
                                                'basic' => ['subscribers' => 15, 'revenue' => 15 * 299],
                                                'premium' => ['subscribers' => 32, 'revenue' => 32 * 599],
                                                'enterprise' => ['subscribers' => 8, 'revenue' => 8 * 999]
                                            ];
                                        @endphp
                                        
                                        @foreach($planStats as $planKey => $stats)
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">{{ $plans[$planKey]['name'] }}</span>
                                            </td>
                                            <td>{{ $stats['subscribers'] }}</td>
                                            <td>{{ number_format($stats['revenue']) }} ج.م</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th>الإجمالي</th>
                                            <th>{{ array_sum(array_column($planStats, 'subscribers')) }}</th>
                                            <th>{{ number_format(array_sum(array_column($planStats, 'revenue'))) }} ج.م</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pricing History -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        تاريخ تغييرات الأسعار
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الخطة</th>
                                    <th>السعر السابق</th>
                                    <th>السعر الجديد</th>
                                    <th>التغيير</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2025/06/30</td>
                                    <td><span class="badge bg-secondary">الخطة المتقدمة</span></td>
                                    <td>549 ج.م</td>
                                    <td>599 ج.م</td>
                                    <td><span class="text-success">+50 ج.م (+9.1%)</span></td>
                                    <td>Super Admin</td>
                                </tr>
                                <tr>
                                    <td>2025/06/25</td>
                                    <td><span class="badge bg-secondary">الخطة الأساسية</span></td>
                                    <td>249 ج.م</td>
                                    <td>299 ج.م</td>
                                    <td><span class="text-success">+50 ج.م (+20.1%)</span></td>
                                    <td>Super Admin</td>
                                </tr>
                                <tr>
                                    <td>2025/06/20</td>
                                    <td><span class="badge bg-secondary">الخطة المؤسسية</span></td>
                                    <td>899 ج.م</td>
                                    <td>999 ج.م</td>
                                    <td><span class="text-success">+100 ج.م (+11.1%)</span></td>
                                    <td>Super Admin</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Plan Modal -->
<div class="modal fade" id="editPlanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الخطة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPlanForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم الخطة</label>
                                <input type="text" id="planName" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">السعر الشهري (ج.م)</label>
                                <input type="number" id="planPrice" class="form-control" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف الخطة</label>
                        <textarea id="planDescription" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المميزات</label>
                        <div id="featuresContainer">
                            <!-- Features will be loaded here -->
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addFeature()">
                            <i class="fas fa-plus me-1"></i>
                            إضافة ميزة
                        </button>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="planActive">
                                <label class="form-check-label" for="planActive">
                                    خطة نشطة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="planFeatured">
                                <label class="form-check-label" for="planFeatured">
                                    خطة مميزة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Plans Chart
const ctx = document.getElementById('plansChart').getContext('2d');
const plansChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['الخطة الأساسية', 'الخطة المتقدمة', 'الخطة المؤسسية'],
        datasets: [{
            data: [15, 32, 8],
            backgroundColor: [
                '#6c757d',
                '#0d6efd', 
                '#198754'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            title: {
                display: true,
                text: 'توزيع المشتركين حسب الخطة'
            }
        }
    }
});

function editPlan(planKey, planName, planPrice) {
    document.getElementById('planName').value = planName;
    document.getElementById('planPrice').value = planPrice;
    
    // Load features
    const features = @json($plans);
    const featuresContainer = document.getElementById('featuresContainer');
    featuresContainer.innerHTML = '';
    
    features[planKey].features.forEach(feature => {
        addFeatureInput(feature);
    });
    
    const modal = new bootstrap.Modal(document.getElementById('editPlanModal'));
    modal.show();
}

function addFeature() {
    addFeatureInput('');
}

function addFeatureInput(value = '') {
    const container = document.getElementById('featuresContainer');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" value="${value}" placeholder="اكتب الميزة هنا">
        <button class="btn btn-outline-danger" type="button" onclick="this.parentElement.remove()">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function viewPlanStats(planKey) {
    alert(`إحصائيات الخطة: ${planKey}`);
}

function duplicatePlan(planKey) {
    if (confirm(`هل تريد نسخ الخطة؟`)) {
        alert(`تم نسخ الخطة بنجاح!`);
    }
}

document.getElementById('editPlanForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ التغييرات بنجاح!');
    bootstrap.Modal.getInstance(document.getElementById('editPlanModal')).hide();
});
</script>
@endpush
@endsection
