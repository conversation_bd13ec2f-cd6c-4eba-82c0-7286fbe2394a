@extends('layouts.app')

@section('title', 'إدارة الفواتير')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-file-invoice me-2 text-primary"></i>
                إدارة الفواتير
            </h2>
            <p class="text-muted mb-0">{{ $tenant->company_name }}</p>
        </div>
        <div>
            <a href="{{ route('invoices.create', ['tenant' => $tenant->id]) }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء فاتورة جديدة
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <input type="hidden" name="tenant" value="{{ $tenant->id }}">
                
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="رقم الفاتورة أو اسم العميل" 
                           value="{{ request('search') }}">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">النوع</label>
                    <select name="type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="sale" {{ request('type') == 'sale' ? 'selected' : '' }}>مبيعات</option>
                        <option value="purchase" {{ request('type') == 'purchase' ? 'selected' : '' }}>مشتريات</option>
                        <option value="return_sale" {{ request('type') == 'return_sale' ? 'selected' : '' }}>مرتجع مبيعات</option>
                        <option value="return_purchase" {{ request('type') == 'return_purchase' ? 'selected' : '' }}>مرتجع مشتريات</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>مسودة</option>
                        <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>مؤكدة</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتملة</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغية</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                </div>
                
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ $invoices->where('type', 'sale')->count() }}</h3>
                    <p class="text-muted mb-0">فواتير المبيعات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">{{ number_format($invoices->where('type', 'sale')->sum('total_amount'), 2) }}</h3>
                    <p class="text-muted mb-0">إجمالي المبيعات (ج.م)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">{{ $invoices->where('payment_status', 'pending')->count() }}</h3>
                    <p class="text-muted mb-0">فواتير معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">{{ $invoices->where('payment_status', 'paid')->count() }}</h3>
                    <p class="text-muted mb-0">فواتير مدفوعة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="card">
        <div class="card-body">
            @if($invoices->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>النوع</th>
                                <th>التاريخ</th>
                                <th>الإجمالي</th>
                                <th>حالة الدفع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($invoices as $invoice)
                            <tr>
                                <td>
                                    <strong>{{ $invoice->invoice_number }}</strong>
                                </td>
                                <td>
                                    @if($invoice->customer)
                                        {{ $invoice->customer->name }}
                                    @else
                                        <span class="text-muted">عميل نقدي</span>
                                    @endif
                                </td>
                                <td>
                                    @switch($invoice->type)
                                        @case('sale')
                                            <span class="badge bg-success">مبيعات</span>
                                            @break
                                        @case('purchase')
                                            <span class="badge bg-primary">مشتريات</span>
                                            @break
                                        @case('return_sale')
                                            <span class="badge bg-warning">مرتجع مبيعات</span>
                                            @break
                                        @case('return_purchase')
                                            <span class="badge bg-info">مرتجع مشتريات</span>
                                            @break
                                    @endswitch
                                </td>
                                <td>{{ $invoice->invoice_date->format('Y/m/d') }}</td>
                                <td>{{ number_format($invoice->total_amount, 2) }} ج.م</td>
                                <td>
                                    @switch($invoice->payment_status)
                                        @case('paid')
                                            <span class="badge bg-success">مدفوعة</span>
                                            @break
                                        @case('partial')
                                            <span class="badge bg-warning">جزئية</span>
                                            @break
                                        @case('pending')
                                            <span class="badge bg-danger">معلقة</span>
                                            @break
                                    @endswitch
                                </td>
                                <td>
                                    @switch($invoice->status)
                                        @case('draft')
                                            <span class="badge bg-secondary">مسودة</span>
                                            @break
                                        @case('confirmed')
                                            <span class="badge bg-primary">مؤكدة</span>
                                            @break
                                        @case('completed')
                                            <span class="badge bg-success">مكتملة</span>
                                            @break
                                        @case('cancelled')
                                            <span class="badge bg-danger">ملغية</span>
                                            @break
                                    @endswitch
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('invoices.show', ['invoice' => $invoice->id, 'tenant' => $tenant->id]) }}" 
                                           class="btn btn-outline-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('invoices.print', ['invoice' => $invoice->id, 'tenant' => $tenant->id]) }}" 
                                           class="btn btn-outline-primary" title="طباعة" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        @if($invoice->status === 'draft')
                                            <button class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        @endif
                                        @if($invoice->status !== 'completed')
                                            <button class="btn btn-outline-danger" title="إلغاء">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $invoices->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد فواتير</h5>
                    <p class="text-muted">ابدأ بإنشاء أول فاتورة</p>
                    <a href="{{ route('invoices.create', ['tenant' => $tenant->id]) }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء أول فاتورة
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
