@extends('layouts.app')

@section('title', 'إضافة منتج جديد')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1">
                <i class="fas fa-plus me-2 text-primary"></i>
                إضافة منتج جديد
            </h2>
            <p class="text-muted mb-0">{{ $tenant->company_name }}</p>
        </div>
        <div>
            <a href="{{ route('inventory.products.index', ['tenant' => $tenant->id]) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ route('inventory.products.store') }}" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="tenant" value="{{ $tenant->id }}">
                        
                        <div class="row">
                            <!-- اسم المنتج -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- كود المنتج -->
                            <div class="col-md-6 mb-3">
                                <label for="code" class="form-label">كود المنتج *</label>
                                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                       id="code" name="code" value="{{ old('code') }}" required>
                                @error('code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- الباركود -->
                            <div class="col-md-6 mb-3">
                                <label for="barcode" class="form-label">الباركود</label>
                                <input type="text" class="form-control @error('barcode') is-invalid @enderror" 
                                       id="barcode" name="barcode" value="{{ old('barcode') }}">
                                @error('barcode')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- الفئة -->
                            <div class="col-md-6 mb-3">
                                <label for="category_id" class="form-label">الفئة *</label>
                                <select class="form-select @error('category_id') is-invalid @enderror" 
                                        id="category_id" name="category_id" required>
                                    <option value="">اختر الفئة</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" 
                                            {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- وحدة القياس -->
                            <div class="col-md-4 mb-3">
                                <label for="unit" class="form-label">وحدة القياس *</label>
                                <select class="form-select @error('unit') is-invalid @enderror" 
                                        id="unit" name="unit" required>
                                    <option value="">اختر الوحدة</option>
                                    <option value="قطعة" {{ old('unit') == 'قطعة' ? 'selected' : '' }}>قطعة</option>
                                    <option value="متر" {{ old('unit') == 'متر' ? 'selected' : '' }}>متر</option>
                                    <option value="متر مربع" {{ old('unit') == 'متر مربع' ? 'selected' : '' }}>متر مربع</option>
                                    <option value="كيلو" {{ old('unit') == 'كيلو' ? 'selected' : '' }}>كيلو</option>
                                    <option value="صندوق" {{ old('unit') == 'صندوق' ? 'selected' : '' }}>صندوق</option>
                                </select>
                                @error('unit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- سعر التكلفة -->
                            <div class="col-md-4 mb-3">
                                <label for="cost_price" class="form-label">سعر التكلفة *</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" 
                                           class="form-control @error('cost_price') is-invalid @enderror" 
                                           id="cost_price" name="cost_price" value="{{ old('cost_price') }}" required>
                                    <span class="input-group-text">ج.م</span>
                                </div>
                                @error('cost_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- سعر البيع -->
                            <div class="col-md-4 mb-3">
                                <label for="selling_price" class="form-label">سعر البيع *</label>
                                <div class="input-group">
                                    <input type="number" step="0.01" min="0" 
                                           class="form-control @error('selling_price') is-invalid @enderror" 
                                           id="selling_price" name="selling_price" value="{{ old('selling_price') }}" required>
                                    <span class="input-group-text">ج.م</span>
                                </div>
                                @error('selling_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- الحد الأدنى للمخزون -->
                            <div class="col-md-6 mb-3">
                                <label for="min_stock" class="form-label">الحد الأدنى للمخزون *</label>
                                <input type="number" min="0" 
                                       class="form-control @error('min_stock') is-invalid @enderror" 
                                       id="min_stock" name="min_stock" value="{{ old('min_stock', 5) }}" required>
                                @error('min_stock')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- المخزون الحالي -->
                            <div class="col-md-6 mb-3">
                                <label for="current_stock" class="form-label">المخزون الحالي *</label>
                                <input type="number" min="0" 
                                       class="form-control @error('current_stock') is-invalid @enderror" 
                                       id="current_stock" name="current_stock" value="{{ old('current_stock', 0) }}" required>
                                @error('current_stock')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- الوصف -->
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- صورة المنتج -->
                        <div class="mb-4">
                            <label for="image" class="form-label">صورة المنتج</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            <div class="form-text">الحد الأقصى: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- الحالة -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    منتج نشط
                                </label>
                            </div>
                        </div>
                        
                        <!-- أزرار الحفظ -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ المنتج
                            </button>
                            <button type="submit" name="save_and_new" value="1" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                حفظ وإضافة آخر
                            </button>
                            <a href="{{ route('inventory.products.index', ['tenant' => $tenant->id]) }}" class="btn btn-outline-secondary">
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح
                    </h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            استخدم أكواد منتجات واضحة ومميزة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            أضف الباركود لتسهيل عملية البيع
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            حدد الحد الأدنى للمخزون لتجنب النفاد
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            أضف صورة واضحة للمنتج
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-calculator me-2"></i>
                        حاسبة الربح
                    </h6>
                    <div id="profit-calculator">
                        <div class="mb-2">
                            <small class="text-muted">هامش الربح:</small>
                            <div class="fw-bold" id="profit-margin">0%</div>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">الربح لكل وحدة:</small>
                            <div class="fw-bold" id="profit-per-unit">0 ج.م</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// حاسبة الربح
function calculateProfit() {
    const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (costPrice > 0 && sellingPrice > 0) {
        const profit = sellingPrice - costPrice;
        const margin = ((profit / sellingPrice) * 100).toFixed(2);
        
        document.getElementById('profit-margin').textContent = margin + '%';
        document.getElementById('profit-per-unit').textContent = profit.toFixed(2) + ' ج.م';
    } else {
        document.getElementById('profit-margin').textContent = '0%';
        document.getElementById('profit-per-unit').textContent = '0 ج.م';
    }
}

// ربط الأحداث
document.getElementById('cost_price').addEventListener('input', calculateProfit);
document.getElementById('selling_price').addEventListener('input', calculateProfit);

// توليد كود تلقائي
document.getElementById('name').addEventListener('blur', function() {
    const name = this.value;
    const codeField = document.getElementById('code');
    
    if (name && !codeField.value) {
        // توليد كود بسيط من الاسم
        const code = name.substring(0, 3).toUpperCase() + Math.floor(Math.random() * 1000);
        codeField.value = code;
    }
});
</script>
@endpush
@endsection
