<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إدارة المخزون - نظام إدارة السيراميك</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
        .navbar-custom { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .module-header { background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%); color: white; border-radius: 15px; padding: 30px; margin-bottom: 30px; }
        .feature-card { background: white; border-radius: 15px; padding: 30px; margin-bottom: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/dashboard?tenant=<?php echo e($tenant_id); ?>">
                <i class="fas fa-cube me-2"></i>Ceramic POS
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">إدارة المخزون | <?php echo e(auth()->user()->name); ?></span>
                <a href="/dashboard?tenant=<?php echo e($tenant_id); ?>" class="btn btn-outline-light btn-sm me-2">
                    <i class="fas fa-home me-1"></i>لوحة التحكم
                </a>
                <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="module-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-3"><i class="fas fa-warehouse me-2"></i>إدارة المخزون</h1>
                    <p class="lead mb-0">تتبع دقيق للمخزون والمنتجات مع تنبيهات النفاد</p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-boxes" style="font-size: 4rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="feature-card text-center">
                    <i class="fas fa-plus fa-2x text-primary mb-3"></i>
                    <h5>منتج جديد</h5>
                    <button class="btn btn-primary w-100">إضافة منتج</button>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="feature-card text-center">
                    <i class="fas fa-boxes fa-2x text-info mb-3"></i>
                    <h5>إجمالي المنتجات</h5>
                    <div class="h4 text-info">150</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="feature-card text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                    <h5>منتجات منخفضة</h5>
                    <div class="h4 text-warning">8</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="feature-card text-center">
                    <i class="fas fa-money-bill fa-2x text-success mb-3"></i>
                    <h5>قيمة المخزون</h5>
                    <div class="h4 text-success">285,500 ج.م</div>
                </div>
            </div>
        </div>

        <div class="alert alert-info">
            <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i>وحدة إدارة المخزون</h5>
            <p class="mb-0">هذه الوحدة قيد التطوير. ستتضمن إدارة كاملة للمخزون مع تتبع الحركات والتنبيهات.<br><strong>الشركة الحالية:</strong> <?php echo e($tenant_id); ?></p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH D:\Work\ceramic pos\ceramic-pos-system\resources\views/modules/inventory.blade.php ENDPATH**/ ?>